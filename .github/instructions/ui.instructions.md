---
applyTo: "**"
---

# shadcn/ui LLM UI Development Instructions (2025)

_Last updated: July 2025_

- Before making any changes to the UI or fetching the docs, always refer to the components/ui directory to see if the component already exists. If it does, use it instead of building a custom component. Make sure you have a good understanding of the component's props and behavior before using it. Always use the class names and props as specified in the component file. Use all the advanced props that are available to you. Dont directly modify the component files unless you are making a general improvement to the component that will be useful across the app or told to do so.
- Only use the fetch tool to look up the latest component usage if the component is not already in the codebase. This will give you the latest component usage, install name, and best practices directly from the official shadcn/ui documentation: https://ui.shadcn.com/docs/components
- Do not rely on what you think you know about shadcn/ui components, as they are frequently updated and improved. Your training data is outdated.
- For any shadcn/ui component, CLI command, or usage pattern, fetch the relevant page from the docs and follow the instructions there.

**Core Principles:**

- shadcn/ui components are open code: you are expected to read, modify, and extend them directly.
- Use the CLI (`bunx shadcn@latest add <component>`) to add or update components.
- Always import from the local `@/components/ui/<component>` path.
- Follow accessibility and composition best practices as described in the docs.
- Always use the `cn()` utility for className merging.
- Always make designs mobile-first and responsive.

**Summary:**

> For all shadcn/ui work, always use the fetch tool to look up the latest component documentation and usage from https://ui.shadcn.com/docs/components. Do not rely on static instructions.
