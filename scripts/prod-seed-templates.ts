/**
 * Production-ready templates for seeding the database in prod environments
 * - 5 categories: contact, feedback, registration, application, survey
 * - 4 templates per category (20 total)
 * - Each template uses realistic, comprehensive fields and settings
 * - Field and option ids use nanoid()
 */

import { nanoid } from "nanoid";

export const prodSeedTemplates = [
  // CONTACT (4)
  {
    name: "General Contact (Departments)",
    description:
      "Contact form with department routing, subject, priority and attachment",
    category: "contact",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Support Team",
    respondantEmailSubject: "We received your message",
    respondantEmailMessageHTML:
      "<p>Thanks for reaching out. A member of our team will respond shortly.</p>",
    submitButtonText: "Send Message",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#0ea5e9",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#0ea5e9",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "This form is currently closed.",
    closeMessageTitle: "Form Closed",
    headerDescription: "Choose a department to route your message.",
    headerImage: "",
    headerTitle: "Contact Us",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Message received!",
    tpMessage:
      "Thanks for contacting us. We'll reply to your email as soon as possible.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#0ea5e9",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    ipBlacklist: "",
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Department",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "Sales" },
          { id: nanoid(), value: "Support" },
          { id: nanoid(), value: "Billing" },
          { id: nanoid(), value: "Partnerships" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Subject",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Priority",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Low" },
          { id: nanoid(), value: "Normal" },
          { id: nanoid(), value: "High" },
          { id: nanoid(), value: "Urgent" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Message",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Attachment (optional)",
        type: "file",
        subtype: "file_upload",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Request a Quote",
    description:
      "Sales-focused contact form with company details, budget and timeline",
    category: "contact",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Sales Team",
    respondantEmailSubject: "We received your quote request",
    respondantEmailMessageHTML:
      "<p>Thanks! Our team will review your request and get back with a proposal.</p>",
    submitButtonText: "Request Quote",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#0284c7",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#0284c7",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "This form is currently closed.",
    closeMessageTitle: "Form Closed",
    headerDescription: "Tell us about your project and budget range.",
    headerImage: "",
    headerTitle: "Request a Quote",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks for your request!",
    tpMessage: "A sales specialist will follow up via email.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#0284c7",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Business Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Company",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Website",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Services Interested In",
        type: "select",
        subtype: "multiple_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Implementation" },
          { id: nanoid(), value: "Consulting" },
          { id: nanoid(), value: "Custom Development" },
          { id: nanoid(), value: "Training" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Budget Range",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "<$5,000" },
          { id: nanoid(), value: "$5,000 - $20,000" },
          { id: nanoid(), value: "$20,000 - $50,000" },
          { id: nanoid(), value: ">$50,000" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Timeline",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "ASAP" },
          { id: nanoid(), value: "1-3 months" },
          { id: nanoid(), value: "3-6 months" },
          { id: nanoid(), value: "6+ months" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Project Details",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },
  {
    name: "Technical Support (Ticket)",
    description:
      "Support ticket form with issue type, severity, order number and attachments",
    category: "contact",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Support",
    respondantEmailSubject: "Support ticket created",
    respondantEmailMessageHTML:
      "<p>Your ticket has been created. We'll be in touch shortly.</p>",
    submitButtonText: "Submit Ticket",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#22c55e",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#22c55e",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Support is currently unavailable.",
    closeMessageTitle: "Form Closed",
    headerDescription: "Tell us about your issue.",
    headerImage: "",
    headerTitle: "Create a Support Ticket",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Ticket received",
    tpMessage: "Thanks! We'll follow up by email.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#22c55e",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Order/Account Number",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Issue Type",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "Technical Bug" },
          { id: nanoid(), value: "Billing Problem" },
          { id: nanoid(), value: "Account Access" },
          { id: nanoid(), value: "Other" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Severity",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Low" },
          { id: nanoid(), value: "Medium" },
          { id: nanoid(), value: "High" },
          { id: nanoid(), value: "Critical" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Steps to Reproduce",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Attachments",
        type: "file",
        subtype: "file_upload",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Partnership / Media Inquiry",
    description:
      "Media/PR and partnership inquiry with organization details and deadline",
    category: "contact",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Communications",
    respondantEmailSubject: "Thanks for your inquiry",
    respondantEmailMessageHTML:
      "<p>Thanks for reaching out. Our communications team will respond soon.</p>",
    submitButtonText: "Submit Inquiry",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#6366f1",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#6366f1",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "This form is currently closed.",
    closeMessageTitle: "Form Closed",
    headerDescription: "Tell us about your opportunity.",
    headerImage: "",
    headerTitle: "Partnership / Media Inquiry",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Inquiry received",
    tpMessage: "We'll get back to you shortly.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#6366f1",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Contact Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Business Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Organization / Publication",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Topic",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Link (if applicable)",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Deadline",
        type: "date",
        subtype: "date",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Details",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },

  // FEEDBACK (4)
  {
    name: "Product Feedback (CSAT + Comments)",
    description: "CSAT-focused product feedback with optional comments",
    category: "feedback",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Customer Experience",
    respondantEmailSubject: "Thanks for your feedback",
    respondantEmailMessageHTML:
      "<p>We appreciate your feedback and will use it to improve the product.</p>",
    submitButtonText: "Submit Feedback",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#8b5cf6",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#8b5cf6",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Feedback form is closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Rate your satisfaction and share any feedback.",
    headerImage: "",
    headerTitle: "Product Feedback",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thank you!",
    tpMessage: "Your feedback has been received.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#8b5cf6",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Overall, how satisfied are you?",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 5,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Which areas need improvement? (Select all that apply)",
        type: "select",
        subtype: "multiple_choice",
        required: false,
        options: [
          { id: nanoid(), value: "Performance" },
          { id: nanoid(), value: "Reliability" },
          { id: nanoid(), value: "Ease of use" },
          { id: nanoid(), value: "Documentation" },
          { id: nanoid(), value: "Support" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What did you like most?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Any other comments?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Website UX Feedback",
    description: "Gather feedback on website usability and device context",
    category: "feedback",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Web Team",
    respondantEmailSubject: "Thanks for your feedback",
    respondantEmailMessageHTML:
      "<p>Thanks for helping us improve the website experience.</p>",
    submitButtonText: "Submit",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#06b6d4",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#06b6d4",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Feedback is closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Tell us about your browsing experience.",
    headerImage: "",
    headerTitle: "Website Feedback",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "We appreciate your insights.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#06b6d4",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Which page were you on?",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "How easy was it to find what you needed?",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Very easy" },
          { id: nanoid(), value: "Easy" },
          { id: nanoid(), value: "Neutral" },
          { id: nanoid(), value: "Difficult" },
          { id: nanoid(), value: "Very difficult" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Device used",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "Mobile" },
          { id: nanoid(), value: "Tablet" },
          { id: nanoid(), value: "Desktop" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What can we improve?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Post-purchase Feedback",
    description: "Feedback after purchase: CSAT, delivery and product quality",
    category: "feedback",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Customer Experience",
    respondantEmailSubject: "Thanks for your purchase!",
    respondantEmailMessageHTML:
      "<p>We'd love to hear how everything went with your order.</p>",
    submitButtonText: "Submit Feedback",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#f59e0b",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#f59e0b",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Feedback is closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Tell us about your recent order.",
    headerImage: "",
    headerTitle: "Post-purchase Feedback",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "We appreciate your feedback.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#f59e0b",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Order Number",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Overall satisfaction",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 5,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Delivery experience",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Excellent" },
          { id: nanoid(), value: "Good" },
          { id: nanoid(), value: "Average" },
          { id: nanoid(), value: "Poor" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Product quality comments",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Feature Request",
    description: "Collect structured feature requests and impact",
    category: "feedback",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Product Team",
    respondantEmailSubject: "Thanks for your request",
    respondantEmailMessageHTML:
      "<p>We track every request and use them to guide our roadmap.</p>",
    submitButtonText: "Submit Request",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#10b981",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#10b981",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "This form is closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Propose a feature and describe the impact.",
    headerImage: "",
    headerTitle: "Feature Request",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "Your request has been recorded.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#10b981",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Your email (for follow-up)",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Feature title",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Impact",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "Nice to have" },
          { id: nanoid(), value: "Important" },
          { id: nanoid(), value: "Critical" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Description / Use case",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },

  // REGISTRATION (4)
  {
    name: "Conference Registration (Tracks + T-Shirt)",
    description:
      "Conference registration with tracks, dietary and t-shirt size",
    category: "registration",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Conference Team",
    respondantEmailSubject: "Registration confirmed",
    respondantEmailMessageHTML:
      "<p>You're in! We'll send your ticket and updates soon.</p>",
    submitButtonText: "Register",
    limitResponses: true,
    maxResponses: 5000,
    isClosed: false,
    autoCloseEnabled: true,
    autoCloseDate: new Date("2025-12-31"),
    autoCloseTime: "23:59",
    autoCloseTimezone: "America/New_York",
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#ef4444",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#ef4444",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Registration has ended.",
    closeMessageTitle: "Registration Closed",
    headerDescription: "Select your tracks and provide preferences.",
    headerImage: "",
    headerTitle: "Conference Registration",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "You're registered!",
    tpMessage: "We've sent a confirmation email.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#ef4444",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Company",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Attendance Type",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "In-person" },
          { id: nanoid(), value: "Virtual" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Tracks (select up to 3)",
        type: "select",
        subtype: "multiple_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Engineering" },
          { id: nanoid(), value: "Product" },
          { id: nanoid(), value: "Design" },
          { id: nanoid(), value: "Growth" },
          { id: nanoid(), value: "Leadership" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Dietary Restrictions",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "T-Shirt Size",
        type: "select",
        subtype: "dropdown",
        required: false,
        options: [
          { id: nanoid(), value: "XS" },
          { id: nanoid(), value: "S" },
          { id: nanoid(), value: "M" },
          { id: nanoid(), value: "L" },
          { id: nanoid(), value: "XL" },
          { id: nanoid(), value: "XXL" },
        ],
        showDescription: false,
      },
    ],
  },
  {
    name: "Webinar Registration",
    description: "Webinar sign-up with timezone and reminders",
    category: "registration",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Webinar Team",
    respondantEmailSubject: "Webinar registration confirmed",
    respondantEmailMessageHTML:
      "<p>We'll send the join link and reminders to your email.</p>",
    submitButtonText: "Register",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#3b82f6",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#3b82f6",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Registration closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Join our upcoming webinar.",
    headerImage: "",
    headerTitle: "Webinar Registration",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "You're registered!",
    tpMessage: "We'll send reminders to your email.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#3b82f6",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Company",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Timezone",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "UTC-08 (PT)" },
          { id: nanoid(), value: "UTC-05 (ET)" },
          { id: nanoid(), value: "UTC+00 (GMT)" },
          { id: nanoid(), value: "UTC+01 (CET)" },
          { id: nanoid(), value: "UTC+05:30 (IST)" },
          { id: nanoid(), value: "UTC+08 (CST)" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Reminders",
        type: "select",
        subtype: "multiple_choice",
        required: false,
        options: [
          { id: nanoid(), value: "1 day before" },
          { id: nanoid(), value: "1 hour before" },
          { id: nanoid(), value: "10 minutes before" },
        ],
        showDescription: false,
      },
    ],
  },
  {
    name: "Course Enrollment",
    description: "Course signup with schedule, experience and goals",
    category: "registration",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Academy",
    respondantEmailSubject: "Enrollment received",
    respondantEmailMessageHTML:
      "<p>Thanks for enrolling! We'll send next steps shortly.</p>",
    submitButtonText: "Enroll",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#f97316",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#f97316",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Enrollment closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Provide your details and preferences.",
    headerImage: "",
    headerTitle: "Course Enrollment",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "We'll confirm your spot via email.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#f97316",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Preferred Schedule",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "Weekdays" },
          { id: nanoid(), value: "Evenings" },
          { id: nanoid(), value: "Weekends" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Experience Level",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Beginner" },
          { id: nanoid(), value: "Intermediate" },
          { id: nanoid(), value: "Advanced" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Learning Goals",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Volunteer Sign-up",
    description: "Volunteer registration with availability and skills",
    category: "registration",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Volunteer Team",
    respondantEmailSubject: "Thanks for signing up!",
    respondantEmailMessageHTML:
      "<p>We'll match you with upcoming opportunities.</p>",
    submitButtonText: "Sign Up",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#10b981",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#10b981",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Signup closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Share your availability and skills.",
    headerImage: "",
    headerTitle: "Volunteer Sign-up",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "We'll be in touch soon.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#10b981",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Phone",
        type: "text",
        subtype: "phone",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Availability (Select all that apply)",
        type: "select",
        subtype: "multiple_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Weekdays" },
          { id: nanoid(), value: "Evenings" },
          { id: nanoid(), value: "Weekends" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Skills",
        type: "select",
        subtype: "multiple_choice",
        required: false,
        options: [
          { id: nanoid(), value: "Event support" },
          { id: nanoid(), value: "Fundraising" },
          { id: nanoid(), value: "Logistics" },
          { id: nanoid(), value: "Mentoring" },
          { id: nanoid(), value: "Administrative" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Emergency Contact (optional)",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
    ],
  },

  // APPLICATION (4)
  {
    name: "Job Application",
    description: "Job application with portfolio, LinkedIn and start date",
    category: "application",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "HR Team",
    respondantEmailSubject: "Application received",
    respondantEmailMessageHTML:
      "<p>Thanks for applying! Our team will review your application.</p>",
    submitButtonText: "Submit Application",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#2563eb",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#2563eb",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Applications closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Tell us about your experience.",
    headerImage: "",
    headerTitle: "Job Application",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "We'll follow up by email.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#2563eb",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Phone",
        type: "text",
        subtype: "phone",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Position",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "LinkedIn URL",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Portfolio URL",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Work Preference",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "Remote" },
          { id: nanoid(), value: "Onsite" },
          { id: nanoid(), value: "Hybrid" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Resume/CV",
        type: "file",
        subtype: "file_upload",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Earliest Start Date",
        type: "date",
        subtype: "date",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Cover Letter",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Internship Application",
    description: "Intern application with school, graduation and GPA",
    category: "application",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "HR Team",
    respondantEmailSubject: "Application received",
    respondantEmailMessageHTML:
      "<p>Thanks for applying! We'll be in touch after review.</p>",
    submitButtonText: "Submit Application",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#9333ea",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#9333ea",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Applications closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Provide your academic details.",
    headerImage: "",
    headerTitle: "Internship Application",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "We'll contact you if selected.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#9333ea",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "University/College",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Graduation Date",
        type: "date",
        subtype: "date",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "GPA",
        type: "number",
        subtype: "number",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Resume/CV",
        type: "file",
        subtype: "file_upload",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Why this internship?",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },
  {
    name: "Scholarship / Grant Application",
    description: "Education funding application with essay and transcripts",
    category: "application",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Scholarship Committee",
    respondantEmailSubject: "Application received",
    respondantEmailMessageHTML:
      "<p>Thanks for applying. We'll notify recipients by email.</p>",
    submitButtonText: "Submit Application",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: true,
    autoCloseDate: new Date("2025-11-30"),
    autoCloseTime: "23:59",
    autoCloseTimezone: "America/New_York",
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#16a34a",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#16a34a",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Applications are closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Provide your academic details and essay.",
    headerImage: "",
    headerTitle: "Scholarship Application",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "We've received your application.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#16a34a",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Current GPA",
        type: "number",
        subtype: "number",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Academic Transcripts",
        type: "file",
        subtype: "file_upload",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Household Income (optional)",
        type: "number",
        subtype: "number",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Personal Essay (max 500 words)",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },
  {
    name: "Vendor Application (Business)",
    description: "Vendor onboarding with business info",
    category: "application",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Vendor Relations",
    respondantEmailSubject: "Application received",
    respondantEmailMessageHTML:
      "<p>Thanks for applying to become a vendor. We'll review and respond.</p>",
    submitButtonText: "Submit Application",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#9333ea",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#9333ea",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Applications closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Provide your business information.",
    headerImage: "",
    headerTitle: "Vendor Application",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "We'll reach out if additional information is needed.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#9333ea",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Company Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Primary Contact",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Business Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Business Phone",
        type: "text",
        subtype: "phone",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Tax ID (EIN)",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Website",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Business Description",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },

  // SURVEY (4)
  {
    name: "NPS Survey",
    description: "Net Promoter style survey with follow-up reason",
    category: "survey",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Research Team",
    respondantEmailSubject: "Thanks for your response",
    respondantEmailMessageHTML: "<p>Thanks for helping us improve.</p>",
    submitButtonText: "Submit",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#0d9488",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#0d9488",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Survey closed.",
    closeMessageTitle: "Closed",
    headerDescription:
      "How likely are you to recommend us to a friend or colleague?",
    headerImage: "",
    headerTitle: "NPS Survey",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "Your response has been recorded.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#0d9488",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Likelihood to recommend (0-10)",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 10,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What's the primary reason for your score?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Customer Effort Score (CES)",
    description: "Measure how easy it was to complete a task",
    category: "survey",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "CX Team",
    respondantEmailSubject: "Thanks for your feedback",
    respondantEmailMessageHTML:
      "<p>Your input helps us reduce customer effort.</p>",
    submitButtonText: "Submit",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#14b8a6",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#14b8a6",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Survey closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Please rate the ease of completing your task today.",
    headerImage: "",
    headerTitle: "Customer Effort Score",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "Your response helps us improve.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#14b8a6",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "How easy was it to complete your task?",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Very easy" },
          { id: nanoid(), value: "Easy" },
          { id: nanoid(), value: "Neutral" },
          { id: nanoid(), value: "Difficult" },
          { id: nanoid(), value: "Very difficult" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What could we do to make it easier?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Employee Engagement Survey",
    description: "Pulse survey for employee engagement and well-being",
    category: "survey",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "HR Team",
    respondantEmailSubject: "Thanks for your input",
    respondantEmailMessageHTML:
      "<p>Your responses are anonymous and help us improve.</p>",
    submitButtonText: "Submit",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#7c3aed",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#7c3aed",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Survey closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Share your honest feedback.",
    headerImage: "",
    headerTitle: "Employee Engagement",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "Your feedback matters.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#7c3aed",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "How satisfied are you with your current role?",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 5,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "How supported do you feel by your manager?",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 5,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Which department are you in?",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "Engineering" },
          { id: nanoid(), value: "Product" },
          { id: nanoid(), value: "Design" },
          { id: nanoid(), value: "Sales" },
          { id: nanoid(), value: "Marketing" },
          { id: nanoid(), value: "HR" },
          { id: nanoid(), value: "Finance" },
          { id: nanoid(), value: "Other" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What could we do to improve your experience?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Market Research Survey",
    description: "Understand preferences, channels and trends",
    category: "survey",
    isFormboxTemplate: true,
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Research Team",
    respondantEmailSubject: "Thanks for participating",
    respondantEmailMessageHTML:
      "<p>Your responses will help guide our roadmap.</p>",
    submitButtonText: "Submit",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    accentColor: "#d97706",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#d97706",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Survey is closed.",
    closeMessageTitle: "Closed",
    headerDescription: "Tell us about your preferences and behavior.",
    headerImage: "",
    headerTitle: "Market Research",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#0f172a",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Thanks!",
    tpMessage: "We appreciate your time.",
    tpTextColor: "#0f172a",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#d97706",
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Age Group",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "18-24" },
          { id: nanoid(), value: "25-34" },
          { id: nanoid(), value: "35-44" },
          { id: nanoid(), value: "45-54" },
          { id: nanoid(), value: "55+" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Primary channels you use (select all that apply)",
        type: "select",
        subtype: "multiple_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Search" },
          { id: nanoid(), value: "Social media" },
          { id: nanoid(), value: "Email" },
          { id: nanoid(), value: "Word of mouth" },
          { id: nanoid(), value: "Events" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "How price-sensitive are you?",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Not at all" },
          { id: nanoid(), value: "Somewhat" },
          { id: nanoid(), value: "Very" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What trends are you seeing?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
].map((t) => ({ ...t, ipBlacklist: "" }));
