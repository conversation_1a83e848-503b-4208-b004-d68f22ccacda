#!/usr/bin/env tsx

/**
 * Reset Formbox templates in the database and seed the new templates.
 *
 * Usage: ./scripts/reset-and-seed-templates.ts (needs `tsx` installed in PATH)
 */

import { PrismaClient } from "@prisma/client";
import { prodSeedTemplates as seedTemplates } from "./prod-seed-templates";
// import { seedTemplates as devSeedTemplates } from "./seed-templates";

const prisma = new PrismaClient();

async function resetAndSeedTemplates() {
  console.log("🔄 Starting reset-and-seed for Formbox templates...");

  try {
    // Delete existing Formbox templates
    const deleted = await prisma.template.deleteMany({
      where: { isFormboxTemplate: true },
    });

    console.log(`🗑️  Deleted ${deleted.count} existing Formbox templates`);

    // Insert seed templates
    let created = 0;
    for (const template of seedTemplates) {
      try {
        await prisma.template.create({
          data: {
            name: template.name,
            description: template.description ?? "",
            category: template.category ?? "",
            isFormboxTemplate: !!template.isFormboxTemplate,
            fields: template.fields ?? [],

            // Form settings and customization
            removeFormboxBranding: template.removeFormboxBranding ?? false,
            sendEmailNotifications: template.sendEmailNotifications ?? true,
            emailsToNotify: template.emailsToNotify ?? [],
            submissionStorageDuration:
              template.submissionStorageDuration ?? "60",
            sendRespondantEmailNotifications:
              template.sendRespondantEmailNotifications ?? false,
            respondantEmailFromName: template.respondantEmailFromName ?? "",
            respondantEmailSubject: template.respondantEmailSubject ?? "",
            respondantEmailMessageHTML:
              template.respondantEmailMessageHTML ?? "",
            submitButtonText: template.submitButtonText ?? "Submit",
            limitResponses: template.limitResponses ?? false,
            isClosed: template.isClosed ?? false,
            autoCloseEnabled: template.autoCloseEnabled ?? false,
            autoCloseDate: template.autoCloseDate ?? null,
            autoCloseTime: template.autoCloseTime ?? null,
            autoCloseTimezone: template.autoCloseTimezone ?? null,
            maxResponses: template.maxResponses ?? null,
            useCustomRedirect: template.useCustomRedirect ?? false,
            customSuccessUrl: template.customSuccessUrl ?? "",
            webhookEnabled: template.webhookEnabled ?? false,
            webhookUrl: template.webhookUrl ?? "",
            customHoneypot: template.customHoneypot ?? "",
            googleRecaptchaEnabled: template.googleRecaptchaEnabled ?? false,
            googleRecaptchaSecretKey: template.googleRecaptchaSecretKey ?? "",
            allowedDomains: template.allowedDomains ?? "",
            allowedCountries: template.allowedCountries ?? "",

            // Styling
            accentColor: template.accentColor ?? "#030712",
            backgroundColor: template.backgroundColor ?? "#ffffff",
            buttonBackgroundColor: template.buttonBackgroundColor ?? "#030712",
            buttonBorderStyle: template.buttonBorderStyle ?? "rounded",
            buttonTextColor: template.buttonTextColor ?? "#ffffff",
            closeMessageDescription: template.closeMessageDescription ?? "",
            closeMessageTitle: template.closeMessageTitle ?? "",
            headerDescription: template.headerDescription ?? "",
            headerImage: template.headerImage ?? "",
            headerTitle: template.headerTitle ?? "",
            inputBorderStyle: template.inputBorderStyle ?? "rounded",
            logo: template.logo ?? "",
            pageMode: template.pageMode ?? "compact",
            saveAnswers: template.saveAnswers ?? false,
            showCustomClosedMessage: template.showCustomClosedMessage ?? false,
            textColor: template.textColor ?? "#000000",
            tpBackgroundColor: template.tpBackgroundColor ?? "#ffffff",
            tpButtonColor: template.tpButtonColor ?? "#030712",
            tpButtonText: template.tpButtonText ?? "",
            tpButtonUrl: template.tpButtonUrl ?? "",
            tpHeader: template.tpHeader ?? "",
            tpMessage: template.tpMessage ?? "",
            tpTextColor: template.tpTextColor ?? "#030712",
            useCustomThankYouPage: template.useCustomThankYouPage ?? false,
            tpButtonBackgroundColor:
              template.tpButtonBackgroundColor ?? "#f3f4f6",

            // SMTP
            smtpHost: template.smtpHost ?? null,
            smtpPassword: template.smtpPassword ?? null,
            smtpPort: template.smtpPort ?? null,
            smtpUsername: template.smtpUsername ?? null,
            smtpEnabled: template.smtpEnabled ?? false,
            smtpSenderEmail: template.smtpSenderEmail ?? null,

            ipBlacklist: template.ipBlacklist ?? "",
            organizationId: template.organizationId ?? null,
          },
        });

        created++;
        console.log(`✅ Created template: ${template.name}`);
      } catch (err) {
        console.error(`❌ Failed to create template ${template.name}:`, err);
      }
    }

    console.log(`🎉 Seed complete — created ${created} templates`);
  } catch (error) {
    console.error("💥 Error running reset-and-seed-templates:", error);
    process.exitCode = 1;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  resetAndSeedTemplates()
    .then(() => {
      console.log("Done.");
    })
    .catch((e) => {
      console.error(e);
      process.exit(1);
    });
}
