#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to update existing templates with new customization fields
 * This adds dummy data for all the new template settings that were added
 */

import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

const defaultTemplateSettings = {
  // Form settings
  removeFormboxBranding: false,
  sendEmailNotifications: true,
  emailsToNotify: ["<EMAIL>"],
  submissionStorageDuration: "365",
  sendRespondantEmailNotifications: false,
  respondantEmailFromName: "Formbox",
  respondantEmailSubject: "Thank you for your submission",
  respondantEmailMessageHTML:
    "<p>Thank you for your submission. We'll get back to you soon!</p>",
  submitButtonText: "Submit",
  limitResponses: false,
  isClosed: false,
  autoCloseEnabled: false,
  autoCloseDate: null,
  autoCloseTime: null,
  autoCloseTimezone: null,
  maxResponses: null,
  useCustomRedirect: false,
  customSuccessUrl: "",
  webhookEnabled: false,
  webhookUrl: "",
  customHoneypot: "",
  googleRecaptchaEnabled: false,
  googleRecaptchaSecretKey: "",
  allowedDomains: "",
  allowedCountries: "",
  ipBlacklist: "",

  // Styling options - Modern, clean defaults
  accentColor: "#3b82f6", // Blue
  backgroundColor: "#ffffff",
  buttonBackgroundColor: "#3b82f6",
  buttonBorderStyle: "rounded",
  buttonTextColor: "#ffffff",
  closeMessageDescription: "This form is currently closed.",
  closeMessageTitle: "Form Closed",
  headerDescription: "Please fill out the form below.",
  headerImage: "",
  headerTitle: "Contact Form",
  inputBorderStyle: "rounded",
  logo: "",
  pageMode: "compact",
  saveAnswers: true,
  showCustomClosedMessage: false,
  textColor: "#1f2937", // Dark gray
  tpBackgroundColor: "#ffffff",
  tpButtonColor: "#ffffff",
  tpButtonText: "Continue",
  tpButtonUrl: "",
  tpHeader: "Thank You!",
  tpMessage: "Your submission has been received successfully.",
  tpTextColor: "#1f2937",
  useCustomThankYouPage: false,
  tpButtonBackgroundColor: "#3b82f6",

  // SMTP settings - disabled by default
  smtpHost: null,
  smtpPassword: null,
  smtpPort: null,
  smtpUsername: null,
  smtpEnabled: false,
  smtpSenderEmail: null,
};

async function updateTemplates() {
  try {
    console.log("🚀 Starting template update script...");

    // Get all existing templates
    const templates = await prisma.template.findMany({
      select: {
        id: true,
        name: true,
        category: true,
        isFormboxTemplate: true,
      },
    });

    console.log(`📋 Found ${templates.length} templates to update`);

    if (templates.length === 0) {
      console.log("ℹ️  No templates found to update");
      return;
    }

    // Update each template with the new settings
    let updated = 0;
    let failed = 0;

    for (const template of templates) {
      try {
        console.log(`🔄 Updating template: ${template.name}`);

        // Customize settings based on template category/type
        const customSettings = { ...defaultTemplateSettings };

        // Customize based on category
        switch (template.category.toLowerCase()) {
          case "contact":
            customSettings.headerTitle = "Contact Us";
            customSettings.headerDescription =
              "We'd love to hear from you. Send us a message!";
            customSettings.tpHeader = "Message Sent!";
            customSettings.tpMessage =
              "Thank you for contacting us. We'll get back to you within 24 hours.";
            break;

          case "feedback":
            customSettings.headerTitle = "Share Your Feedback";
            customSettings.headerDescription =
              "Your opinion matters to us. Please share your thoughts.";
            customSettings.tpHeader = "Feedback Received!";
            customSettings.tpMessage = "Thank you for your valuable feedback.";
            customSettings.accentColor = "#10b981"; // Green
            customSettings.buttonBackgroundColor = "#10b981";
            customSettings.tpButtonBackgroundColor = "#10b981";
            break;

          case "survey":
            customSettings.headerTitle = "Product Survey";
            customSettings.headerDescription =
              "Help us improve by sharing your experience.";
            customSettings.tpHeader = "Survey Complete!";
            customSettings.tpMessage =
              "Thank you for participating in our survey.";
            customSettings.accentColor = "#8b5cf6"; // Purple
            customSettings.buttonBackgroundColor = "#8b5cf6";
            customSettings.tpButtonBackgroundColor = "#8b5cf6";
            break;

          case "registration":
          case "rsvp":
            customSettings.headerTitle = "Event Registration";
            customSettings.headerDescription =
              "Please fill out the form below to register.";
            customSettings.tpHeader = "Registration Confirmed!";
            customSettings.tpMessage =
              "You're all set! We look forward to seeing you at the event.";
            customSettings.accentColor = "#f59e0b"; // Orange
            customSettings.buttonBackgroundColor = "#f59e0b";
            customSettings.tpButtonBackgroundColor = "#f59e0b";
            break;

          case "application":
            customSettings.headerTitle = "Job Application";
            customSettings.headerDescription =
              "We're excited to learn more about you!";
            customSettings.tpHeader = "Application Submitted!";
            customSettings.tpMessage =
              "Thank you for your application. We'll review it and get back to you soon.";
            customSettings.accentColor = "#ef4444"; // Red
            customSettings.buttonBackgroundColor = "#ef4444";
            customSettings.tpButtonBackgroundColor = "#ef4444";
            break;

          default:
            // Keep default settings
            break;
        }

        // If it's a Formbox template, enable some premium features
        if (template.isFormboxTemplate) {
          customSettings.removeFormboxBranding = false; // Keep branding for official templates
          customSettings.saveAnswers = true;
          customSettings.sendEmailNotifications = true;
        }

        await prisma.template.update({
          where: { id: template.id },
          data: customSettings,
        });

        updated++;
        console.log(`✅ Updated template: ${template.name}`);
      } catch (error) {
        failed++;
        console.error(`❌ Failed to update template ${template.name}:`, error);
      }
    }

    console.log("\n📊 Update Summary:");
    console.log(`✅ Successfully updated: ${updated} templates`);
    console.log(`❌ Failed to update: ${failed} templates`);
    console.log(`📋 Total processed: ${templates.length} templates`);

    if (updated > 0) {
      console.log("\n🎉 Template update completed successfully!");
      console.log("📝 All templates now have customization settings applied.");
      console.log(
        "🎨 Settings include styling, form behavior, and email configurations.",
      );
    }
  } catch (error) {
    console.error("💥 Script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function main() {
  console.log("🏗️  Template Settings Update Script");
  console.log("=====================================");
  console.log(
    "This script will update all existing templates with new customization fields.\n",
  );

  await updateTemplates();
}

// Run the script
main().catch((error) => {
  console.error("💥 Unhandled error:", error);
  process.exit(1);
});
