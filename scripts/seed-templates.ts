/**
 * Seed templates for the database
 * Creates comprehensive form templates across multiple categories
 */

import { nanoid } from "nanoid";

export const seedTemplates = [
  // CONTACT CATEGORY (3 templates)
  {
    name: "Simple Contact Form",
    description: "Basic contact form for getting in touch with visitors",
    category: "contact",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Contact Team",
    respondantEmailSubject: "Thank you for contacting us",
    respondantEmailMessageHTML:
      "<p>Thank you for your message. We'll get back to you within 24 hours!</p>",
    submitButtonText: "Send Message",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Blue theme
    accentColor: "#3b82f6",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#3b82f6",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "This contact form is currently closed.",
    closeMessageTitle: "Form Closed",
    headerDescription: "We'd love to hear from you. Send us a message!",
    headerImage: "",
    headerTitle: "Contact Us",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Message Sent!",
    tpMessage:
      "Thank you for contacting us. We'll get back to you within 24 hours.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#3b82f6",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email Address",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Your Message",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },
  {
    name: "Business Inquiry Form",
    description:
      "Professional contact form for business inquiries and partnerships",
    category: "contact",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "Business Development",
    respondantEmailSubject: "Thank you for your business inquiry",
    respondantEmailMessageHTML:
      "<p>Thank you for reaching out. Our business development team will review your inquiry and respond within 2 business days.</p>",
    submitButtonText: "Submit Inquiry",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Dark blue theme
    accentColor: "#1e40af",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#1e40af",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Business inquiries are currently closed.",
    closeMessageTitle: "Form Closed",
    headerDescription: "Let's discuss how we can work together",
    headerImage: "",
    headerTitle: "Business Partnership Inquiry",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Inquiry Received!",
    tpMessage: "Thank you for your business inquiry. We'll be in touch soon.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#1e40af",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Company Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Contact Person",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Business Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Phone Number",
        type: "text",
        subtype: "phone",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Inquiry Details",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },
  {
    name: "Customer Support Form",
    description: "Help customers get support for their issues and questions",
    category: "contact",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "Customer Support",
    respondantEmailSubject: "Support ticket created",
    respondantEmailMessageHTML:
      "<p>Your support ticket has been created. Our team will respond within 24 hours. Ticket ID: #{{submission_id}}</p>",
    submitButtonText: "Submit Support Request",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Green theme
    accentColor: "#059669",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#059669",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Support form is currently unavailable.",
    closeMessageTitle: "Form Closed",
    headerDescription: "How can we help you today?",
    headerImage: "",
    headerTitle: "Customer Support",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Support Request Received!",
    tpMessage:
      "Your support ticket has been created. We'll get back to you soon.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#059669",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Your Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email Address",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Issue Type",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "Technical Issue" },
          { id: nanoid(), value: "Billing Question" },
          { id: nanoid(), value: "General Inquiry" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Describe Your Issue",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },

  // FEEDBACK CATEGORY (3 templates)
  {
    name: "Customer Feedback Survey",
    description:
      "Collect valuable feedback from your customers about their experience",
    category: "feedback",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "Customer Experience Team",
    respondantEmailSubject: "Thank you for your feedback",
    respondantEmailMessageHTML:
      "<p>Thank you for taking the time to share your feedback. Your input helps us improve our products and services.</p>",
    submitButtonText: "Submit Feedback",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Purple theme
    accentColor: "#8b5cf6",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#8b5cf6",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Feedback collection is currently closed.",
    closeMessageTitle: "Form Closed",
    headerDescription:
      "Your opinion matters to us. Please share your thoughts.",
    headerImage: "",
    headerTitle: "Share Your Feedback",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Feedback Received!",
    tpMessage: "Thank you for your valuable feedback. It helps us improve!",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#8b5cf6",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "How would you rate your overall experience?",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 5,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What could we improve?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "How likely are you to recommend us to a friend?",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 10,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Any additional comments?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Product Review Form",
    description: "Allow customers to leave detailed reviews for your products",
    category: "feedback",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "Product Team",
    respondantEmailSubject: "Thank you for your product review",
    respondantEmailMessageHTML:
      "<p>Thank you for leaving a review! Your feedback helps other customers make informed decisions.</p>",
    submitButtonText: "Submit Review",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Orange theme
    accentColor: "#f59e0b",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#f59e0b",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Product reviews are currently closed.",
    closeMessageTitle: "Form Closed",
    headerDescription: "Share your experience with this product",
    headerImage: "",
    headerTitle: "Product Review",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Review Submitted!",
    tpMessage: "Thank you for your review. It's been published successfully.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#f59e0b",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Product Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Product Rating",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 5,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Your Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Your Review",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Purchase Date",
        type: "date",
        subtype: "date",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Website Feedback Form",
    description: "Collect feedback about your website's usability and design",
    category: "feedback",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Web Team",
    respondantEmailSubject: "Thank you for your website feedback",
    respondantEmailMessageHTML:
      "<p>Thank you for helping us improve our website. Your feedback is invaluable!</p>",
    submitButtonText: "Submit Feedback",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Cyan theme
    accentColor: "#06b6d4",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#06b6d4",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Website feedback is currently closed.",
    closeMessageTitle: "Form Closed",
    headerDescription: "Help us improve your browsing experience",
    headerImage: "",
    headerTitle: "Website Feedback",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Feedback Received!",
    tpMessage: "Thank you for helping us improve our website!",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#06b6d4",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "How easy was it to find what you were looking for?",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Very Easy" },
          { id: nanoid(), value: "Easy" },
          { id: nanoid(), value: "Neutral" },
          { id: nanoid(), value: "Difficult" },
          { id: nanoid(), value: "Very Difficult" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Which page were you visiting?",
        type: "text",
        subtype: "short_answer",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What suggestions do you have for improving our website?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },

  // REGISTRATION CATEGORY (3 templates)
  {
    name: "Event Registration Form",
    description: "Register attendees for events, conferences, and workshops",
    category: "registration",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "Event Team",
    respondantEmailSubject: "Event registration confirmed",
    respondantEmailMessageHTML:
      "<p>Thank you for registering! We'll send you event details and updates as the date approaches.</p>",
    submitButtonText: "Register Now",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Emerald theme
    accentColor: "#10b981",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#10b981",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Event registration is currently closed.",
    closeMessageTitle: "Registration Closed",
    headerDescription:
      "Please fill out the form below to register for our event",
    headerImage: "",
    headerTitle: "Event Registration",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Registration Confirmed!",
    tpMessage: "You're all set! We look forward to seeing you at the event.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#10b981",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email Address",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Phone Number",
        type: "text",
        subtype: "phone",
        required: false,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Attendance Type",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "In Person" },
          { id: nanoid(), value: "Virtual" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Dietary Restrictions (if any)",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Workshop Sign-up Form",
    description: "Register participants for workshops and training sessions",
    category: "registration",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "Workshop Team",
    respondantEmailSubject: "Workshop registration confirmed",
    respondantEmailMessageHTML:
      "<p>Welcome to our workshop! You'll receive preparation materials and joining instructions 24 hours before the session.</p>",
    submitButtonText: "Sign Up",
    limitResponses: true,
    maxResponses: 50,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Indigo theme
    accentColor: "#6366f1",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#6366f1",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Workshop registration is currently closed.",
    closeMessageTitle: "Registration Closed",
    headerDescription: "Limited spots available - register now!",
    headerImage: "",
    headerTitle: "Workshop Registration",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "You're In!",
    tpMessage: "Workshop registration confirmed. Check your email for details.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#6366f1",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email Address",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Experience Level",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "Beginner" },
          { id: nanoid(), value: "Intermediate" },
          { id: nanoid(), value: "Advanced" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What do you hope to learn from this workshop?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Conference Registration Form",
    description: "Comprehensive registration form for multi-day conferences",
    category: "registration",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "Conference Team",
    respondantEmailSubject: "Conference registration confirmed",
    respondantEmailMessageHTML:
      "<p>Thank you for registering for our conference! Your ticket and agenda will be sent to you soon.</p>",
    submitButtonText: "Complete Registration",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: true,
    autoCloseDate: new Date("2025-12-31"),
    autoCloseTime: "23:59",
    autoCloseTimezone: "America/New_York",
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Rose theme
    accentColor: "#f43f5e",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#f43f5e",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Conference registration has ended.",
    closeMessageTitle: "Registration Closed",
    headerDescription:
      "Join industry leaders for three days of learning and networking",
    headerImage: "",
    headerTitle: "Annual Conference Registration",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Registration Complete!",
    tpMessage:
      "Welcome to the conference! We'll send your ticket and details soon.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#f43f5e",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Company/Organization",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Job Title",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email Address",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Ticket Type",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "General Admission" },
          { id: nanoid(), value: "VIP Pass" },
          { id: nanoid(), value: "Student Discount" },
        ],
        showDescription: false,
      },
    ],
  },

  // APPLICATION CATEGORY (3 templates)
  {
    name: "Job Application Form",
    description:
      "Collect job applications with resume upload and screening questions",
    category: "application",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "HR Team",
    respondantEmailSubject: "Application received",
    respondantEmailMessageHTML:
      "<p>Thank you for your application! We'll review it and get back to you within 2 weeks if you're selected for an interview.</p>",
    submitButtonText: "Submit Application",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Blue theme
    accentColor: "#2563eb",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#2563eb",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Job applications are currently closed.",
    closeMessageTitle: "Applications Closed",
    headerDescription: "We're excited to learn more about you!",
    headerImage: "",
    headerTitle: "Job Application",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Application Submitted!",
    tpMessage:
      "Thank you for your application. We'll review it and get back to you soon.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#2563eb",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email Address",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Phone Number",
        type: "text",
        subtype: "phone",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Position Applied For",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Resume/CV",
        type: "file",
        subtype: "file_upload",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Cover Letter",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Scholarship Application Form",
    description: "Application form for educational scholarships and grants",
    category: "application",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "Scholarship Committee",
    respondantEmailSubject: "Scholarship application received",
    respondantEmailMessageHTML:
      "<p>Thank you for applying for our scholarship program. We'll review all applications and notify winners by the announced date.</p>",
    submitButtonText: "Submit Application",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: true,
    autoCloseDate: new Date("2025-06-30"),
    autoCloseTime: "23:59",
    autoCloseTimezone: "America/New_York",
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Green theme
    accentColor: "#16a34a",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#16a34a",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Scholarship applications are currently closed.",
    closeMessageTitle: "Application Period Ended",
    headerDescription:
      "Invest in your future - apply for our scholarship program",
    headerImage: "",
    headerTitle: "Scholarship Application",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Application Submitted!",
    tpMessage: "Your scholarship application has been received. Good luck!",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#16a34a",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Full Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Email Address",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Current GPA",
        type: "number",
        subtype: "number",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Academic Transcripts",
        type: "file",
        subtype: "file_upload",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Personal Essay (500 words max)",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },
  {
    name: "Vendor Application Form",
    description:
      "Application form for vendors and suppliers to join your platform",
    category: "application",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "Vendor Relations",
    respondantEmailSubject: "Vendor application received",
    respondantEmailMessageHTML:
      "<p>Thank you for your interest in becoming a vendor. We'll review your application and contact you within 5 business days.</p>",
    submitButtonText: "Submit Application",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Purple theme
    accentColor: "#9333ea",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#9333ea",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Vendor applications are currently closed.",
    closeMessageTitle: "Applications Closed",
    headerDescription: "Join our network of trusted vendors and suppliers",
    headerImage: "",
    headerTitle: "Vendor Application",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Application Received!",
    tpMessage: "Thank you for your vendor application. We'll be in touch soon.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#9333ea",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Company Name",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Primary Contact Person",
        type: "text",
        subtype: "short_answer",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Business Email",
        type: "text",
        subtype: "email",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Business Phone",
        type: "text",
        subtype: "phone",
        required: true,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Product/Service Categories",
        type: "select",
        subtype: "multiple_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Electronics" },
          { id: nanoid(), value: "Clothing & Accessories" },
          { id: nanoid(), value: "Home & Garden" },
          { id: nanoid(), value: "Professional Services" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Business Description",
        type: "text",
        subtype: "long_answer",
        required: true,
        showDescription: false,
      },
    ],
  },

  // SURVEY CATEGORY (3 templates)
  {
    name: "Customer Satisfaction Survey",
    description: "Measure customer satisfaction with your products or services",
    category: "survey",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: true,
    respondantEmailFromName: "Research Team",
    respondantEmailSubject: "Thank you for participating in our survey",
    respondantEmailMessageHTML:
      "<p>Thank you for taking the time to complete our customer satisfaction survey. Your feedback is valuable to us!</p>",
    submitButtonText: "Submit Survey",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Teal theme
    accentColor: "#0d9488",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#0d9488",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "This survey is currently closed.",
    closeMessageTitle: "Survey Closed",
    headerDescription: "Help us improve by sharing your experience",
    headerImage: "",
    headerTitle: "Customer Satisfaction Survey",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Survey Complete!",
    tpMessage:
      "Thank you for participating in our customer satisfaction survey.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#0d9488",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "How satisfied are you with our service overall?",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 5,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "How would you rate the quality of our service?",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Excellent" },
          { id: nanoid(), value: "Good" },
          { id: nanoid(), value: "Average" },
          { id: nanoid(), value: "Poor" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "How likely are you to recommend us to others?",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 10,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Any additional feedback?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Market Research Survey",
    description:
      "Conduct market research to understand customer preferences and trends",
    category: "survey",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "Market Research Team",
    respondantEmailSubject: "Thank you for your participation",
    respondantEmailMessageHTML:
      "<p>Thank you for participating in our market research survey. Your responses will help shape future products and services.</p>",
    submitButtonText: "Complete Survey",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Amber theme
    accentColor: "#d97706",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#d97706",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Market research survey is currently closed.",
    closeMessageTitle: "Survey Closed",
    headerDescription: "Your opinion shapes our future - please participate",
    headerImage: "",
    headerTitle: "Market Research Survey",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Survey Submitted!",
    tpMessage: "Thank you for your valuable input in our market research.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#d97706",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "Age Group",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "18-24" },
          { id: nanoid(), value: "25-34" },
          { id: nanoid(), value: "35-44" },
          { id: nanoid(), value: "45-54" },
          { id: nanoid(), value: "55+" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "How often do you purchase our type of product?",
        type: "select",
        subtype: "single_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Weekly" },
          { id: nanoid(), value: "Monthly" },
          { id: nanoid(), value: "Every 3 months" },
          { id: nanoid(), value: "Once a year" },
          { id: nanoid(), value: "Never" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label:
          "What factors are most important when making a purchase? (Select all that apply)",
        type: "select",
        subtype: "multiple_choice",
        required: true,
        options: [
          { id: nanoid(), value: "Price" },
          { id: nanoid(), value: "Quality" },
          { id: nanoid(), value: "Brand reputation" },
          { id: nanoid(), value: "Convenience" },
          { id: nanoid(), value: "Customer reviews" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What trends do you see emerging in this market?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
  {
    name: "Employee Engagement Survey",
    description:
      "Measure employee satisfaction and engagement in the workplace",
    category: "survey",
    isFormboxTemplate: true,
    // Form settings
    removeFormboxBranding: false,
    sendEmailNotifications: true,
    emailsToNotify: ["<EMAIL>"],
    submissionStorageDuration: "365",
    sendRespondantEmailNotifications: false,
    respondantEmailFromName: "HR Team",
    respondantEmailSubject: "Employee survey participation confirmed",
    respondantEmailMessageHTML:
      "<p>Thank you for participating in our employee engagement survey. Your responses are anonymous and will help improve our workplace.</p>",
    submitButtonText: "Submit Survey",
    limitResponses: false,
    isClosed: false,
    autoCloseEnabled: false,
    autoCloseDate: null,
    autoCloseTime: null,
    autoCloseTimezone: null,
    maxResponses: null,
    useCustomRedirect: false,
    customSuccessUrl: "",
    webhookEnabled: false,
    webhookUrl: "",
    customHoneypot: "",
    googleRecaptchaEnabled: false,
    googleRecaptchaSecretKey: "",
    allowedDomains: "",
    allowedCountries: "",
    ipBlacklist: "",
    // Styling - Violet theme
    accentColor: "#7c3aed",
    backgroundColor: "#ffffff",
    buttonBackgroundColor: "#7c3aed",
    buttonBorderStyle: "rounded",
    buttonTextColor: "#ffffff",
    closeMessageDescription: "Employee survey is currently closed.",
    closeMessageTitle: "Survey Closed",
    headerDescription:
      "Your honest feedback helps us create a better workplace",
    headerImage: "",
    headerTitle: "Employee Engagement Survey",
    inputBorderStyle: "rounded",
    logo: "",
    pageMode: "compact",
    saveAnswers: true,
    showCustomClosedMessage: false,
    textColor: "#1f2937",
    tpBackgroundColor: "#ffffff",
    tpButtonColor: "#ffffff",
    tpButtonText: "Continue",
    tpButtonUrl: "",
    tpHeader: "Survey Complete!",
    tpMessage:
      "Thank you for your honest feedback. All responses are confidential.",
    tpTextColor: "#1f2937",
    useCustomThankYouPage: false,
    tpButtonBackgroundColor: "#7c3aed",
    // SMTP settings
    smtpHost: null,
    smtpPassword: null,
    smtpPort: null,
    smtpUsername: null,
    smtpEnabled: false,
    smtpSenderEmail: null,
    organizationId: null,
    fields: [
      {
        id: nanoid(),
        label: "How satisfied are you with your current role?",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 5,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "How would you rate your work-life balance?",
        type: "rating",
        subtype: "rating",
        required: true,
        ratingCount: 5,
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "Which department do you work in?",
        type: "select",
        subtype: "dropdown",
        required: true,
        options: [
          { id: nanoid(), value: "Engineering" },
          { id: nanoid(), value: "Sales" },
          { id: nanoid(), value: "Marketing" },
          { id: nanoid(), value: "Human Resources" },
          { id: nanoid(), value: "Finance" },
          { id: nanoid(), value: "Other" },
        ],
        showDescription: false,
      },
      {
        id: nanoid(),
        label: "What suggestions do you have for improving our workplace?",
        type: "text",
        subtype: "long_answer",
        required: false,
        showDescription: false,
      },
    ],
  },
];
