import { withPostHogConfig } from "@posthog/nextjs-config";

/*
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
await import("./src/env.js");

function createHeaders() {
  const isDev = process.env.NODE_ENV !== "production";

  if (isDev) {
    return [];
  }

  //   const cspHeader = `
  //     default-src 'self';
  //     script-src 'self' 'unsafe-eval' 'unsafe-inline';
  //     style-src 'self' 'unsafe-inline';
  //     img-src 'self' blob: data:;
  //     font-src 'self';
  //     object-src 'none';
  //     base-uri 'self';
  //     frame-src ${process.env.APP_URL}/forms/*;
  //     upgrade-insecure-requests;
  // `;

  return [
    {
      source: "/:path*",
      headers: [
        // {
        //   key: "Content-Security-Policy",
        //   value: cspHeader.replace(/\n/g, ""),
        // },
        {
          key: "X-Content-Type-Options",
          value: "nosniff",
        },
        {
          key: "Referrer-Policy",
          value: "strict-origin-when-cross-origin",
        },
      ],
    },
  ];
}

/** @type {import("next").NextConfig} */
const nextConfig = {
  async headers() {
    return createHeaders();
  },
  async redirects() {
    return [
      {
        source: "/",
        destination: "/organizations",
        permanent: false,
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: "/ingest/static/:path*",
        destination: "https://us-assets.i.posthog.com/static/:path*",
      },
      {
        source: "/ingest/:path*",
        destination: "https://us.i.posthog.com/:path*",
      },
      {
        source: "/ingest/decide",
        destination: "https://us.i.posthog.com/decide",
      },
    ];
  },
  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,
  typescript: {
    ignoreBuildErrors: process.env.VERCEL_ENV === "preview",
  },
};

export default withPostHogConfig(nextConfig, {
  personalApiKey: process.env.POSTHOG_API_KEY ?? "", // Personal API Key
  envId: process.env.POSTHOG_ENV_ID ?? "",
  host: process.env.NEXT_PUBLIC_POSTHOG_HOST ?? "https://us.posthog.com", // (optional), defaults to https://us.posthog.com
  sourcemaps: {
    // (optional)
    enabled: true, // (optional) Enable sourcemaps generation and upload, default to true on production builds
    project: "formbox", // (optional) Project name, defaults to repository name
    version: "1.0.0", // (optional) Release version, defaults to current git commit
    deleteAfterUpload: true, // (optional) Delete sourcemaps after upload, defaults to true
  },
});
