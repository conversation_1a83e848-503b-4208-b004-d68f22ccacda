<a href="https://formbox.app">
  <img alt="Formbox is an all in one forms solution for your business." src="https://media.cleanshot.cloud/media/6204/Y9QXlS6vDLZ7CqDf9MH4YIek9ViFTwdeRSlZslTW.jpeg?Expires=1724712356&Signature=dPoPOuwXyU9cLdTiWq5rZB07hy7VqDi55rvQv9UlFQu~~Rhytk-5ZUnmuOOoIXIAa8Ik~S5tPIlPr093YlIlCzQDgwq-awELhczs1Znzfk1wIDxCeI6bwPjMA24XTFBDX4rzjt9MEZ8wGfb3nUl1EHGtfgexNO13L~9bjVD6~6YUoRMHBYwZ24o95bCXug~vXPHNks1gCV~OPbxj0t2~15~Ok9tVVZuKTuYYpzK808Hia0qMPW-ZQg0V80uay5Uh276LNXwG1-jM-UtI~Q2a2S6B63DFDjqzr5KKX90sdDoMFVGaL7obgUPy5fS-P8EF9-KMx9slZv-rhj5zvdl1gA__&Key-Pair-Id=K269JMAT9ZF4GZ">
</a>

<h3 align="center">Formbox</h3>

<p align="center">
    All in one forms solution for your business.
    <br />
    <a href="https://formbox.app"><strong>Learn more »</strong></a>
    <br />
    <br />
    <a href="#introduction"><strong>Introduction</strong></a> ·
    <a href="#features"><strong>Features</strong></a> ·
    <a href="#tech-stack"><strong>Tech Stack</strong></a> ·
    <a href="https://docs.formbox.app"><strong>Documentation</strong></a>
</p>

<br/>

## Introduction

Formbox is a powerfull forms management platform for your business. It allows you to collect submissions from your websites without any backend code or use a customizable form builder to build beautiful forms. You can get email notifications for new submissions, upload files from your forms, and even integrate with services like Google Sheets, Slack, and more.

## Features

- [Team collaboration](https://docs.formbox.app/features/organizations)
- [Email notifications](https://docs.formbox.app/features/self-email-notifications)
- [Custom redirects](https://docs.formbox.app/features/custom-redirects)
- [Spam protection](https://docs.formbox.app/features/spam-protection)
- [Integrations](https://docs.formbox.app/integrations/overview)
- [Form builder](https://formbox.app)
- [Developer API](https://docs.formbox.app/api-reference)

## Tech Stack

- [Next.js](https://nextjs.org/) – Framework
- [TypeScript](https://www.typescriptlang.org/) – Language
- [Tailwind](https://tailwindcss.com/) – CSS Framework
- [Upstash](https://upstash.com/) – Redis / Message Queue
- [Supabase](https://supabase.com/) – Database
- [Prisma](https://prisma.io/) - ORM
- [BetterAuth.js](https://better-auth.com/) – Authentication
- [tRPC](https://trpc.io) – API
- [Cloudflare R2](https://www.cloudflare.com/r2/) – Object Storage
- [Stripe](https://stripe.com/) – Payments
- [Resend](https://resend.com/) – Emails
- [Nango.dev](https://nango.dev/) – Integrations
- [Vercel](https://vercel.com/) – Deployments
- [Spam Check AI](https://spamcheck.ai/) – Spam Protection
