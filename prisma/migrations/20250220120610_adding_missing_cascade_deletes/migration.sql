-- DropForeign<PERSON>ey
ALTER TABLE "apiKeys" DROP CONSTRAINT "apiKeys_organizationId_fkey";

-- DropForeignKey
ALTER TABLE "forms" DROP CONSTRAINT "forms_organizationId_fkey";

-- AddForeignKey
ALTER TABLE "apiKeys" ADD CONSTRAINT "apiKeys_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forms" ADD CONSTRAINT "forms_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
