-- CreateTable
CREATE TABLE "accounts" (
    "id" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "idToken" TEXT,
    "accessTokenExpiresAt" TIMESTAMP(3),
    "refreshTokenExpiresAt" TIMESTAMP(3),
    "scope" TEXT,
    "password" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "image" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "apiKeys" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "partialKey" TEXT NOT NULL,
    "expires" TIMESTAMP(3),
    "key" TEXT NOT NULL,
    "scopes" TEXT[],
    "organizationId" TEXT,

    CONSTRAINT "apiKeys_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "organizations" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT,
    "logo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "metadata" TEXT,
    "stripeCustomerId" TEXT,
    "stripeSubscriptionId" TEXT,
    "stripeSubscriptionStatus" TEXT,
    "stripePlanNickname" TEXT,
    "stripePlan" TEXT,
    "stripeCancelAtPeriodEnd" BOOLEAN DEFAULT false,
    "stripeCurrentPeriodEnd" TIMESTAMP(3),
    "stripeTrialEnd" TIMESTAMP(3),

    CONSTRAINT "organizations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "members" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "members_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invitations" (
    "id" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "role" TEXT,
    "status" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "inviterId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "invitations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "forms" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "removeFormboxBranding" BOOLEAN NOT NULL DEFAULT false,
    "sendEmailNotifications" BOOLEAN NOT NULL DEFAULT true,
    "emailsToNotify" TEXT[],
    "submissionStorageDuration" TEXT NOT NULL DEFAULT '60',
    "sendRespondantEmailNotifications" BOOLEAN NOT NULL DEFAULT false,
    "respondantEmailFromName" TEXT NOT NULL DEFAULT '',
    "respondantEmailSubject" TEXT NOT NULL DEFAULT '',
    "respondantEmailMessageHTML" TEXT NOT NULL DEFAULT '',
    "submitButtonText" TEXT NOT NULL DEFAULT 'Submit',
    "limitResponses" BOOLEAN NOT NULL DEFAULT false,
    "isClosed" BOOLEAN NOT NULL DEFAULT false,
    "maxResponses" INTEGER,
    "useCustomRedirect" BOOLEAN NOT NULL DEFAULT false,
    "customSuccessUrl" TEXT NOT NULL DEFAULT '',
    "webhookEnabled" BOOLEAN NOT NULL DEFAULT false,
    "webhookUrl" TEXT NOT NULL DEFAULT '',
    "customHoneypot" TEXT NOT NULL DEFAULT '',
    "googleRecaptchaEnabled" BOOLEAN NOT NULL DEFAULT false,
    "googleRecaptchaSecretKey" TEXT NOT NULL DEFAULT '',
    "allowedDomains" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'endpoint',
    "fields" JSONB NOT NULL DEFAULT '[]',
    "accentColor" TEXT NOT NULL DEFAULT '#030712',
    "backgroundColor" TEXT NOT NULL DEFAULT '#ffffff',
    "buttonBackgroundColor" TEXT NOT NULL DEFAULT '#030712',
    "buttonBorderStyle" TEXT NOT NULL DEFAULT 'rounded',
    "buttonTextColor" TEXT NOT NULL DEFAULT '#ffffff',
    "closeMessageDescription" TEXT NOT NULL DEFAULT '',
    "closeMessageTitle" TEXT NOT NULL DEFAULT '',
    "headerDescription" TEXT NOT NULL DEFAULT '',
    "headerImage" TEXT NOT NULL DEFAULT '',
    "headerTitle" TEXT NOT NULL DEFAULT '',
    "inputBorderStyle" TEXT NOT NULL DEFAULT 'rounded',
    "logo" TEXT NOT NULL DEFAULT '',
    "pageMode" TEXT NOT NULL DEFAULT 'compact',
    "saveAnswers" BOOLEAN NOT NULL DEFAULT false,
    "showCustomClosedMessage" BOOLEAN NOT NULL DEFAULT false,
    "textColor" TEXT NOT NULL DEFAULT '#000000',
    "tpBackgroundColor" TEXT NOT NULL DEFAULT '#ffffff',
    "tpButtonColor" TEXT NOT NULL DEFAULT '#030712',
    "tpButtonText" TEXT NOT NULL DEFAULT '',
    "tpButtonUrl" TEXT NOT NULL DEFAULT '',
    "tpHeader" TEXT NOT NULL DEFAULT '',
    "tpMessage" TEXT NOT NULL DEFAULT '',
    "tpTextColor" TEXT NOT NULL DEFAULT '#030712',
    "useCustomThankYouPage" BOOLEAN NOT NULL DEFAULT false,
    "tpButtonBackgroundColor" TEXT NOT NULL DEFAULT '#f3f4f6',
    "smtpHost" TEXT,
    "smtpPassword" TEXT,
    "smtpPort" INTEGER,
    "smtpUsername" TEXT,
    "smtpEnabled" BOOLEAN NOT NULL DEFAULT false,
    "smtpSenderEmail" TEXT,
    "ipBlacklist" TEXT NOT NULL DEFAULT '',

    CONSTRAINT "forms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "submissions" (
    "id" TEXT NOT NULL,
    "formId" TEXT NOT NULL,
    "isSpam" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "browser" TEXT NOT NULL DEFAULT '',
    "countryCode" TEXT NOT NULL DEFAULT '',
    "ipAddress" TEXT NOT NULL DEFAULT '',
    "os" TEXT NOT NULL DEFAULT '',
    "platform" TEXT NOT NULL DEFAULT '',

    CONSTRAINT "submissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "answers" (
    "id" TEXT NOT NULL,
    "label" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "answers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "files" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "size" INTEGER NOT NULL,
    "url" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "submissionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "formId" TEXT NOT NULL,
    "formFieldName" TEXT,

    CONSTRAINT "files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "integrations" (
    "id" TEXT NOT NULL,
    "formId" TEXT NOT NULL,
    "connectionId" TEXT DEFAULT '',
    "type" TEXT NOT NULL,
    "isEnabled" BOOLEAN NOT NULL DEFAULT false,
    "spreadsheetId" TEXT DEFAULT '',
    "slackTeamId" TEXT DEFAULT '',
    "slackTeamName" TEXT DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orgId" TEXT NOT NULL,
    "organizationId" TEXT,
    "airtableBaseId" TEXT DEFAULT '',
    "airtableTableId" TEXT DEFAULT '',
    "webhookUrl" TEXT DEFAULT '',
    "mailchimpDC" TEXT DEFAULT '',
    "mailchimpListId" TEXT DEFAULT '',
    "slackChannelId" TEXT DEFAULT '',
    "excelWebUrl" TEXT DEFAULT '',

    CONSTRAINT "integrations_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "apiKeys_key_key" ON "apiKeys"("key");

-- CreateIndex
CREATE UNIQUE INDEX "organizations_slug_key" ON "organizations"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "members_userId_organizationId_key" ON "members"("userId", "organizationId");

-- CreateIndex
CREATE UNIQUE INDEX "invitations_email_organizationId_key" ON "invitations"("email", "organizationId");

-- AddForeignKey
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "apiKeys" ADD CONSTRAINT "apiKeys_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "members" ADD CONSTRAINT "members_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "members" ADD CONSTRAINT "members_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_inviterId_fkey" FOREIGN KEY ("inviterId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "forms" ADD CONSTRAINT "forms_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "submissions" ADD CONSTRAINT "submissions_formId_fkey" FOREIGN KEY ("formId") REFERENCES "forms"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "answers" ADD CONSTRAINT "answers_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "submissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "files" ADD CONSTRAINT "files_formId_fkey" FOREIGN KEY ("formId") REFERENCES "forms"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "files" ADD CONSTRAINT "files_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "submissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "integrations" ADD CONSTRAINT "integrations_formId_fkey" FOREIGN KEY ("formId") REFERENCES "forms"("id") ON DELETE CASCADE ON UPDATE CASCADE;
