/*
  Warnings:

  - Made the column `accentColor` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `allowedCountries` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `allowedDomains` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `autoCloseEnabled` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `backgroundColor` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `buttonBackgroundColor` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `buttonBorderStyle` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `buttonTextColor` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `closeMessageDescription` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `closeMessageTitle` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `customHoneypot` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `customSuccessUrl` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `googleRecaptchaEnabled` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `googleRecaptchaSecretKey` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `headerDescription` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `headerImage` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `headerTitle` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `inputBorderStyle` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `ipBlacklist` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `isClosed` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `limitResponses` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `logo` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `pageMode` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `removeFormboxBranding` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `respondantEmailFromName` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `respondantEmailMessageHTML` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `respondantEmailSubject` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `saveAnswers` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `sendEmailNotifications` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `sendRespondantEmailNotifications` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `showCustomClosedMessage` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `smtpEnabled` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `submissionStorageDuration` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `submitButtonText` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `textColor` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tpBackgroundColor` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tpButtonBackgroundColor` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tpButtonColor` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tpButtonText` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tpButtonUrl` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tpHeader` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tpMessage` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `tpTextColor` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `useCustomRedirect` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `useCustomThankYouPage` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `webhookEnabled` on table `templates` required. This step will fail if there are existing NULL values in that column.
  - Made the column `webhookUrl` on table `templates` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "templates" ALTER COLUMN "accentColor" SET NOT NULL,
ALTER COLUMN "accentColor" SET DEFAULT '#030712',
ALTER COLUMN "allowedCountries" SET NOT NULL,
ALTER COLUMN "allowedCountries" SET DEFAULT '',
ALTER COLUMN "allowedDomains" SET NOT NULL,
ALTER COLUMN "allowedDomains" SET DEFAULT '',
ALTER COLUMN "autoCloseEnabled" SET NOT NULL,
ALTER COLUMN "autoCloseEnabled" SET DEFAULT false,
ALTER COLUMN "backgroundColor" SET NOT NULL,
ALTER COLUMN "backgroundColor" SET DEFAULT '#ffffff',
ALTER COLUMN "buttonBackgroundColor" SET NOT NULL,
ALTER COLUMN "buttonBackgroundColor" SET DEFAULT '#030712',
ALTER COLUMN "buttonBorderStyle" SET NOT NULL,
ALTER COLUMN "buttonBorderStyle" SET DEFAULT 'rounded',
ALTER COLUMN "buttonTextColor" SET NOT NULL,
ALTER COLUMN "buttonTextColor" SET DEFAULT '#ffffff',
ALTER COLUMN "closeMessageDescription" SET NOT NULL,
ALTER COLUMN "closeMessageDescription" SET DEFAULT '',
ALTER COLUMN "closeMessageTitle" SET NOT NULL,
ALTER COLUMN "closeMessageTitle" SET DEFAULT '',
ALTER COLUMN "customHoneypot" SET NOT NULL,
ALTER COLUMN "customHoneypot" SET DEFAULT '',
ALTER COLUMN "customSuccessUrl" SET NOT NULL,
ALTER COLUMN "customSuccessUrl" SET DEFAULT '',
ALTER COLUMN "googleRecaptchaEnabled" SET NOT NULL,
ALTER COLUMN "googleRecaptchaEnabled" SET DEFAULT false,
ALTER COLUMN "googleRecaptchaSecretKey" SET NOT NULL,
ALTER COLUMN "googleRecaptchaSecretKey" SET DEFAULT '',
ALTER COLUMN "headerDescription" SET NOT NULL,
ALTER COLUMN "headerDescription" SET DEFAULT '',
ALTER COLUMN "headerImage" SET NOT NULL,
ALTER COLUMN "headerImage" SET DEFAULT '',
ALTER COLUMN "headerTitle" SET NOT NULL,
ALTER COLUMN "headerTitle" SET DEFAULT '',
ALTER COLUMN "inputBorderStyle" SET NOT NULL,
ALTER COLUMN "inputBorderStyle" SET DEFAULT 'rounded',
ALTER COLUMN "ipBlacklist" SET NOT NULL,
ALTER COLUMN "ipBlacklist" SET DEFAULT '',
ALTER COLUMN "isClosed" SET NOT NULL,
ALTER COLUMN "isClosed" SET DEFAULT false,
ALTER COLUMN "limitResponses" SET NOT NULL,
ALTER COLUMN "limitResponses" SET DEFAULT false,
ALTER COLUMN "logo" SET NOT NULL,
ALTER COLUMN "logo" SET DEFAULT '',
ALTER COLUMN "pageMode" SET NOT NULL,
ALTER COLUMN "pageMode" SET DEFAULT 'compact',
ALTER COLUMN "removeFormboxBranding" SET NOT NULL,
ALTER COLUMN "removeFormboxBranding" SET DEFAULT false,
ALTER COLUMN "respondantEmailFromName" SET NOT NULL,
ALTER COLUMN "respondantEmailFromName" SET DEFAULT '',
ALTER COLUMN "respondantEmailMessageHTML" SET NOT NULL,
ALTER COLUMN "respondantEmailMessageHTML" SET DEFAULT '',
ALTER COLUMN "respondantEmailSubject" SET NOT NULL,
ALTER COLUMN "respondantEmailSubject" SET DEFAULT '',
ALTER COLUMN "saveAnswers" SET NOT NULL,
ALTER COLUMN "saveAnswers" SET DEFAULT false,
ALTER COLUMN "sendEmailNotifications" SET NOT NULL,
ALTER COLUMN "sendEmailNotifications" SET DEFAULT true,
ALTER COLUMN "sendRespondantEmailNotifications" SET NOT NULL,
ALTER COLUMN "sendRespondantEmailNotifications" SET DEFAULT false,
ALTER COLUMN "showCustomClosedMessage" SET NOT NULL,
ALTER COLUMN "showCustomClosedMessage" SET DEFAULT false,
ALTER COLUMN "smtpEnabled" SET NOT NULL,
ALTER COLUMN "smtpEnabled" SET DEFAULT false,
ALTER COLUMN "submissionStorageDuration" SET NOT NULL,
ALTER COLUMN "submissionStorageDuration" SET DEFAULT '60',
ALTER COLUMN "submitButtonText" SET NOT NULL,
ALTER COLUMN "submitButtonText" SET DEFAULT 'Submit',
ALTER COLUMN "textColor" SET NOT NULL,
ALTER COLUMN "textColor" SET DEFAULT '#000000',
ALTER COLUMN "tpBackgroundColor" SET NOT NULL,
ALTER COLUMN "tpBackgroundColor" SET DEFAULT '#ffffff',
ALTER COLUMN "tpButtonBackgroundColor" SET NOT NULL,
ALTER COLUMN "tpButtonBackgroundColor" SET DEFAULT '#f3f4f6',
ALTER COLUMN "tpButtonColor" SET NOT NULL,
ALTER COLUMN "tpButtonColor" SET DEFAULT '#030712',
ALTER COLUMN "tpButtonText" SET NOT NULL,
ALTER COLUMN "tpButtonText" SET DEFAULT '',
ALTER COLUMN "tpButtonUrl" SET NOT NULL,
ALTER COLUMN "tpButtonUrl" SET DEFAULT '',
ALTER COLUMN "tpHeader" SET NOT NULL,
ALTER COLUMN "tpHeader" SET DEFAULT '',
ALTER COLUMN "tpMessage" SET NOT NULL,
ALTER COLUMN "tpMessage" SET DEFAULT '',
ALTER COLUMN "tpTextColor" SET NOT NULL,
ALTER COLUMN "tpTextColor" SET DEFAULT '#030712',
ALTER COLUMN "useCustomRedirect" SET NOT NULL,
ALTER COLUMN "useCustomRedirect" SET DEFAULT false,
ALTER COLUMN "useCustomThankYouPage" SET NOT NULL,
ALTER COLUMN "useCustomThankYouPage" SET DEFAULT false,
ALTER COLUMN "webhookEnabled" SET NOT NULL,
ALTER COLUMN "webhookEnabled" SET DEFAULT false,
ALTER COLUMN "webhookUrl" SET NOT NULL,
ALTER COLUMN "webhookUrl" SET DEFAULT '';
