-- AlterTable
ALTER TABLE "templates" ADD COLUMN     "accentColor" TEXT,
ADD COLUMN     "allowedCountries" TEXT,
ADD COLUMN     "allowedDomains" TEXT,
ADD COLUMN     "autoCloseDate" TIMESTAMP(3),
ADD COLUMN     "autoCloseEnabled" BOOLEAN,
ADD COLUMN     "autoCloseTime" TEXT,
ADD COLUMN     "autoCloseTimezone" TEXT,
ADD COLUMN     "backgroundColor" TEXT,
ADD COLUMN     "buttonBackgroundColor" TEXT,
ADD COLUMN     "buttonBorderStyle" TEXT,
ADD COLUMN     "buttonTextColor" TEXT,
ADD COLUMN     "closeMessageDescription" TEXT,
ADD COLUMN     "closeMessageTitle" TEXT,
ADD COLUMN     "customHoneypot" TEXT,
ADD COLUMN     "customSuccessUrl" TEXT,
ADD COLUMN     "emailsToNotify" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "googleRecaptchaEnabled" BOOLEAN,
ADD COLUMN     "googleRecaptcha<PERSON><PERSON>retKey" TEXT,
ADD COLUMN     "headerDescription" TEXT,
ADD COLUMN     "headerImage" TEXT,
ADD COLUMN     "headerTitle" TEXT,
ADD COLUMN     "inputBorderStyle" TEXT,
ADD COLUMN     "ipBlacklist" TEXT,
ADD COLUMN     "isClosed" BOOLEAN,
ADD COLUMN     "limitResponses" BOOLEAN,
ADD COLUMN     "logo" TEXT,
ADD COLUMN     "maxResponses" INTEGER,
ADD COLUMN     "pageMode" TEXT,
ADD COLUMN     "removeFormboxBranding" BOOLEAN,
ADD COLUMN     "respondantEmailFromName" TEXT,
ADD COLUMN     "respondantEmailMessageHTML" TEXT,
ADD COLUMN     "respondantEmailSubject" TEXT,
ADD COLUMN     "saveAnswers" BOOLEAN,
ADD COLUMN     "sendEmailNotifications" BOOLEAN,
ADD COLUMN     "sendRespondantEmailNotifications" BOOLEAN,
ADD COLUMN     "showCustomClosedMessage" BOOLEAN,
ADD COLUMN     "smtpEnabled" BOOLEAN,
ADD COLUMN     "smtpHost" TEXT,
ADD COLUMN     "smtpPassword" TEXT,
ADD COLUMN     "smtpPort" INTEGER,
ADD COLUMN     "smtpSenderEmail" TEXT,
ADD COLUMN     "smtpUsername" TEXT,
ADD COLUMN     "submissionStorageDuration" TEXT,
ADD COLUMN     "submitButtonText" TEXT,
ADD COLUMN     "textColor" TEXT,
ADD COLUMN     "tpBackgroundColor" TEXT,
ADD COLUMN     "tpButtonBackgroundColor" TEXT,
ADD COLUMN     "tpButtonColor" TEXT,
ADD COLUMN     "tpButtonText" TEXT,
ADD COLUMN     "tpButtonUrl" TEXT,
ADD COLUMN     "tpHeader" TEXT,
ADD COLUMN     "tpMessage" TEXT,
ADD COLUMN     "tpTextColor" TEXT,
ADD COLUMN     "useCustomRedirect" BOOLEAN,
ADD COLUMN     "useCustomThankYouPage" BOOLEAN,
ADD COLUMN     "webhookEnabled" BOOLEAN,
ADD COLUMN     "webhookUrl" TEXT;
