// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

datasource db {
    provider  = "postgresql"
    url       = env("DATABASE_URL")
    directUrl = env("DIRECT_URL")
}

generator client {
    provider        = "prisma-client-js"
    previewFeatures = ["relationJoins", "fullTextSearchPostgres"]
}

model Account {
    id                    String    @id @default(cuid(2))
    accountId             String
    providerId            String
    userId                String
    user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    accessToken           String?
    refreshToken          String?
    idToken               String?
    accessTokenExpiresAt  DateTime?
    refreshTokenExpiresAt DateTime?
    scope                 String?
    password              String?
    createdAt             DateTime  @default(now())
    updatedAt             DateTime  @updatedAt

    @@index([userId])
    @@map("accounts")
}

model User {
    id            String       @id @default(cuid(2))
    name          String?
    email         String?      @unique
    emailVerified Boolean?
    image         String?
    createdAt     DateTime     @default(now())
    updatedAt     DateTime     @updatedAt
    accounts      Account[]
    members       Member[]
    invitations   Invitation[]
    passkeys      Passkey[]
    sessions      Session[]

    @@map("users")
}

model Session {
    id                   String   @id @default(cuid(2))
    expiresAt            DateTime
    token                String
    createdAt            DateTime @default(now())
    updatedAt            DateTime @updatedAt
    ipAddress            String?
    userAgent            String?
    userId               String
    user                 User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    activeOrganizationId String?

    @@unique([token])
    @@map("sessions")
}

model Verification {
    id         String   @id @default(cuid(2))
    identifier String
    value      String
    expiresAt  DateTime
    createdAt  DateTime @default(now())
    updatedAt  DateTime @updatedAt

    @@map("verifications")
}

model Passkey {
    id           String   @id @default(cuid(2))
    name         String?
    publicKey    String
    userId       String
    user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
    credentialID String
    counter      Int
    deviceType   String
    backedUp     Boolean
    transports   String?
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt

    @@index([userId])
    @@map("passkeys")
}

model ApiKey {
    id             String        @id @default(cuid(2))
    name           String?
    createdAt      DateTime      @default(now())
    updatedAt      DateTime      @updatedAt
    partialKey     String
    expires        DateTime?
    key            String        @unique
    scopes         String[]
    organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId String

    @@index([organizationId])
    @@map("apiKeys")
}

model Organization {
    id                       String       @id @default(cuid(2))
    name                     String
    slug                     String?
    logo                     String?
    createdAt                DateTime     @default(now())
    updatedAt                DateTime     @updatedAt
    metadata                 String?
    stripeCustomerId         String?
    stripeSubscriptionId     String?
    stripeSubscriptionStatus String?
    stripePlanNickname       String?
    stripePlan               String?
    stripeCancelAtPeriodEnd  Boolean?     @default(false)
    stripeCurrentPeriodEnd   DateTime?
    stripeTrialEnd           DateTime?
    members                  Member[]
    invitations              Invitation[]
    apiKeys                  ApiKey[]
    forms                    Form[]
    templates                Template[]

    @@unique([slug])
    @@map("organizations")
}

model Member {
    id             String       @id @default(cuid(2))
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String
    user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
    role           String
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@unique([userId, organizationId], name: "member_userId_organizationId_key")
    @@map("members")
}

model Invitation {
    id             String       @id @default(cuid(2))
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    email          String
    role           String?
    status         String
    expiresAt      DateTime
    inviterId      String
    user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)
    createdAt      DateTime     @default(now())

    @@unique([email, organizationId])
    @@map("invitations")
}

model Form {
    id                               String        @id @default(cuid(2))
    name                             String
    organization                     Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId                   String
    removeFormboxBranding            Boolean       @default(false)
    sendEmailNotifications           Boolean       @default(true)
    emailsToNotify                   String[]
    submissionStorageDuration        String        @default("60")
    sendRespondantEmailNotifications Boolean       @default(false)
    respondantEmailFromName          String        @default("")
    respondantEmailSubject           String        @default("")
    respondantEmailMessageHTML       String        @default("")
    submitButtonText                 String        @default("Submit")
    limitResponses                   Boolean       @default(false)
    isClosed                         Boolean       @default(false)
    autoCloseEnabled                 Boolean       @default(false)
    autoCloseDate                    DateTime?
    autoCloseTime                    String?
    autoCloseTimezone                String?
    maxResponses                     Int?
    useCustomRedirect                Boolean       @default(false)
    customSuccessUrl                 String        @default("")
    webhookEnabled                   Boolean       @default(false)
    webhookUrl                       String        @default("")
    customHoneypot                   String        @default("")
    googleRecaptchaEnabled           Boolean       @default(false)
    googleRecaptchaSecretKey         String        @default("")
    allowedDomains                   String        @default("")
    allowedCountries                 String        @default("")
    createdAt                        DateTime      @default(now())
    updatedAt                        DateTime      @updatedAt
    type                             String        @default("endpoint")
    fields                           Json          @default("[]")
    accentColor                      String        @default("#030712")
    backgroundColor                  String        @default("#ffffff")
    buttonBackgroundColor            String        @default("#030712")
    buttonBorderStyle                String        @default("rounded")
    buttonTextColor                  String        @default("#ffffff")
    closeMessageDescription          String        @default("")
    closeMessageTitle                String        @default("")
    headerDescription                String        @default("")
    headerImage                      String        @default("")
    headerTitle                      String        @default("")
    inputBorderStyle                 String        @default("rounded")
    logo                             String        @default("")
    pageMode                         String        @default("compact")
    saveAnswers                      Boolean       @default(false)
    showCustomClosedMessage          Boolean       @default(false)
    textColor                        String        @default("#000000")
    tpBackgroundColor                String        @default("#ffffff")
    tpButtonColor                    String        @default("#030712")
    tpButtonText                     String        @default("")
    tpButtonUrl                      String        @default("")
    tpHeader                         String        @default("")
    tpMessage                        String        @default("")
    tpTextColor                      String        @default("#030712")
    useCustomThankYouPage            Boolean       @default(false)
    tpButtonBackgroundColor          String        @default("#f3f4f6")
    smtpHost                         String?
    smtpPassword                     String?
    smtpPort                         Int?
    smtpUsername                     String?
    smtpEnabled                      Boolean       @default(false)
    smtpSenderEmail                  String?
    ipBlacklist                      String        @default("")
    files                            File[]
    integrations                     Integration[]
    submissions                      Submission[]

    @@index([organizationId])
    @@map("forms")
}

model Submission {
    id          String   @id @default(cuid(2))
    formId      String
    isSpam      Boolean  @default(false)
    isProcessed Boolean  @default(false)
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt
    browser     String   @default("")
    countryCode String   @default("")
    ipAddress   String   @default("")
    os          String   @default("")
    platform    String   @default("")
    answers     Answer[]
    files       File[]
    form        Form     @relation(fields: [formId], references: [id], onDelete: Cascade)

    @@index([formId])
    @@map("submissions")
}

model Answer {
    id           String     @id @default(cuid(2))
    label        String
    value        String
    submissionId String
    createdAt    DateTime   @default(now())
    updatedAt    DateTime   @updatedAt
    submission   Submission @relation(fields: [submissionId], references: [id], onDelete: Cascade)

    @@index([submissionId])
    @@map("answers")
}

model File {
    id            String     @id @default(cuid(2))
    name          String
    type          String
    size          Int
    url           String
    key           String
    submissionId  String
    createdAt     DateTime   @default(now())
    updatedAt     DateTime   @updatedAt
    formId        String
    formFieldName String?
    form          Form       @relation(fields: [formId], references: [id], onDelete: Cascade)
    submission    Submission @relation(fields: [submissionId], references: [id], onDelete: Cascade)

    @@index([formId])
    @@index([submissionId])
    @@map("files")
}

model Integration {
    id              String   @id @default(cuid(2))
    formId          String
    connectionId    String?  @default("")
    type            String
    isEnabled       Boolean  @default(false)
    spreadsheetId   String?  @default("")
    slackTeamId     String?  @default("")
    slackTeamName   String?  @default("")
    createdAt       DateTime @default(now())
    updatedAt       DateTime @updatedAt
    orgId           String
    organizationId  String?
    airtableBaseId  String?  @default("")
    airtableTableId String?  @default("")
    webhookUrl      String?  @default("")
    mailchimpDC     String?  @default("")
    mailchimpListId String?  @default("")
    slackChannelId  String?  @default("")
    excelWebUrl     String?  @default("")
    form            Form     @relation(fields: [formId], references: [id], onDelete: Cascade)

    @@index([formId])
    @@map("integrations")
}

model Template {
    id                String        @id @default(cuid(2))
    name              String
    description       String        @default("")
    fields            Json          @default("[]")
    category          String        @default("")
    isFormboxTemplate Boolean       @default(false)
    organization      Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId    String?

    // Form settings and customization options - with defaults matching Form model
    removeFormboxBranding            Boolean   @default(false)
    sendEmailNotifications           Boolean   @default(true)
    emailsToNotify                   String[]  @default([])
    submissionStorageDuration        String    @default("60")
    sendRespondantEmailNotifications Boolean   @default(false)
    respondantEmailFromName          String    @default("")
    respondantEmailSubject           String    @default("")
    respondantEmailMessageHTML       String    @default("")
    submitButtonText                 String    @default("Submit")
    limitResponses                   Boolean   @default(false)
    isClosed                         Boolean   @default(false)
    autoCloseEnabled                 Boolean   @default(false)
    autoCloseDate                    DateTime?
    autoCloseTime                    String?
    autoCloseTimezone                String?
    maxResponses                     Int?
    useCustomRedirect                Boolean   @default(false)
    customSuccessUrl                 String    @default("")
    webhookEnabled                   Boolean   @default(false)
    webhookUrl                       String    @default("")
    customHoneypot                   String    @default("")
    googleRecaptchaEnabled           Boolean   @default(false)
    googleRecaptchaSecretKey         String    @default("")
    allowedDomains                   String    @default("")
    allowedCountries                 String    @default("")

    // Styling options - with defaults matching Form model
    accentColor             String  @default("#030712")
    backgroundColor         String  @default("#ffffff")
    buttonBackgroundColor   String  @default("#030712")
    buttonBorderStyle       String  @default("rounded")
    buttonTextColor         String  @default("#ffffff")
    closeMessageDescription String  @default("")
    closeMessageTitle       String  @default("")
    headerDescription       String  @default("")
    headerImage             String  @default("")
    headerTitle             String  @default("")
    inputBorderStyle        String  @default("rounded")
    logo                    String  @default("")
    pageMode                String  @default("compact")
    saveAnswers             Boolean @default(false)
    showCustomClosedMessage Boolean @default(false)
    textColor               String  @default("#000000")
    tpBackgroundColor       String  @default("#ffffff")
    tpButtonColor           String  @default("#030712")
    tpButtonText            String  @default("")
    tpButtonUrl             String  @default("")
    tpHeader                String  @default("")
    tpMessage               String  @default("")
    tpTextColor             String  @default("#030712")
    useCustomThankYouPage   Boolean @default(false)
    tpButtonBackgroundColor String  @default("#f3f4f6")

    // SMTP settings - with defaults matching Form model
    smtpHost        String?
    smtpPassword    String?
    smtpPort        Int?
    smtpUsername    String?
    smtpEnabled     Boolean @default(false)
    smtpSenderEmail String?
    ipBlacklist     String  @default("")

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([organizationId])
    @@index([category])
    @@index([isFormboxTemplate])
    @@map("templates")
}
