{"name": "formbox", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "better-auth:generate": "npx @better-auth/cli generate --config ./src/libs/auth.ts", "db:push": "prisma db push", "db:deploy": "prisma migrate deploy", "db:generate": "prisma generate", "db:update": "bun run db:push && bun run db:generate", "db:studio": "prisma studio", "db:local": "npx supabase start", "stripe:listen": "stripe listen --forward-to localhost:3000/api/webhooks/stripe", "preview-emails": "email dev --dir ./src/emails --port 3333", "postinstall": "prisma generate", "lint": "next lint", "start": "next start", "generate-openapi": "bun ./scripts/generate-openapi.ts", "update-packages": "bun update -i"}, "dependencies": {"@arcjet/ip": "1.0.0-beta.10", "@arcjet/next": "1.0.0-beta.10", "@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.864.0", "@better-fetch/fetch": "^1.1.18", "@dnd-kit/core": "^6.3.1", "@dnd-kit/helpers": "^0.1.21", "@dnd-kit/react": "^0.1.21", "@faker-js/faker": "^9.9.0", "@headlessui/react": "^2.2.7", "@hookform/resolvers": "^5.2.1", "@json2csv/plainjs": "^7.0.6", "@mantine/hooks": "^8.2.5", "@nangohq/frontend": "0.41.1", "@nangohq/node": "0.41.1", "@posthog/nextjs-config": "^1.1.2", "@prisma/client": "^6.14.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@react-email/components": "^0.5.0", "@react-email/tailwind": "^1.1.0", "@t3-oss/env-nextjs": "^0.13.8", "@tabler/icons-react": "^3.34.1", "@tanstack/react-query": "^5.85.3", "@tanstack/react-query-devtools": "^5.85.3", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-link": "^3.2.0", "@tiptap/extension-placeholder": "^3.2.0", "@tiptap/extension-text-align": "^3.2.0", "@tiptap/extension-text-style": "^3.2.0", "@tiptap/pm": "^3.2.0", "@tiptap/react": "^3.2.0", "@tiptap/starter-kit": "^3.2.0", "@trpc/client": "^11.4.4", "@trpc/next": "^11.4.4", "@trpc/react-query": "^11.4.4", "@trpc/server": "^11.4.4", "@upstash/qstash": "^2.8.2", "@upstash/ratelimit": "^2.0.6", "@upstash/redis": "^1.35.3", "@vercel/functions": "^2.2.12", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "better-auth": "^1.3.7", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "immer": "^10.1.3", "libphonenumber-js": "^1.12.12", "lodash.debounce": "^4.0.8", "lucide-react": "^0.539.0", "nanoid": "^5.1.5", "next": "^15.4.6", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "nodemailer": "^7.0.5", "posthog-js": "^1.260.1", "posthog-node": "^5.7.0", "prism-react-renderer": "^2.4.1", "qrcode": "^1.5.4", "radash": "^12.1.1", "react": "^19.1.1", "react-color": "^2.19.3", "react-day-picker": "^9.9.0", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-email": "^4.2.8", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-select": "^5.10.1", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.9", "recharts": "^3.1.2", "resend": "^6.0.1", "sonner": "^2.0.7", "stripe": "^18.4.0", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "unsplash-js": "^7.0.19", "vaul": "^1.1.2", "zod": "3.25.76", "zod-error": "^1.5.0", "zod-openapi": "4.2.4", "zustand": "^5.0.7"}, "devDependencies": {"@headlessui/tailwindcss": "^0.2.2", "@react-email/preview-server": "4.2.8", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.12", "@tailwindcss/typography": "^0.5.16", "@types/bcryptjs": "^3.0.0", "@types/eslint": "^9.6.1", "@types/lodash.debounce": "^4.0.9", "@types/node": "^24.3.0", "@types/nodemailer": "^7.0.0", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.10", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.1.7", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "eslint": "^9.33.0", "eslint-config-next": "^15.4.6", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "prisma": "^6.14.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.12", "tsx": "^4.20.4", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2"}, "ct3aMetadata": {"initVersion": "7.26.0"}}