/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable @typescript-eslint/ban-ts-comment */
//@ts-nocheck

(function () {
  "use strict";

  /**
   * FormBox embed script configuration
   * Contains all the default settings and URLs for the embed functionality
   */
  const EMBED_CONFIG = {
    version: "1.0.0",
    baseUrl: "https://app.formbox.app",
    defaultHeight: "600px", // Initial iframe height before dynamic sizing
    resizeThrottleMs: 100, // Throttle interval for resize operations to prevent excessive calls
    maxRetries: 5, // Maximum attempts to establish communication with iframe
    retryDelay: 500, // Delay between retry attempts in milliseconds
  };

  // Initialize or reference existing FormBox global object
  window.Formbox = window.Formbox || {};

  /**
   * Map to track all active form embeds and their current state
   * Key: formId, Value: { iframe, loaded, lastHeight, lastUpdate }
   */
  const activeEmbeds = new Map();

  /**
   * Throttle utility function to limit how often a function can be called
   * Prevents excessive API calls or DOM manipulations during rapid events
   * @param {Function} func - Function to throttle
   * @param {number} limit - Time limit in milliseconds
   * @returns {Function} Throttled version of the function
   */
  function throttle(func, limit) {
    let inThrottle;
    return function () {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  }

  /**
   * Handles incoming postMessage events from embedded iframes
   * Validates origin for security and routes messages to appropriate handlers
   * @param {MessageEvent} event - The message event from iframe
   */
  function handleMessage(event) {
    // Security check: Only accept messages from trusted FormBox domains
    const isValidOrigin =
      event.origin.includes("formbox.app") ||
      event.origin.includes("localhost:3000") ||
      event.origin.includes("127.0.0.1:3000");

    if (!isValidOrigin) return;

    const data = event.data;
    console.log("Received message:", data);

    // Route message to appropriate handler based on type
    switch (data.type) {
      case "formbox-resize":
        handleResize(data);
        break;
      case "formbox-loaded":
        handleFormLoaded(data);
        break;
      case "formbox-height-changed":
        handleHeightChanged(data);
        break;
    }
  }

  /**
   * Handles iframe resize requests from embedded forms
   * Updates iframe height and maintains state tracking
   * @param {Object} data - Message data containing formId and height
   */
  function handleResize(data) {
    const iframe = document.querySelector(
      `iframe[data-formbox-id="${data.formId}"]`,
    );

    console.log("Handling resize for iframe:", iframe, "height:", data.height);

    if (iframe && data.height) {
      const embedState = activeEmbeds.get(data.formId);
      // Apply exact height without minimum constraints for smooth embedding
      const newHeight = data.height;

      iframe.style.height = newHeight + "px";
      console.log("Updated iframe height to:", newHeight + "px");

      // Update tracking state with new height and timestamp
      activeEmbeds.set(data.formId, {
        ...embedState,
        lastHeight: newHeight,
        lastUpdate: Date.now(),
      });

      // Dispatch custom event for external listeners (analytics, debugging, etc.)
      iframe.dispatchEvent(
        new CustomEvent("formbox-height-changed", {
          detail: { height: newHeight, formId: data.formId },
        }),
      );
    }
  }

  /**
   * Handles form loaded confirmation from iframe
   * Initializes state tracking and requests initial height measurement
   * @param {Object} data - Message data containing formId
   */
  function handleFormLoaded(data) {
    const iframe = document.querySelector(
      `iframe[data-formbox-id="${data.formId}"]`,
    );

    console.log("Form loaded for iframe:", iframe);

    if (iframe) {
      // Register embed as loaded and initialize state
      activeEmbeds.set(data.formId, {
        iframe: iframe,
        loaded: true,
        lastHeight: 0,
        lastUpdate: Date.now(),
      });

      // Request initial height after a brief delay to allow form rendering
      setTimeout(() => requestHeight(iframe, data.formId), 100);
    }
  }

  /**
   * Alias for handleResize to maintain consistency across message types
   * @param {Object} data - Message data containing formId and height
   */
  function handleHeightChanged(data) {
    handleResize(data);
  }

  /**
   * Sends height request to iframe with exponential backoff retry logic
   * Handles communication failures gracefully with automatic retries
   * @param {HTMLIFrameElement} iframe - The iframe element to request height from
   * @param {string} formId - Unique identifier for the form
   * @param {number} retryCount - Current retry attempt number
   */
  function requestHeight(iframe, formId, retryCount = 0) {
    // Stop retrying after maximum attempts to prevent infinite loops
    if (retryCount >= EMBED_CONFIG.maxRetries) {
      console.warn(
        `Failed to get height for form ${formId} after ${EMBED_CONFIG.maxRetries} attempts`,
      );
      return;
    }

    const iframeWindow = iframe.contentWindow;
    if (iframeWindow) {
      try {
        // Send height request message to iframe content
        iframeWindow.postMessage(
          {
            type: "formbox-height-request",
            formId: formId,
            timestamp: Date.now(),
          },
          "*",
        );
      } catch (error) {
        console.warn("Failed to send height request:", error);
        // Retry with exponential backoff on communication failure
        setTimeout(() => {
          requestHeight(iframe, formId, retryCount + 1);
        }, EMBED_CONFIG.retryDelay);
      }
    } else {
      // Iframe not ready, retry after delay
      setTimeout(() => {
        requestHeight(iframe, formId, retryCount + 1);
      }, EMBED_CONFIG.retryDelay);
    }
  }

  /**
   * Extracts form ID from FormBox embed URL
   * Parses the URL to find the unique form identifier
   * @param {string} url - The embed URL
   * @returns {string|null} The extracted form ID or null if not found
   */
  function extractFormId(url) {
    const match = url.match(/\/embed\/([^/?]+)/);
    return match ? match[1] : null;
  }

  /**
   * Applies consistent default styling to iframe elements
   * Ensures proper appearance and responsive behavior
   * @param {HTMLIFrameElement} iframe - The iframe element to style
   */
  function applyDefaultStyles(iframe) {
    iframe.style.border = "0"; // Remove default border
    iframe.style.width = iframe.style.width || "100%"; // Ensure full width
    iframe.style.transition = "height 0.3s ease-in-out"; // Smooth height transitions
    iframe.style.height = "400px"; // Initial height before dynamic sizing
    iframe.style.overflow = "hidden"; // Prevent scrollbars within iframe
  }

  /**
   * Sets up dynamic height functionality for an embed
   * Initializes state tracking and establishes communication channels
   * @param {HTMLIFrameElement} iframe - The iframe element
   * @param {string} formId - Unique identifier for the form
   */
  function setupDynamicHeight(iframe, formId) {
    console.log("Setting up dynamic height for:", formId);

    // Initialize tracking state for this embed
    activeEmbeds.set(formId, {
      iframe: iframe,
      loaded: false,
      lastHeight: 400,
      lastUpdate: Date.now(),
    });

    // Set up iframe load event handler
    iframe.addEventListener("load", () => {
      console.log("Iframe loaded for:", formId);

      // Wait for content to fully render before requesting height
      setTimeout(() => {
        requestHeight(iframe, formId);

        // Set up periodic height checks for forms with dynamic content
        const intervalId = setInterval(() => {
          const embedState = activeEmbeds.get(formId);
          if (embedState && embedState.loaded) {
            requestHeight(iframe, formId);
          } else {
            // Clean up interval if embed is no longer active
            clearInterval(intervalId);
          }
        }, 3000); // Check every 3 seconds for height changes

        // Automatic cleanup after 10 minutes to prevent memory leaks
        setTimeout(() => {
          clearInterval(intervalId);
        }, 600000);
      }, 500);
    });
  }

  /**
   * Initializes all FormBox embeds found on the page
   * Scans for iframes with data-formbox-src attribute and sets them up
   */
  function initializeEmbeds() {
    // Find all uninitialized FormBox embeds
    const embeds = document.querySelectorAll(
      "iframe[data-formbox-src]:not([src])",
    );

    embeds.forEach(function (iframe) {
      const src = iframe.dataset.formboxSrc;
      const formId = extractFormId(src);

      console.log("iframe: ", iframe);

      if (src && formId) {
        // Set unique identifier for tracking this embed
        iframe.setAttribute("data-formbox-id", formId);

        // Load the form using the provided URL (with all query parameters)
        iframe.src = src;

        // Apply consistent styling
        applyDefaultStyles(iframe);

        // Enable dynamic height functionality for all embeds
        setupDynamicHeight(iframe, formId);
      }
    });
  }

  /**
   * Legacy auto-resize functionality for backward compatibility
   * Attempts to measure iframe content height directly (same-origin only)
   */
  function autoResize() {
    // Find iframes specifically marked for auto-resize
    const iframes = document.querySelectorAll(
      'iframe[data-formbox-src][data-auto-resize="1"]',
    );

    iframes.forEach(function (iframe) {
      const formId = iframe.dataset.formboxId;
      const embedState = activeEmbeds.get(formId);

      try {
        // Attempt direct content measurement (same-origin only)
        const doc = iframe.contentDocument || iframe.contentWindow.document;
        if (doc) {
          const height = Math.max(
            doc.body.scrollHeight,
            doc.documentElement.scrollHeight,
            200, // Minimum height
          );

          // Only update if height changed significantly to avoid flickering
          if (
            height > 0 &&
            (!embedState || Math.abs(embedState.lastHeight - height) > 5)
          ) {
            iframe.style.height = Math.min(height, 5000) + "px"; // Cap at 5000px

            // Update state tracking
            if (embedState) {
              embedState.lastHeight = height;
              embedState.lastUpdate = Date.now();
            }
          }
        }
      } catch (e) {
        // Cross-origin restrictions prevent direct access, fall back to postMessage
        if (formId) {
          requestHeight(iframe, formId);
        }
      }
    });
  }

  // Create throttled version of autoResize to prevent excessive calls
  const throttledResize = throttle(autoResize, EMBED_CONFIG.resizeThrottleMs);

  /**
   * Cleanup function to remove all embed state tracking
   * Prevents memory leaks when page is unloaded
   */
  function cleanup() {
    activeEmbeds.clear();
  }

  /**
   * Public API for FormBox embed functionality
   * Exposed as window.Formbox for external access
   */
  window.Formbox = {
    version: EMBED_CONFIG.version,

    /**
     * Initialize all FormBox embeds on the page
     */
    loadEmbeds: function () {
      initializeEmbeds();
    },

    /**
     * Initialize a specific embed by CSS selector
     * @param {string} selector - CSS selector for the iframe
     */
    loadEmbed: function (selector) {
      const iframe = document.querySelector(selector);
      if (iframe && iframe.dataset.formboxSrc && !iframe.src) {
        const src = iframe.dataset.formboxSrc;
        const formId = extractFormId(src);

        if (formId) {
          iframe.setAttribute("data-formbox-id", formId);
          iframe.src = src;
          applyDefaultStyles(iframe);
          setupDynamicHeight(iframe, formId);
        }
      }
    },

    /**
     * Manually trigger resize for all auto-resize embeds
     */
    resizeAll: function () {
      throttledResize();
    },

    /**
     * Get current state information for a specific embed
     * @param {string} formId - The form identifier
     * @returns {Object|undefined} Embed state object
     */
    getEmbedState: function (formId) {
      return activeEmbeds.get(formId);
    },

    /**
     * Manually request height update for a specific embed
     * @param {string} formId - The form identifier
     */
    updateHeight: function (formId) {
      const embedState = activeEmbeds.get(formId);
      if (embedState && embedState.iframe) {
        requestHeight(embedState.iframe, formId);
      }
    },

    /**
     * Clean up all embed state (for page unload)
     */
    cleanup: cleanup,
  };

  // Auto-initialize embeds when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeEmbeds);
  } else {
    // DOM already loaded, initialize immediately
    initializeEmbeds();
  }

  // Set up global message listener for iframe communication
  window.addEventListener("message", handleMessage);

  // Set up window resize listener for legacy auto-resize functionality
  if (window.addEventListener) {
    window.addEventListener("resize", throttledResize);
  } else if (window.attachEvent) {
    // IE8 compatibility
    window.attachEvent("onresize", throttledResize);
  }

  // Clean up embed state when page is unloaded to prevent memory leaks
  window.addEventListener("beforeunload", cleanup);
})();
