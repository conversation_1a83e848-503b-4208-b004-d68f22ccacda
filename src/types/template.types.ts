import type { FormField } from "@/types/form.types";

export type Template = {
  id: string;
  name: string;
  description: string;
  category: string;
  isFormboxTemplate: boolean;
  organizationId: string | null;
  createdAt: string | Date;
  updatedAt: string | Date;
  fields?: FormField[];
  // Form settings - required fields with defaults
  removeFormboxBranding: boolean;
  sendEmailNotifications: boolean;
  emailsToNotify: string[];
  submissionStorageDuration: string;
  sendRespondantEmailNotifications: boolean;
  respondantEmailFromName: string;
  respondantEmailSubject: string;
  respondantEmailMessageHTML: string;
  submitButtonText: string;
  limitResponses: boolean;
  isClosed: boolean;
  autoCloseEnabled: boolean;
  autoCloseDate?: Date;
  autoCloseTime?: string;
  autoCloseTimezone?: string;
  maxResponses?: number;
  useCustomRedirect: boolean;
  customSuccessUrl: string;
  webhookEnabled: boolean;
  webhookUrl: string;
  customHoneypot: string;
  googleRecaptchaEnabled: boolean;
  googleRecaptchaSecretKey: string;
  allowedDomains: string;
  allowedCountries: string;
  ipBlacklist: string;
  // Styling options - required fields with defaults
  accentColor: string;
  backgroundColor: string;
  buttonBackgroundColor: string;
  buttonBorderStyle: string;
  buttonTextColor: string;
  closeMessageDescription: string;
  closeMessageTitle: string;
  headerDescription: string;
  headerImage: string;
  headerTitle: string;
  inputBorderStyle: string;
  logo: string;
  pageMode: string;
  saveAnswers: boolean;
  showCustomClosedMessage: boolean;
  textColor: string;
  tpBackgroundColor: string;
  tpButtonColor: string;
  tpButtonText: string;
  tpButtonUrl: string;
  tpHeader: string;
  tpMessage: string;
  tpTextColor: string;
  useCustomThankYouPage: boolean;
  tpButtonBackgroundColor: string;
  // SMTP settings - nullable fields
  smtpHost?: string;
  smtpPassword?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpEnabled: boolean;
  smtpSenderEmail?: string;
};
