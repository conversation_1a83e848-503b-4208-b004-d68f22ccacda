import { type auth } from "@/libs/auth";
import {
  type LoginSchema,
  type SignupSchema,
} from "@/libs/schemas/auth.schemas";
import { type z } from "@/libs/zod";
import { type RouterOutputs } from "@/trpc/react";

export type Session = typeof auth.$Infer.Session;

export type LoginFormData = z.infer<typeof LoginSchema>;

export type SignupFormData = z.infer<typeof SignupSchema>;

export type Passkey = NonNullable<
  RouterOutputs["user"]["getUser"]
>["passkeys"][0];
