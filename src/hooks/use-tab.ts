"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export type UseSearchTabOptions<T extends string> = {
  param?: string;
  params?: T[];
  defaultValue?: T;
};

/**
 * A custom hook for managing tab state with URL search parameters.
 *
 * @template T - The type of the tab value
 * @param {UseSearchTabOptions<T>} opts - Configuration options for the tab hook
 * @param {string} opts.param - The URL parameter name for the tab (default: "tab")
 * @param {T[]} opts.params - The allowed tab values (default: ["formbox", "organization"])
 * @param {T} opts.defaultValue - The default tab value (default: first item in params)
 *
 * @returns A tuple containing:
 * - The current tab value
 * - A function to set the tab value
 *
 * @example
 * ```tsx
 * const [tab, setTab] = useSearchTab({ param: "view", params: ["grid", "list"] });
 * ```
 */
export function useSearchTab<T extends string = string>(
  opts: UseSearchTabOptions<T> = {},
): [T, (v: T) => void] {
  const {
    param = "tab",
    params,
    defaultValue,
  } = opts as UseSearchTabOptions<T> & {
    params: T[] | undefined;
    defaultValue: T | undefined;
  };

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const resolvedAllowed =
    (params as T[]) ?? (["formbox", "organization"] as unknown as T[]);
  const resolvedDefault = (defaultValue as T) ?? (resolvedAllowed[0] as T);

  const getFromSearch = (params: URLSearchParams | null) => {
    const t = params?.get(param) as T | null;
    return t && resolvedAllowed.includes(t) ? t : resolvedDefault;
  };

  const [value, setValueState] = useState<T>(() =>
    getFromSearch(searchParams as unknown as URLSearchParams),
  );

  // Sync when searchParams change (user navigates/back/forward)
  useEffect(() => {
    setValueState(getFromSearch(searchParams as unknown as URLSearchParams));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams?.toString?.()]);

  const setValue = (v: T) => {
    setValueState(v);
    const params = new URLSearchParams(
      Array.from((searchParams ?? new URLSearchParams()).entries()),
    );
    params.set(param, String(v));
    router.replace(`${pathname}?${params.toString()}`);
  };

  return [value, setValue];
}
