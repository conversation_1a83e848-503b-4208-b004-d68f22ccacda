import { useFormStore } from "@/stores/form.store";
import { type FormField } from "@/types/form.types";

/**
 * Hook that provides common field actions for form elements.
 * Eliminates the need for prop drilling and duplicate logic across element components.
 *
 * @param fieldId - The ID of the field to operate on
 * @returns Object containing common field actions
 */
export function useFieldActions(fieldId: string) {
  const { duplicateField, removeField, updateFieldProperty, select } =
    useFormStore();

  /**
   * Duplicate the field and place it right after the current field.
   * The new field will be automatically selected.
   */
  const duplicate = () => duplicateField(fieldId);

  /**
   * Remove the field from the form.
   */
  const remove = () => removeField(fieldId);

  /**
   * Update a specific property of the field.
   *
   * @param property - The property name to update (must be a key of FormField)
   * @param value - The new value for the property
   */
  const updateProperty = (property: keyof FormField, value: unknown) =>
    updateFieldProperty(fieldId, property, value);

  /**
   * Select this field (make it the active/selected field).
   */
  const selectField = () => select(fieldId);

  // Convenience methods for common property updates
  /**
   * Update the field's label.
   */
  const updateLabel = (label: string) => updateProperty("label", label);

  /**
   * Update the field's description.
   */
  const updateDescription = (description: string) =>
    updateProperty("description", description);

  /**
   * Toggle the field's required status.
   */
  const toggleRequired = () => {
    // We need to get the current field to toggle its required status
    const { form } = useFormStore.getState();
    const field = form?.fields.find((f) => f.id === fieldId);
    if (field) {
      updateProperty("required", !field.required);
    }
  };

  /**
   * Toggle the field's showDescription status.
   */
  const toggleShowDescription = () => {
    // We need to get the current field to toggle its showDescription status
    const { form } = useFormStore.getState();
    const field = form?.fields.find((f) => f.id === fieldId);
    if (field) {
      updateProperty("showDescription", !field.showDescription);
    }
  };

  return {
    duplicate,
    remove,
    updateProperty,
    selectField,
    // Convenience methods
    updateLabel,
    updateDescription,
    toggleRequired,
    toggleShowDescription,
  };
}
