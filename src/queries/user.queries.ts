import {
  removePasskey,
  sendChangeEmailVerificationEmail,
  sendPasswordResetEmail,
} from "@/libs/auth-client";
import { api } from "@/trpc/react";
import type { UserWithAccounts } from "@/types/user.types";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

export const useUser = (initialData?: UserWithAccounts) => {
  const user = api.user.getUser.useQuery(undefined, { initialData });
  return { user: user.data, isLoading: user.isLoading };
};

export const useAuthUser = () => {
  const user = api.user.getUser.useQuery();
  return user.data;
};

export const useUserUpdateMutation = (
  options: { showToast: boolean } = { showToast: true },
) => {
  const apiUtils = api.useUtils();

  return api.user.updateUser.useMutation({
    onMutate: async () => {
      await apiUtils.user.getUser.cancel();
      const previousQueryData = apiUtils.user.getUser.getData();
      return { previousQueryData };
    },
    onSuccess: () => {
      if (options.showToast) {
        toast.success("Account updated");
      }
    },
    onError: (error, _, ctx) => {
      console.log(error);
      apiUtils.user.getUser.setData(void {}, ctx?.previousQueryData);
      toast.error("Something went wrong!", {
        description: "An error occured while trying to update your profile.",
      });
    },
    onSettled: async () => {
      await apiUtils.user.getUser.invalidate();
    },
  });
};

export function useDeleteUserPasskeyMutation() {
  const apiUtils = api.useUtils();

  return useMutation({
    mutationFn: removePasskey,
    onSuccess: () => {
      toast.success("Passkey deleted successfully");
    },
    onError: (error) => {
      console.log(error);
      toast.error(error.message);
      toast.error("Something went wrong!", {
        description: error.message,
        closeButton: true,
      });
    },
    onSettled: async () => {
      await apiUtils.user.getUser.invalidate();
    },
  });
}

export function useUserPasswordResetMutation({
  onSuccess: customOnSuccess,
  onError: customOnError,
}: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
} = {}) {
  return useMutation({
    mutationFn: async (params: { email: string }) => {
      return await sendPasswordResetEmail(params.email);
    },
    onSuccess: () => {
      const defaultOnSuccess = () => {
        toast.success("Password reset email sent", {
          description: "Please check your inbox for the email.",
          closeButton: true,
          duration: 10000,
        });
      };
      if (customOnSuccess) {
        return customOnSuccess();
      }
      return defaultOnSuccess();
    },
    onError: (error) => {
      const defaultOnError = () => {
        console.log(error);
        toast.error(error.message);
        toast.error("Something went wrong!", {
          description: error.message,
          closeButton: true,
        });
      };
      if (customOnError) {
        return customOnError(error);
      }
      return defaultOnError();
    },
  });
}

export function useChangeEmailMutation({
  onSuccess: customOnSuccess,
  onError: customOnError,
}: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
} = {}) {
  return useMutation({
    mutationFn: async (params: { newEmail: string }) => {
      return await sendChangeEmailVerificationEmail(params.newEmail);
    },
    onSuccess: (response) => {
      if (response.error) {
        toast.error("Something went wrong!", {
          description: response.error.message,
          closeButton: true,
        });
        return;
      }

      const defaultOnSuccess = () => {
        toast.success("Email sent", {
          description: "Please check your inbox for the email.",
          closeButton: true,
          duration: 10000,
        });
      };
      if (customOnSuccess) {
        return customOnSuccess();
      }
      return defaultOnSuccess();
    },
    onError: (error) => {
      const defaultOnError = () => {
        console.log(error);
        toast.error(error.message);
        toast.error("Something went wrong!", {
          description: error.message,
          closeButton: true,
        });
      };
      if (customOnError) {
        return customOnError(error);
      }
      return defaultOnError();
    },
  });
}
