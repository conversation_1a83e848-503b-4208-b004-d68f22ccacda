import { api } from "@/trpc/react";
import { isEmpty } from "radash";
import { toast } from "sonner";

type QueryOptions = {
  refetchOnWindowFocus?: boolean;
};

type TemplateFilter = {
  category?: string;
  searchString?: string;
  take?: number;
  cursor?: string;
};

export const useFormboxTemplates = (filter: TemplateFilter = {}) => {
  return api.template.getAllFormboxTemplates.useInfiniteQuery(
    { ...filter },
    {
      getNextPageParam: (lastPage) => lastPage.cursor || undefined,
    },
  );
};

export const useOrgTemplates = (orgId: string, filter: TemplateFilter = {}) => {
  return api.template.getAllOrgTemplates.useInfiniteQuery(
    { orgId, ...filter },
    {
      enabled: !isEmpty(orgId),
      getNextPageParam: (lastPage) => lastPage.cursor || undefined,
    },
  );
};

export const useTemplateCategories = () => {
  return api.template.getTemplateCategories.useQuery();
};

export const useOrgTemplateCategories = (orgId: string) => {
  return api.template.getOrgCategories.useQuery(
    { orgId },
    {
      enabled: !isEmpty(orgId),
    },
  );
};

export const useTemplateById = (
  { id, orgId }: { id: string; orgId?: string },
  options: QueryOptions = { refetchOnWindowFocus: true },
) => {
  return api.template.getById.useQuery(
    { id, orgId },
    {
      enabled: !isEmpty(id),
      refetchOnWindowFocus: options.refetchOnWindowFocus,
    },
  );
};

export const useTemplateCreateMutation = () => {
  const apiUtils = api.useUtils();

  return api.template.create.useMutation({
    onMutate: async () => {
      await apiUtils.template.getAllOrgTemplates.cancel();
      const previousQueryData =
        apiUtils.template.getAllOrgTemplates.getInfiniteData();
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      if (input.organizationId) {
        apiUtils.template.getAllOrgTemplates.setInfiniteData(
          { orgId: input.organizationId },
          ctx?.previousQueryData,
        );
      }
      toast.error("Error", { description: error.message });
    },
    onSettled: async (data, error, input) => {
      if (input.organizationId) {
        await apiUtils.template.getAllOrgTemplates.invalidate({
          orgId: input.organizationId,
        });
      }
      await apiUtils.template.getAllFormboxTemplates.invalidate();
    },
  });
};

export const useTemplateDuplicateMutation = () => {
  const apiUtils = api.useUtils();

  return api.template.duplicate.useMutation({
    onMutate: async () => {
      await apiUtils.template.getAllOrgTemplates.cancel();
      const previousQueryData =
        apiUtils.template.getAllOrgTemplates.getInfiniteData();
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      if (input.organizationId) {
        apiUtils.template.getAllOrgTemplates.setInfiniteData(
          { orgId: input.organizationId },
          ctx?.previousQueryData,
        );
      }
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, _input) => {
      await apiUtils.template.getAllOrgTemplates.invalidate();
    },
  });
};

export const useTemplateUpdateMutation = (
  orgId?: string,
  options: { showToast: boolean; toastMessage: string } = {
    showToast: false,
    toastMessage: "Template updated successfully",
  },
) => {
  const apiUtils = api.useUtils();

  return api.template.updateById.useMutation({
    onMutate: async (input) => {
      await apiUtils.template.getById.cancel({ id: input.id, orgId });
      const previousQueryData = apiUtils.template.getById.getData({
        id: input.id,
        orgId,
      });
      return { previousQueryData };
    },
    onSuccess: () => {
      if (options.showToast) {
        return toast.success(options.toastMessage);
      }
    },
    onError: (error, input, ctx) => {
      console.log(error);
      apiUtils.template.getById.setData(
        { id: input.id, orgId },
        ctx?.previousQueryData,
      );
      toast.error("Error", { description: error.message });
    },
    onSettled: async (data, error, input) => {
      await apiUtils.template.getById.invalidate({ id: input.id, orgId });
      if (orgId) {
        await apiUtils.template.getAllOrgTemplates.invalidate({ orgId });
      }
      await apiUtils.template.getAllFormboxTemplates.invalidate();
    },
  });
};

export const useTemplateDeleteMutation = () => {
  const apiUtils = api.useUtils();

  return api.template.deleteById.useMutation({
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await apiUtils.template.getAllOrgTemplates.invalidate();
    },
  });
};
