"use client";

import { PageTitle } from "@/components/ui/page-title";
import { env } from "@/env";
import { useEffect } from "react";

// Declare global Formbox object
declare global {
  interface Window {
    Formbox?: {
      loadEmbeds(): void;
    };
  }
  let Formbox:
    | {
        loadEmbeds(): void;
      }
    | undefined;
}

export default function DevPage() {
  useEffect(() => {
    const script = `
      var d=document,w="${env.NEXT_PUBLIC_APP_URL}/scripts/embed.js",v=function(){"undefined"!=typeof Formbox?Formbox.loadEmbeds():d.querySelectorAll("iframe[data-formbox-src]:not([src])").forEach(function(e){e.src=e.dataset.formboxSrc})};if("undefined"!=typeof Formbox)v();else if(d.querySelector('script[src="'+w+'"]')==null){var s=d.createElement("script");s.src=w,s.onload=v,s.onerror=v,d.body.appendChild(s);}
    `;
    const scriptElement = document.createElement("script");
    scriptElement.innerHTML = script;
    document.body.appendChild(scriptElement);

    // Add debugging for iframe height changes
    const handleIframeHeightChange = (event: any) => {
      console.log("Iframe height changed:", event.detail);
    };

    document.addEventListener(
      "formbox-height-changed",
      handleIframeHeightChange,
    );

    return () => {
      document.body.removeChild(scriptElement);
      document.removeEventListener(
        "formbox-height-changed",
        handleIframeHeightChange,
      );
    };
  }, []);

  return (
    <div className="p-10">
      <PageTitle>Dev Page</PageTitle>

      <div className="mt-10">
        <h2 className="text-2xl font-semibold">Form Embed</h2>
        <p className="mt-2 text-gray-600">
          This page is for testing the form embed with dynamic height
          functionality.
        </p>
        <p className="mt-2 text-gray-600">
          <a
            href={`${env.NEXT_PUBLIC_APP_URL}/embed/ys94l4jostrmuxkqzvv5vbwk?transparentBackground=1&dynamicHeight=1`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline"
          >
            Open embed directly
          </a>
        </p>

        <div className="mt-10">
          <h3 className="text-xl font-semibold">Dynamic Height Embed Test</h3>
          <div className="mt-4">
            <iframe
              data-formbox-src={`${env.NEXT_PUBLIC_APP_URL}/embed/ys94l4jostrmuxkqzvv5vbwk?transparentBackground=1&dynamicHeight=1&isEmbedded=1&hideTitle=1&alignLeft=1`}
              loading="lazy"
              width="100%"
              title="Contact form"
            ></iframe>
          </div>
          <p className="mt-2 text-sm text-gray-600">
            The iframe should automatically resize based on the form content.
            Check the browser console for debugging information.
          </p>
        </div>
      </div>
    </div>
  );
}
