import { FormBuildViewV2 } from "@/components/forms/form-build-view-v2";
import { api } from "@/trpc/server";
import { type Metadata } from "next";

interface Props {
  params: Promise<{ formId: string; orgId: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const paramsData = await params;

  const form = await api.form.getById({
    id: paramsData.formId,
    orgId: paramsData.orgId,
  });
  return {
    title: `Build - ${form?.name}`,
  };
}

export default async function BuildPage({ params }: Props) {
  const paramsData = await params;
  return (
    <div>
      <FormBuildViewV2 formId={paramsData.formId} orgId={paramsData.orgId} />
    </div>
  );
}
