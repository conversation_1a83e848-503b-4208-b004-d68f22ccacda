// app/global-error.jsx

"use client"; // Error boundaries must be Client Components

import { ErrorView } from "@/components/error/error-view";
import posthog from "posthog-js";
import { useEffect } from "react";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    posthog.captureException(error);
  }, [error]);

  return (
    // global-error must include html and body tags
    <html>
      <body>
        <ErrorView reset={reset} message={error.message} />
      </body>
    </html>
  );
}
