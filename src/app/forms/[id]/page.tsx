import { FormPreview } from "@/components/forms/form-preview";
import { api } from "@/trpc/server";
import { type FormByIdOutput } from "@/types/form.types";
import { IconEyeOff } from "@tabler/icons-react";
import { type Metadata } from "next";
import { redirect } from "next/navigation";
import { isEmpty } from "radash";

interface Props {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = await params;
  const form = await api.form.getByIdPublic({ id });
  return {
    title: `${form?.headerTitle}`,
    description: "Made with Formbox, the easiest way to create forms for free.",
    openGraph: {
      title: `${form?.headerTitle}`,
      description:
        "Made with Formbox, the easiest way to create forms for free.",
      images: [
        {
          url: `https://storage.formbox.app/marketing-assets/website-home-page.webp`,
          width: 800,
          height: 800,
          alt: "Formbox Forms",
        },
      ],
    },
  };
}

export default async function FormViewPage({ params }: Props) {
  const { id } = await params;
  const form = await api.form.getByIdPublic({ id });

  const isFreePlan =
    form?.organization?.stripePlan?.toLocaleLowerCase() === "free" ||
    isEmpty(form?.organization?.stripePlan);

  async function redirectToCustomSuccessUrl() {
    "use server";
    if (form?.customSuccessUrl && !isFreePlan) {
      redirect(form?.customSuccessUrl);
    }
  }

  return (
    <div className="h-full">
      <div className="h-full">
        {form.isClosed && (
          <div className="flex h-screen flex-col items-center justify-center">
            <div
              className="space-y-3 text-center"
              style={{ color: form?.textColor }}
            >
              <div className="flex flex-col items-center">
                <IconEyeOff size={50} />
              </div>
              <h2 className="mt-4 text-2xl font-semibold">
                {form?.closeMessageTitle || "This form is now closed"}
              </h2>
              <p className="text-xl font-light">
                {form?.closeMessageDescription ||
                  "The form can't receive new submissions at this moment."}
              </p>
            </div>
          </div>
        )}

        {!form.isClosed && (
          <FormPreview
            form={form as FormByIdOutput}
            redirect={redirectToCustomSuccessUrl}
          />
        )}
      </div>
    </div>
  );
}
