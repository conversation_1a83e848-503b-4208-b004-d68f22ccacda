import { SettingsView } from "@/components/settings/settings-view";
import { COMPANY_NAME } from "@/utils/constants";

export const metadata = {
  title: `Settings - ${COMPANY_NAME}`,
};

interface Props {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function SettingsPage(props: Props) {
  const searchParams = await props.searchParams;
  return <SettingsView searchParams={searchParams} />;
}
