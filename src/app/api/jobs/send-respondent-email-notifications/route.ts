// This is a public API endpoint that will be invoked by QStash.
// It contains the logic for the background job and may take a long time to execute.

import { isEmailDeliverable } from "@/libs/email-validation";
import {
  sendFormRespondentEmail,
  sendFormRespondentEmailWithSMTP,
} from "@/libs/mail";
import { type SMTPSchema } from "@/libs/schemas/form.schemas";
import { type z } from "@/libs/zod";

type SMTPConfig = z.infer<typeof SMTPSchema> | null | undefined;

export async function POST(request: Request) {
  try {
    const body = await request.json();

    const to = body.to as string;
    const fromName = body.fromName as string;
    const subject = body.subject as string;
    const html = body.html as string;
    const smtpConfig = body.smtpConfig as SMTPConfig;

    const isDeliverableEmail = await isEmailDeliverable(to);

    if (!isDeliverableEmail) {
      return new Response("Email is not deliverable", { status: 400 });
    }

    if (smtpConfig && smtpConfig.smtpEnabled) {
      await sendFormRespondentEmailWithSMTP({
        fromName,
        to,
        subject,
        html,
        smtpHost: smtpConfig.smtpHost,
        smtpPort: Number(smtpConfig.smtpPort),
        smtpUsername: smtpConfig.smtpUsername,
        smtpPassword: smtpConfig.smtpPassword,
        smtpSenderEmail: smtpConfig.smtpSenderEmail,
      });

      return new Response("Respondent email job started", { status: 200 });
    }

    const result = await sendFormRespondentEmail(fromName, to, subject, html);

    if (result.error?.message) {
      console.error(result);
      return new Response("Error sending email", { status: 500 });
    }

    return new Response("Respondent email job started", { status: 200 });
  } catch (error) {
    console.error(error);
    return new Response("Error sending email", { status: 500 });
  }
}
