import { db } from "@/server/db";
import { verifySignatureAppRouter } from "@upstash/qstash/dist/nextjs";
import { type NextRequest, NextResponse } from "next/server";

/**
 * <PERSON><PERSON> to delete submissions that are more than 60 days old.
 * Runs every 6 hours (might need to change this if we have more users).
 **/
async function handler(_req: NextRequest) {
  try {
    const submissions = await db.submission.deleteMany({
      where: {
        createdAt: {
          lte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        },
        form: { submissionStorageDuration: "30" },
        isSpam: false,
        isProcessed: true,
      },
    });
    return NextResponse.json(
      {
        message: "success",
        deletedCount: submissions.count,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error(error);
    return NextResponse.json(error, { status: 500 });
  }
}

export const POST = verifySignatureAppRouter(handler);
