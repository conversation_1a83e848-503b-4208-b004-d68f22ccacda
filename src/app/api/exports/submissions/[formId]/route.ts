import { auth } from "@/libs/auth";
import { nanoid } from "@/libs/nanoid";
import { db } from "@/server/db";
import { convertToCSV } from "@/utils/convert";
import { headers } from "next/headers";
import { type NextRequest } from "next/server";
import { dash } from "radash";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ formId: string }> },
) {
  const { formId } = await params;
  const requestHeaders = await headers();
  const session = await auth.api.getSession({ headers: requestHeaders });

  if (!session?.user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const searchParams = req.nextUrl.searchParams;
  const format = searchParams.get("format");
  const isSpam = searchParams.get("isSpam");

  try {
    const form = await db.form.findUnique({ where: { id: formId } });

    if (!form) {
      return new Response("Form not found", { status: 404 });
    }

    const submissions = await db.submission.findMany({
      where: {
        formId,
        isSpam: isSpam === "false" ? false : true,
      },
      include: {
        answers: true,
        files: true,
      },
    });

    const json = submissions.map((submission) => {
      const submissionAnswers: Record<string, string> = {};

      submission.answers?.forEach((answer) => {
        submissionAnswers[answer.label] = answer.value;
      });

      // Add file URLs as separate columns based on formFieldName
      const fileColumns: Record<string, string> = {};
      submission.files?.forEach((file) => {
        if (file.formFieldName) {
          // If multiple files for same field, concatenate with semicolon
          const existingValue = fileColumns[file.formFieldName];
          fileColumns[file.formFieldName] = existingValue
            ? `${existingValue}; ${file.url}`
            : file.url;
        }
      });

      return {
        ...submissionAnswers,
        ...fileColumns,
        isSpam: submission.isSpam,
        createdAt: submission.createdAt,
      };
    });

    if (format === "csv") {
      const csv = convertToCSV(json);

      return new Response(csv, {
        status: 200,
        headers: {
          "Content-Disposition": `attachment; filename="${dash(form.name.toLowerCase())}-${nanoid(6)}.csv"`,
          "Content-Type": "application/csv",
        },
      });
    }

    if (format === "json") {
      return Response.json(json, {
        status: 200,
        headers: {
          "Content-Disposition": `attachment; filename="${dash(form.name.toLowerCase())}-${nanoid(6)}.json"`,
          "Content-Type": "application/json",
        },
      });
    }
  } catch (e) {
    if (e instanceof Error) {
      console.error(e);
      return new Response(e.message, {
        status: 400,
      });
    }
  }
}
