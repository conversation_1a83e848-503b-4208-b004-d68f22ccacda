import { env } from "@/env";
import { qstash } from "@/libs/upstash";
import { db } from "@/server/db";
import { type NextRequest, NextResponse } from "next/server";

// QStash will call this endpoint with the data it received earlier.
async function handler(req: NextRequest) {
  const body = await req.json();

  try {
    const integrations = await db.integration.findMany({
      where: { formId: body.formId, isEnabled: true },
    });

    if (integrations.length === 0) {
      return NextResponse.json(
        { success: true, message: "No integrations found" },
        { status: 200 },
      );
    }

    const batchTriggers = integrations.map((integration) => {
      return {
        url: `${env.APP_URL}/api/integrations/${integration.type}`,
        body,
      };
    });

    const result = await qstash.batchJSON(batchTriggers);

    return NextResponse.json(
      { success: true, message: "Integrations processed successfully", result },
      { status: 200 },
    );
  } catch (error) {
    console.log("ERROR: ", error);
    return NextResponse.json({ success: false, error }, { status: 500 });
  }
}

export const POST = handler;
