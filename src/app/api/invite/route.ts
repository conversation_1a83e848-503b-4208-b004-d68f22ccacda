import { env } from "@/env";
import { getAuthSession } from "@/libs/auth-client";
import { db } from "@/server/db";
import { redirect } from "next/navigation";
import { NextRequest } from "next/server";

async function findUserByEmail(email: string) {
  const user = await db.user.findUnique({
    where: { email },
  });

  if (user) {
    return true;
  }

  return false;
}

export async function GET(request: NextRequest) {
  const session = await getAuthSession(request);

  const orgName = request.nextUrl.searchParams.get("organization") as string;
  const orgId = request.nextUrl.searchParams.get("organizationId") as string;
  const inviteId = request.nextUrl.searchParams.get("inviteId") as string;
  const email = request.nextUrl.searchParams.get("email") as string;

  const searchParams = new URLSearchParams({
    organization: orgName,
    organizationId: orgId,
    inviteId,
    email,
  });

  const callbackUrl = `${env.APP_URL}/invite?${searchParams.toString()}`;

  const encodedCallbackUrl = encodeURIComponent(callbackUrl);

  if (!session.data) {
    const userExists = await findUserByEmail(email);

    if (!userExists) {
      redirect(`${env.APP_URL}/auth/signup?callbackUrl=${encodedCallbackUrl}`);
    } else {
      redirect(`${env.APP_URL}/auth/login?callbackUrl=${encodedCallbackUrl}`);
    }
  }

  if (session.data) {
    redirect(`${callbackUrl}`);
  }

  return new Response("ok", { status: 200 });
}
