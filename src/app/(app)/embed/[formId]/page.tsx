import { FormEmbedView } from "@/components/forms/form-embed-view";
import { api } from "@/trpc/server";
import { type FormByIdOutput } from "@/types/form.types";
import { type Metadata } from "next";
import { isEmpty } from "radash";

type Params = Promise<{ formId: string }>;
type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>;

interface Props {
  params: Params;
  searchParams: SearchParams;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const paramsData = await params;
  const form = await api.form.getByIdPublic({ id: paramsData.formId });

  if (!form) {
    return {
      title: "Form Not Found",
      description: "The requested form could not be found.",
    };
  }

  const title = form.headerTitle || form.name || "Form";
  const description = form.headerDescription || "Fill out this form";

  return {
    title: `${title} | FormBox`,
    description,
    robots: "noindex, nofollow",
    other: {
      "X-Frame-Options": "ALLOWALL",
    },
  };
}

export default async function FormEmbedPage({ params, searchParams }: Props) {
  const paramsData = await params;
  const searchParamsData = await searchParams;
  const form = await api.form.getByIdPublic({ id: paramsData.formId });

  if (!form) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Form Not Found</h1>
          <p className="mt-2 text-gray-600">
            The form you&apos;re looking for doesn&apos;t exist or has been
            removed.
          </p>
        </div>
      </div>
    );
  }

  const isEmbedded = searchParamsData?.isEmbedded === "1";

  if (form.isClosed) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">
            {form?.closeMessageTitle || "This form is now closed"}
          </h1>
          <p className="mt-2 text-gray-600">
            {form?.closeMessageDescription ||
              "The form can't receive new submissions at this moment."}
          </p>
        </div>
      </div>
    );
  }

  // Prepare redirect config instead of function
  const redirectConfig = {
    shouldRedirect:
      form?.organization?.stripePlan?.toLowerCase() !== "free" &&
      !isEmpty(form.organization?.stripePlan) &&
      form.useCustomRedirect &&
      !isEmpty(form.customSuccessUrl),
    customSuccessUrl: form.customSuccessUrl,
    isEmbedded,
  };

  return (
    <div className="min-h-screen">
      <FormEmbedView
        form={form as FormByIdOutput}
        embedOptions={{
          isEmbedded,
          hideTitle: searchParamsData?.hideTitle === "1",
          alignLeft: searchParamsData?.alignLeft === "1",
          transparentBackground:
            searchParamsData?.transparentBackground === "1",
          dynamicHeight: searchParamsData?.dynamicHeight === "1",
          autoResize: searchParamsData?.autoResize === "1",
          theme: (searchParamsData?.theme as string) || "light",
          accentColor: (searchParamsData?.accentColor as string) || null,
          backgroundColor:
            (searchParamsData?.backgroundColor as string) || null,
          textColor: (searchParamsData?.textColor as string) || null,
        }}
        redirectConfig={redirectConfig}
      />
    </div>
  );
}
