import { TemplatesView } from "@/components/templates/templates-view";
import { COMPANY_NAME } from "@/utils/constants";

export const metadata = {
  title: `Templates - ${COMPANY_NAME}`,
};

interface Props {
  params: Promise<{ orgId: string }>;
}

export default async function TemplatesPage({ params }: Props) {
  const paramsData = await params;
  return <TemplatesView orgId={paramsData.orgId} />;
}
