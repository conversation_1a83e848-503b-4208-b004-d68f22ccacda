import { TemplatePreviewView } from "@/components/templates/template-preview-view";
import { api } from "@/trpc/server";
import { COMPANY_NAME } from "@/utils/constants";
import { type Metadata } from "next";

interface Props {
  params: Promise<{ templateId: string; orgId: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const paramsData = await params;

  try {
    const template = await api.template.getById({
      id: paramsData.templateId,
    });
    return {
      title: `${template?.name} - Template Preview - ${COMPANY_NAME}`,
    };
  } catch {
    return {
      title: `Template Preview - ${COMPANY_NAME}`,
    };
  }
}

export default async function TemplatePreviewPage({ params }: Props) {
  const paramsData = await params;
  return (
    <TemplatePreviewView
      templateId={paramsData.templateId}
      orgId={paramsData.orgId}
    />
  );
}
