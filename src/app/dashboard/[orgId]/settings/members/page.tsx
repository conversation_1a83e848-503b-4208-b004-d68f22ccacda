import { OrgMembersView } from "@/components/orgs/org-members-view";
import { api } from "@/trpc/server";
import { type Metadata } from "next";

interface Props {
  params: Promise<{ orgId: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata(props: Props): Promise<Metadata> {
  const params = await props.params;
  const org = await api.org.getById({ id: params.orgId });
  return {
    title: `Members - ${org?.name}`,
  };
}

export default async function OrgMembersPage(props: Props) {
  const params = await props.params;
  const searchParams = await props.searchParams;
  return <OrgMembersView orgId={params.orgId} searchParams={searchParams} />;
}
