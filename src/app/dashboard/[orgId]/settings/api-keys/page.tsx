import { OrgAPIKeysView } from "@/components/orgs/org-api-keys-view";
import { api } from "@/trpc/server";
import { type Metadata } from "next";

interface Props {
  params: Promise<{ orgId: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const paramsData = await params;
  const org = await api.org.getById({ id: paramsData.orgId });
  return {
    title: `API Keys - ${org?.name}`,
  };
}

export default async function APIKeysPage({ params }: Props) {
  const paramsData = await params;
  return <OrgAPIKeysView orgId={paramsData.orgId} />;
}
