import { FormIntegrationsView } from "@/components/forms/form-integrations-view";
import { api } from "@/trpc/server";
import { type Metadata } from "next";

interface Props {
  params: Promise<{ formId: string; orgId: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const paramsData = await params;

  const form = await api.form.getById({
    id: paramsData.formId,
    orgId: paramsData.orgId,
  });
  return {
    title: `Integrations - ${form?.name}`,
  };
}

export default async function IntegrationsPage({ params }: Props) {
  const paramsData = await params;
  return (
    <FormIntegrationsView orgId={paramsData.orgId} formId={paramsData.formId} />
  );
}
