import { FormSettingsView } from "@/components/forms/form-settings-view";
import { api } from "@/trpc/server";
import { type Metadata } from "next";
interface Props {
  params: Promise<{ formId: string; orgId: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const paramsData = await params;

  const form = await api.form.getById({
    id: paramsData.formId,
    orgId: paramsData.orgId,
  });
  return {
    title: `Settings - ${form?.name}`,
  };
}

export default async function SettingsPage({ params }: Props) {
  const paramsData = await params;
  return (
    <FormSettingsView orgId={paramsData.orgId} formId={paramsData.formId} />
  );
}
