import { FormSubmissionsView } from "@/components/forms/form-submissions-view";
import { api } from "@/trpc/server";
import { type Metadata } from "next";

interface Props {
  params: Promise<{ formId: string; orgId: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const paramsData = await params;

  const form = await api.form.getById({
    id: paramsData.formId,
    orgId: paramsData.orgId,
  });
  return {
    title: `Submissions - ${form?.name}`,
  };
}

export default async function Page({ params }: Props) {
  const paramsData = await params;
  return (
    <FormSubmissionsView formId={paramsData.formId} orgId={paramsData.orgId} />
  );
}
