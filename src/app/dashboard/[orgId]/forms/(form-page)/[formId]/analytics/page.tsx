import { FormAnalyticsView } from "@/components/forms/form-analytics-view";
import { api } from "@/trpc/server";
import { type Metadata } from "next";

interface Props {
  params: Promise<{ formId: string; orgId: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const paramsData = await params;

  const form = await api.form.getById({
    id: paramsData.formId,
    orgId: paramsData.orgId,
  });

  return {
    title: `Analytics - ${form?.name}`,
  };
}

export default async function AnalyticsPage({ params }: Props) {
  const paramsData = await params;
  return (
    <FormAnalyticsView formId={paramsData.formId} orgId={paramsData.orgId} />
  );
}
