"use client";

import { ErrorView } from "@/components/error/error-view";
import posthog from "posthog-js";
import { useEffect } from "react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    posthog.captureException(error);
  }, [error]);
  return (
    <ErrorView reset={reset} message={error.message} className="h-[80vh]" />
  );
}
