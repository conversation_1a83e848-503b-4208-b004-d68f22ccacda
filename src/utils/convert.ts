/**
 * Converts an array of objects to CSV format.
 *
 * @param data - An array of objects to be converted to CSV format. Each object represents a row,
 * where the object keys become column headers and values become cell contents.
 *
 * @returns A string containing the CSV data with headers as the first row,
 * followed by data rows. Fields are comma-separated and rows are separated by CRLF (\r\n).
 * Null values are converted to empty strings, and all other values are JSON stringified.
 *
 * @example
 * ```typescript
 * const data = [
 *   { name: "<PERSON>", age: 30, city: "New York" },
 *   { name: "<PERSON>", age: 25, city: null }
 * ];
 * const csv = convertToCSV(data);
 * // Returns:
 * // name,age,city
 * // "<PERSON>",30,"New York"
 * // "Jane",25,""
 * ```
 */
export function convertToCSV(data: Record<string, any>[]): string {
  if (!data || data.length === 0) {
    return "";
  }

  // Get all unique headers from all objects to handle inconsistent data
  const allHeaders = new Set<string>();
  data.forEach((row) => {
    Object.keys(row).forEach((key) => allHeaders.add(key));
  });
  const headers = Array.from(allHeaders);

  const escapeCSVField = (field: any): string => {
    if (field === null || field === undefined) {
      return "";
    }

    const stringValue = String(field);

    // If field contains comma, newline, or quote, wrap in quotes and escape internal quotes
    if (
      stringValue.includes(",") ||
      stringValue.includes("\n") ||
      stringValue.includes("\r") ||
      stringValue.includes('"')
    ) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }

    return stringValue;
  };

  const csvRows = data.map((row) =>
    headers.map((header) => escapeCSVField(row[header])).join(","),
  );

  return [headers.join(","), ...csvRows].join("\r\n");
}
