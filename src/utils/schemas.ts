import { z } from "zod";

export const LoginSchema = z.object({
  email: z.string().email({
    message: "Email is required",
  }),
});

export const UserSchema = z.object({
  name: z
    .string()
    .min(3, "Name must contain at least 3 character(s)")
    .max(50, "Name must contain at less than 50 character(s)"),
});

export const OrgCreateSchema = z.object({
  name: z.string().min(1, "Organization name is a required field"),
  slug: z.string().min(1, "Organization slug is a required field"),
  logo: z.string().optional(),
});

export const OrgUpdateSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
});

export const formCreateSchema = z.object({
  name: z.string().min(1, "Form name is a required field."),
  type: z.enum(["endpoint", "hosted"]).default("endpoint"),
  organizationId: z.string(),

  // Optional template fields for form creation
  removeFormboxBranding: z.boolean().optional(),
  sendEmailNotifications: z.boolean().optional(),
  emailsToNotify: z.array(z.string()).optional(),
  submissionStorageDuration: z.string().optional(),
  sendRespondantEmailNotifications: z.boolean().optional(),
  respondantEmailFromName: z.string().optional(),
  respondantEmailSubject: z.string().optional(),
  respondantEmailMessageHTML: z.string().optional(),
  submitButtonText: z.string().optional(),
  limitResponses: z.boolean().optional(),
  isClosed: z.boolean().optional(),
  autoCloseEnabled: z.boolean().optional(),
  autoCloseDate: z.date().optional().nullable(),
  autoCloseTime: z.string().optional().nullable(),
  autoCloseTimezone: z.string().optional().nullable(),
  maxResponses: z.number().optional().nullable(),
  useCustomRedirect: z.boolean().optional(),
  customSuccessUrl: z.string().optional(),
  webhookEnabled: z.boolean().optional(),
  webhookUrl: z.string().optional(),
  customHoneypot: z.string().optional(),
  googleRecaptchaEnabled: z.boolean().optional(),
  googleRecaptchaSecretKey: z.string().optional(),
  allowedDomains: z.string().optional(),
  allowedCountries: z.string().optional(),
  ipBlacklist: z.string().optional(),

  // Styling options
  accentColor: z.string().optional(),
  backgroundColor: z.string().optional(),
  buttonBackgroundColor: z.string().optional(),
  buttonBorderStyle: z.enum(["full", "flat", "rounded"]).optional(),
  buttonTextColor: z.string().optional(),
  closeMessageDescription: z.string().optional(),
  closeMessageTitle: z.string().optional(),
  headerDescription: z.string().optional(),
  headerImage: z.string().optional(),
  headerTitle: z.string().optional(),
  inputBorderStyle: z.enum(["full", "flat", "rounded"]).optional(),
  logo: z.string().optional(),
  pageMode: z.enum(["compact", "full"]).optional(),
  saveAnswers: z.boolean().optional(),
  showCustomClosedMessage: z.boolean().optional(),
  textColor: z.string().optional(),
  tpBackgroundColor: z.string().optional(),
  tpButtonColor: z.string().optional(),
  tpButtonText: z.string().optional(),
  tpButtonUrl: z.string().optional(),
  tpHeader: z.string().optional(),
  tpMessage: z.string().optional(),
  tpTextColor: z.string().optional(),
  useCustomThankYouPage: z.boolean().optional(),
  tpButtonBackgroundColor: z.string().optional(),

  // SMTP settings - nullable for compatibility
  smtpHost: z.string().optional().nullable(),
  smtpPassword: z.string().optional().nullable(),
  smtpPort: z.number().optional().nullable(),
  smtpUsername: z.string().optional().nullable(),
  smtpEnabled: z.boolean().optional(),
  smtpSenderEmail: z.string().email().optional().nullable(),

  fields: z
    .array(
      z.object({
        id: z.string(),
        label: z.string(),
        description: z.string().optional(),
        type: z.string(),
        subtype: z.string(),
        required: z.boolean(),
        ratingCount: z.number().optional(),
        options: z
          .array(z.object({ id: z.string(), value: z.string() }))
          .optional(),
        showDescription: z.boolean(),
      }),
    )
    .optional(),
});

export const formDuplicateSchema = z.object({
  id: z.string(),
  orgId: z.string(),
});

export const formUpdateSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  isClosed: z.boolean().optional(),
  autoCloseEnabled: z.boolean().optional(),
  autoCloseDate: z.date().optional().nullable(),
  autoCloseTimezone: z.string().optional().nullable(),
  autoCloseTime: z.string().optional().nullable(),
  removeFormboxBranding: z.boolean().optional(),
  saveAnswers: z.boolean().optional(),
  submissionStorageDuration: z.string().optional(),
  sendEmailNotifications: z.boolean().optional(),
  sendRespondantEmailNotifications: z.boolean().optional(),
  emailsToNotify: z.array(z.string()).optional(),
  allowedDomains: z.string().optional(),
  allowedCountries: z.string().optional(),
  ipBlacklist: z.string().optional(),
  customHoneypot: z.string().optional(),
  limitResponses: z.boolean().optional(),
  maxResponses: z.number().optional().nullable(),
  respondantEmailFromName: z.string().optional(),
  respondantEmailSubject: z.string().optional(),
  respondantEmailMessageHTML: z.string().optional(),
  smtpEnabled: z.boolean().optional(),
  smtpHost: z.string().optional().nullable(),
  smtpPort: z.number().optional().nullable(),
  smtpUsername: z.string().optional().nullable(),
  smtpPassword: z.string().optional().nullable(),
  smtpSenderEmail: z.string().email().optional().nullable(),
  submitButtonText: z.string().optional(),
  webhookEnabled: z.boolean().optional(),
  webhookUrl: z.string().optional(),
  useCustomRedirect: z.boolean().optional(),
  customSuccessUrl: z.string().optional(),
  useCustomThankYouPage: z.boolean().optional(),
  tpBackgroundColor: z.string().optional(),
  tpTextColor: z.string().optional(),
  tpButtonBackgroundColor: z.string().optional(),
  tpButtonColor: z.string().optional(),
  tpHeader: z.string().optional(),
  tpMessage: z.string().optional(),
  tpButtonText: z.string().optional(),
  tpButtonUrl: z.string().optional(),
  googleRecaptchaEnabled: z.boolean().optional(),
  googleRecaptchaSecretKey: z.string().optional(),
  showCustomClosedMessage: z.boolean().optional(),
  closeMessageTitle: z.string().optional(),
  closeMessageDescription: z.string().optional(),
  headerTitle: z.string().optional(),
  headerDescription: z.string().optional(),
  pageMode: z.enum(["compact", "full"]).optional(),
  backgroundColor: z.string().optional(),
  textColor: z.string().optional(),
  buttonBackgroundColor: z.string().optional(),
  buttonTextColor: z.string().optional(),
  accentColor: z.string().optional(),
  buttonBorderStyle: z.enum(["full", "flat", "rounded"]).optional(),
  inputBorderStyle: z.enum(["full", "flat", "rounded"]).optional(),
  headerImage: z.string().default("").optional(),
  logo: z.string().default("").optional(),
  fields: z
    .array(
      z.object({
        id: z.string(),
        label: z.string(),
        description: z.string().optional(),
        type: z.string(),
        subtype: z.string(),
        required: z.boolean(),
        ratingCount: z.number().optional(),
        options: z
          .array(z.object({ id: z.string(), value: z.string() }))
          .optional(),
        showDescription: z.boolean(),
      }),
    )
    .optional(),
});

export const formCreateBodySchema = z.object({
  name: z.string().min(1, "Form name is a required field."),
  type: z.enum(["endpoint", "hosted"]).default("endpoint"),
  removeFormboxBranding: z.boolean().default(false),
  sendEmailNotifications: z.boolean().default(true),
  emailsToNotify: z.array(z.string()).default([]),
  submissionStorageDuration: z
    .enum(["30", "60", "90", "365", "forever", "never"])
    .default("365"),
  sendRespondantEmailNotifications: z.boolean().default(false),
  respondantEmailFromName: z.string().default(""),
  respondantEmailSubject: z.string().default(""),
  limitResponses: z.boolean().default(false),
  isClosed: z.boolean().default(false),
  autoCloseEnabled: z.boolean().default(false),
  autoCloseDate: z.date().optional().nullable(),
  autoCloseTimezone: z.string().optional().nullable(),
  autoCloseTime: z.string().optional().nullable(),
  maxResponses: z.number().default(Infinity),
  useCustomThankYouPage: z.boolean().default(false),
  tpButtonText: z.string().default(""),
  tpButtonColor: z.string().default("#030712"),
  tpButtonBackgroundColor: z.string().default("#f3f4f6"),
  tpBackgroundColor: z.string().default("#ffffff"),
  tpTextColor: z.string().default("#030712"),
  tpHeader: z.string().default(""),
  tpMessage: z.string().default(""),
  tpButtonUrl: z.string().default(""),
  useCustomRedirect: z.boolean().default(false),
  customSuccessUrl: z.string().default(""),
  customHoneypot: z.string().default(""),
  googleRecaptchaEnabled: z.boolean().default(false),
  googleRecaptchaSecretKey: z.string().default(""),
  allowedDomains: z.string().default(""),
});

export const formUpdateBodySchema = z.object({
  name: z.string().optional(),
  type: z.enum(["endpoint", "hosted"]).optional(),
  removeFormboxBranding: z.boolean().optional(),
  sendEmailNotifications: z.boolean().optional(),
  emailsToNotify: z.array(z.string()).optional(),
  submissionStorageDuration: z
    .enum(["30", "60", "90", "365", "forever", "never"])
    .optional(),
  sendRespondantEmailNotifications: z.boolean().optional(),
  respondantEmailFromName: z.string().optional(),
  respondantEmailSubject: z.string().optional(),
  limitResponses: z.boolean().optional(),
  isClosed: z.boolean().optional(),
  autoCloseEnabled: z.boolean().optional(),
  autoCloseDate: z.date().optional().nullable(),
  autoCloseTimezone: z.string().optional().nullable(),
  autoCloseTime: z.string().optional().nullable(),
  maxResponses: z.number().optional(),
  useCustomThankYouPage: z.boolean().optional(),
  tpButtonText: z.string().optional(),
  tpButtonColor: z.string().optional(),
  tpButtonBackgroundColor: z.string().optional(),
  tpBackgroundColor: z.string().optional(),
  tpTextColor: z.string().optional(),
  tpHeader: z.string().optional(),
  tpMessage: z.string().optional(),
  tpButtonUrl: z.string().optional(),
  useCustomRedirect: z.boolean().optional(),
  customSuccessUrl: z.string().optional(),
  customHoneypot: z.string().optional(),
  googleRecaptchaEnabled: z.boolean().optional(),
  googleRecaptchaSecretKey: z.string().optional(),
  allowedDomains: z.string().optional(),
  allowedCountries: z.string().optional(),
});

export const integrationCreateSchema = z.object({
  type: z.enum([
    "google-sheets",
    "excel",
    "slack",
    "airtable",
    "notion",
    "mailchimp",
    "github",
    "zapier",
    "webhook",
  ]),
  orgId: z.string(),
  formId: z.string(),
  connectionId: z.string(),
  isEnabled: z.boolean().default(true),
  webhookUrl: z.string().optional(),
});

export const integrationUpdateSchema = z.object({
  id: z.string().optional(),
  isEnabled: z.boolean().optional(),
  spreadsheetId: z.string().optional(),
  excelWebUrl: z.string().optional(),
  airtableBaseId: z.string().optional(),
  airtableTableId: z.string().optional(),
  mailchimpDC: z.string().optional(),
  mailchimpListId: z.string().optional(),
  webhookUrl: z.string().optional(),
  slackChannelId: z.string().optional(),
});

export const domainCreateSchema = z.object({
  name: z.string().min(1, "Domain name is required"),
  orgId: z.string(),
});

export const templateCreateSchema = z.object({
  name: z.string().min(1, "Template name is a required field."),
  description: z.string().default(""),
  category: z.string().default(""),
  organizationId: z.string().optional(),
  isFormboxTemplate: z.boolean().default(false),

  // Form settings and customization options
  removeFormboxBranding: z.boolean().optional(),
  sendEmailNotifications: z.boolean().optional(),
  emailsToNotify: z.array(z.string()).default([]).optional(),
  submissionStorageDuration: z.string().optional(),
  sendRespondantEmailNotifications: z.boolean().optional(),
  respondantEmailFromName: z.string().optional(),
  respondantEmailSubject: z.string().optional(),
  respondantEmailMessageHTML: z.string().optional(),
  submitButtonText: z.string().optional(),
  limitResponses: z.boolean().optional(),
  isClosed: z.boolean().optional(),
  autoCloseEnabled: z.boolean().optional(),
  autoCloseDate: z.date().optional().nullable(),
  autoCloseTime: z.string().optional(),
  autoCloseTimezone: z.string().optional(),
  maxResponses: z.number().optional().nullable(),
  useCustomRedirect: z.boolean().optional(),
  customSuccessUrl: z.string().optional(),
  webhookEnabled: z.boolean().optional(),
  webhookUrl: z.string().optional(),
  customHoneypot: z.string().optional(),
  googleRecaptchaEnabled: z.boolean().optional(),
  googleRecaptchaSecretKey: z.string().optional(),
  allowedDomains: z.string().optional(),
  allowedCountries: z.string().optional(),
  ipBlacklist: z.string().optional(),

  // Styling options
  accentColor: z.string().optional(),
  backgroundColor: z.string().optional(),
  buttonBackgroundColor: z.string().optional(),
  buttonBorderStyle: z.string().optional(),
  buttonTextColor: z.string().optional(),
  closeMessageDescription: z.string().optional(),
  closeMessageTitle: z.string().optional(),
  headerDescription: z.string().optional(),
  headerImage: z.string().optional(),
  headerTitle: z.string().optional(),
  inputBorderStyle: z.string().optional(),
  logo: z.string().optional(),
  pageMode: z.string().optional(),
  saveAnswers: z.boolean().optional(),
  showCustomClosedMessage: z.boolean().optional(),
  textColor: z.string().optional(),
  tpBackgroundColor: z.string().optional(),
  tpButtonColor: z.string().optional(),
  tpButtonText: z.string().optional(),
  tpButtonUrl: z.string().optional(),
  tpHeader: z.string().optional(),
  tpMessage: z.string().optional(),
  tpTextColor: z.string().optional(),
  useCustomThankYouPage: z.boolean().optional(),
  tpButtonBackgroundColor: z.string().optional(),

  // SMTP settings
  smtpHost: z.string().optional(),
  smtpPassword: z.string().optional(),
  smtpPort: z.number().optional(),
  smtpUsername: z.string().optional(),
  smtpEnabled: z.boolean().optional(),
  smtpSenderEmail: z.string().optional(),

  fields: z
    .array(
      z.object({
        id: z.string(),
        label: z.string(),
        description: z.string().optional(),
        type: z.string(),
        subtype: z.string(),
        required: z.boolean(),
        ratingCount: z.number().optional(),
        options: z
          .array(z.object({ id: z.string(), value: z.string() }))
          .optional(),
        showDescription: z.boolean(),
      }),
    )
    .default([]),
});

export const templateDuplicateSchema = z.object({
  id: z.string(),
  organizationId: z.string().optional(),
});

export const templateUpdateSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  description: z.string().optional(),
  category: z.string().optional(),
  isFormboxTemplate: z.boolean().optional(),

  // Form settings and customization options
  removeFormboxBranding: z.boolean().optional(),
  sendEmailNotifications: z.boolean().optional(),
  emailsToNotify: z.array(z.string()).optional(),
  submissionStorageDuration: z.string().optional(),
  sendRespondantEmailNotifications: z.boolean().optional(),
  respondantEmailFromName: z.string().optional(),
  respondantEmailSubject: z.string().optional(),
  respondantEmailMessageHTML: z.string().optional(),
  submitButtonText: z.string().optional(),
  limitResponses: z.boolean().optional(),
  isClosed: z.boolean().optional(),
  autoCloseEnabled: z.boolean().optional(),
  autoCloseDate: z.date().optional().nullable(),
  autoCloseTime: z.string().optional(),
  autoCloseTimezone: z.string().optional(),
  maxResponses: z.number().optional().nullable(),
  useCustomRedirect: z.boolean().optional(),
  customSuccessUrl: z.string().optional(),
  webhookEnabled: z.boolean().optional(),
  webhookUrl: z.string().optional(),
  customHoneypot: z.string().optional(),
  googleRecaptchaEnabled: z.boolean().optional(),
  googleRecaptchaSecretKey: z.string().optional(),
  allowedDomains: z.string().optional(),
  allowedCountries: z.string().optional(),
  ipBlacklist: z.string().optional(),

  // Styling options
  accentColor: z.string().optional(),
  backgroundColor: z.string().optional(),
  buttonBackgroundColor: z.string().optional(),
  buttonBorderStyle: z.string().optional(),
  buttonTextColor: z.string().optional(),
  closeMessageDescription: z.string().optional(),
  closeMessageTitle: z.string().optional(),
  headerDescription: z.string().optional(),
  headerImage: z.string().optional(),
  headerTitle: z.string().optional(),
  inputBorderStyle: z.string().optional(),
  logo: z.string().optional(),
  pageMode: z.string().optional(),
  saveAnswers: z.boolean().optional(),
  showCustomClosedMessage: z.boolean().optional(),
  textColor: z.string().optional(),
  tpBackgroundColor: z.string().optional(),
  tpButtonColor: z.string().optional(),
  tpButtonText: z.string().optional(),
  tpButtonUrl: z.string().optional(),
  tpHeader: z.string().optional(),
  tpMessage: z.string().optional(),
  tpTextColor: z.string().optional(),
  useCustomThankYouPage: z.boolean().optional(),
  tpButtonBackgroundColor: z.string().optional(),

  // SMTP settings
  smtpHost: z.string().optional(),
  smtpPassword: z.string().optional(),
  smtpPort: z.number().optional(),
  smtpUsername: z.string().optional(),
  smtpEnabled: z.boolean().optional(),
  smtpSenderEmail: z.string().optional(),

  fields: z
    .array(
      z.object({
        id: z.string(),
        label: z.string(),
        description: z.string().optional(),
        type: z.string(),
        subtype: z.string(),
        required: z.boolean(),
        ratingCount: z.number().optional(),
        options: z
          .array(z.object({ id: z.string(), value: z.string() }))
          .optional(),
        showDescription: z.boolean(),
      }),
    )
    .optional(),
});

const SortOperators = {
  ASC: "asc",
  DESC: "desc",
} as const;

export const filterSchema = {
  searchString: z.string().optional(),
  cursor: z.string().nullish(),
  sort: z.nativeEnum(SortOperators).optional(),
  take: z.number().optional(),
};
