/**
 * Creates a debounced function that delays invoking the provided function until after
 * the specified delay in milliseconds has elapsed since the last time the debounced
 * function was invoked.
 *
 * @template T - The type of the function to debounce
 * @param func - The function to debounce
 * @param delay - The number of milliseconds to delay
 * @returns A debounced version of the provided function
 *
 * @example
 * ```typescript
 * // Debounce a search function
 * const searchUsers = (query: string) => {
 *   console.log('Searching for:', query);
 *   // API call logic here
 * };
 *
 * const debouncedSearch = debounce(searchUsers, 300);
 *
 * // Will only execute once after 300ms of inactivity
 * debouncedSearch('john');
 * debouncedSearch('john doe'); // Previous call is cancelled
 * debouncedSearch('john doe smith'); // Previous call is cancelled
 * // Only the last call executes after 300ms
 * ```
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    // Clear the previous timeout if it exists
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // Set a new timeout
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
}
