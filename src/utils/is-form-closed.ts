import { dayjs } from "@/libs/dayjs";

/**
 * Check if the form is closed by auto close date.
 * @param form - The form to check.
 * @returns True if the form is closed by auto close date, false otherwise.
 */
export function isFormClosedByAutoClose(form: {
  autoCloseEnabled?: boolean;
  autoCloseDate?: Date | string | null;
  autoCloseTimezone?: string | null;
}): boolean {
  if (!form?.autoCloseEnabled || !form?.autoCloseDate) {
    return false;
  }

  const now = dayjs();
  const closeDate = dayjs(form.autoCloseDate);

  // If timezone is specified, compare in that timezone
  if (form.autoCloseTimezone) {
    try {
      const nowInTimezone = now.tz(form.autoCloseTimezone);
      const closeDateInTimezone = closeDate.tz(form.autoCloseTimezone);
      return (
        nowInTimezone.isAfter(closeDateInTimezone) ||
        nowInTimezone.isSame(closeDateInTimezone)
      );
    } catch (error) {
      // Fallback to UTC comparison if timezone is invalid
      console.warn(
        `Invalid timezone ${form.autoCloseTimezone}, falling back to UTC comparison`,
      );
      return now.isAfter(closeDate) || now.isSame(closeDate);
    }
  }

  // No timezone specified, use UTC comparison
  return now.isAfter(closeDate) || now.isSame(closeDate);
}
