import { dayjs } from "@/libs/dayjs";

/**
 * Formats a date string or Date object into a human-readable string
 *
 * @param date - The date to format (can be a string or Date object)
 * @param format - The desired output format using dayjs format tokens (default: "MMM DD, YYYY")
 * @returns A formatted date string
 *
 * @example
 * ```typescript
 * formatDate(new Date('2023-12-25'))          // returns "Dec 25, 2023"
 * formatDate('2023-12-25', 'YYYY-MM-DD')      // returns "2023-12-25"
 * formatDate('2023-12-25', 'dddd, MMMM D')    // returns "Monday, December 25"
 * ```
 */
export const formatDate = (
  date: string | Date | null | undefined,
  format = "MMM DD, YYYY",
) => {
  if (!date) return "";
  return dayjs(date).format(format);
};

/**
 * Formats a date string or Date object into a relative time string
 *
 * @param date - The date to format (can be a string or Date object)
 * @returns A relative time string (e.g., "2 hours ago", "3 days from now")
 *
 * @example
 * ```typescript
 * formatDateRelative(new Date('2023-12-25'))  // returns "in 10 days"
 * formatDateRelative('2023-12-25')            // returns "in 10 days"
 * formatDateRelative('2023-12-25', 'YYYY-MM-DD')  // returns "in 10 days"
 * ```
 */
export const formatDateRelative = (date: string | Date | null | undefined) => {
  if (!date) return "";
  return dayjs(date).fromNow();
};

/**
 * Formats a date string or Date object into a human-readable string in a specific timezone
 *
 * @param date - The date to format (can be a string or Date object)
 * @param timezone - The timezone to use for formatting (e.g., "America/New_York")
 * @param format - The desired output format using dayjs format tokens (default: "MMM DD, YYYY")
 * @returns A formatted date string in the specified timezone
 *
 * @example
 * ```typescript
 * formatWithTimezone(new Date('2023-12-25'), 'America/New_York')  // returns "Dec 25, 2023"
 * formatWithTimezone('2023-12-25', 'America/New_York', 'YYYY-MM-DD')  // returns "2023-12-25"
 * ```
 */
export const formatWithTimezone = (
  date: string | Date | null | undefined,
  timezone: string | null | undefined,
  format = "MMM DD, YYYY",
) => {
  if (!date) return "";
  const tz = timezone ?? "UTC";
  return dayjs(date).tz(tz).format(format);
};
