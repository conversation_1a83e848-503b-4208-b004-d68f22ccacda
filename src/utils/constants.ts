export const COMPANY_NAME = "Formbox";

export const TRIAL_DAYS = 14;

/**
 * The default redirect path after logging in
 */
export const DEFAULT_LOGIN_REDIRECT = "/organizations";

export const EMAIL_REGEX =
  /^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/;

export const NUMBER_REGEX =
  /^(?:-(?:[1-9](?:\d{0,2}(?:,\d{3})+|\d*))|(?:0|(?:[1-9](?:\d{0,2}(?:,\d{3})+|\d*))))(?:.\d+|)$/;

export const PHONE_NUMBER_REGEX =
  /^\+?\d{1,4}?[-.\s]?\(?\d{1,3}?\)?[-.\s]?\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,9}$/;

export const FILTER_TAKE = 20;

export const DEFAULT_PAGE_SIZE = 20;

export const DEFAULT_REDIRECTS = {
  home: "https://formbox.app",
  formbox: "https://formbox.app",
  auth: "https://app.formbox.app/auth/login",
  signin: "https://app.formbox.app/login",
  login: "https://app.formbox.app/login",
  register: "https://app.formbox.app/signup",
  signup: "https://app.formbox.app/signup",
  app: "https://app.formbox.app",
  dashboard: "https://app.formbox.app",
  settings: "https://app.formbox.app/settings",
  onboarding: "https://app.formbox.co/onboarding",
};

export const submissionErrors = {
  CLOSED: "CLOSED",
  DOMAIN_NOT_ALLOWED: "DOMAIN_NOT_ALLOWED",
  IP_NOT_ALLOWED: "IP_NOT_ALLOWED",
  FORM_NOT_FOUND: "FORM_NOT_FOUND",
  LIMIT_REACHED: "LIMIT_REACHED",
  FILE_TYPE_NOT_ALLOWED: "FILE_TYPE_NOT_ALLOWED",
  FILE_SIZE_EXCEEDED: "FILE_SIZE_EXCEEDED",
  MAX_FILE_COUNT_EXCEEDED: "MAX_FILE_COUNT_EXCEEDED",
} as const;

export const MAX_FILE_SIZE = 5000000; // 5MB
export const MAX_FILE_COUNT = 5;

export const MIME_TYPES = {
  // Images
  png: "image/png",
  gif: "image/gif",
  jpeg: "image/jpeg",
  jpg: "image/jpg",
  svg: "image/svg+xml",
  webp: "image/webp",
  avif: "image/avif",
  heic: "image/heic",

  // Documents
  csv: "text/csv",
  txt: "text/plain",
  pdf: "application/pdf",
  doc: "application/msword",
  docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  xls: "application/vnd.ms-excel",
  xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
} as const;

export const DOCUMENT_MIME_TYPE = [
  MIME_TYPES.txt,
  MIME_TYPES.pdf,
  MIME_TYPES.doc,
  MIME_TYPES.docx,
] as const;

export const SPREADSHEET_MIME_TYPE = [
  MIME_TYPES.xls,
  MIME_TYPES.xlsx,
  MIME_TYPES.csv,
] as const;

export const IMAGE_MIME_TYPE = [
  MIME_TYPES.png,
  MIME_TYPES.gif,
  MIME_TYPES.jpeg,
  MIME_TYPES.jpg,
  MIME_TYPES.svg,
  MIME_TYPES.webp,
  MIME_TYPES.avif,
  MIME_TYPES.heic,
] as const;

export type ImageMimeType =
  | "image/png"
  | "image/gif"
  | "image/jpeg"
  | "image/jpg"
  | "image/svg+xml"
  | "image/webp"
  | "image/avif"
  | "image/heic";

export type DocumentMimeType =
  | "text/plain"
  | "application/pdf"
  | "application/msword"
  | "application/vnd.openxmlformats-officedocument.wordprocessingml.document";

export type SpreadsheetMimeType =
  | "text/csv"
  | "application/vnd.ms-excel"
  | "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

export const API_SCOPES = {
  api: {
    full: "api.full",
    read: "api.read",
  },
};

export const countries = {
  Afghanistan: "AF",
  Albania: "AL",
  Algeria: "DZ",
  Andorra: "AD",
  Angola: "AO",
  Argentina: "AR",
  Armenia: "AM",
  Australia: "AU",
  Austria: "AT",
  Azerbaijan: "AZ",
  Bahamas: "BS",
  Bahrain: "BH",
  Bangladesh: "BD",
  Barbados: "BB",
  Belarus: "BY",
  Belgium: "BE",
  Belize: "BZ",
  Benin: "BJ",
  Bhutan: "BT",
  Bolivia: "BO",
  "Bosnia and Herzegovina": "BA",
  Botswana: "BW",
  Brazil: "BR",
  Bulgaria: "BG",
  "Burkina Faso": "BF",
  Burundi: "BI",
  Cambodia: "KH",
  Cameroon: "CM",
  Canada: "CA",
  "Central African Republic": "CF",
  Chad: "TD",
  Chile: "CL",
  China: "CN",
  Colombia: "CO",
  Comoros: "KM",
  Congo: "CG",
  "Costa Rica": "CR",
  Croatia: "HR",
  Cuba: "CU",
  Cyprus: "CY",
  "Czech Republic": "CZ",
  Denmark: "DK",
  Djibouti: "DJ",
  Dominica: "DM",
  "Dominican Republic": "DO",
  Ecuador: "EC",
  Egypt: "EG",
  "El Salvador": "SV",
  Estonia: "EE",
  Eswatini: "SZ",
  Ethiopia: "ET",
  Fiji: "FJ",
  Finland: "FI",
  France: "FR",
  Gabon: "GA",
  Gambia: "GM",
  Georgia: "GE",
  Germany: "DE",
  Ghana: "GH",
  Greece: "GR",
  Grenada: "GD",
  Guatemala: "GT",
  Guinea: "GN",
  Honduras: "HN",
  Hungary: "HU",
  Iceland: "IS",
  India: "IN",
  Indonesia: "ID",
  Iran: "IR",
  Iraq: "IQ",
  Ireland: "IE",
  Israel: "IL",
  Italy: "IT",
  Jamaica: "JM",
  Japan: "JP",
  Jordan: "JO",
  Kazakhstan: "KZ",
  Kenya: "KE",
  Kuwait: "KW",
  Laos: "LA",
  Latvia: "LV",
  Lebanon: "LB",
  Lesotho: "LS",
  Liberia: "LR",
  Libya: "LY",
  Liechtenstein: "LI",
  Lithuania: "LT",
  Luxembourg: "LU",
  Madagascar: "MG",
  Malawi: "MW",
  Malaysia: "MY",
  Maldives: "MV",
  Mali: "ML",
  Malta: "MT",
  Mexico: "MX",
  Moldova: "MD",
  Monaco: "MC",
  Mongolia: "MN",
  Montenegro: "ME",
  Morocco: "MA",
  Mozambique: "MZ",
  Myanmar: "MM",
  Namibia: "NA",
  Nepal: "NP",
  Netherlands: "NL",
  "New Zealand": "NZ",
  Nicaragua: "NI",
  Niger: "NE",
  Nigeria: "NG",
  "North Macedonia": "MK",
  Norway: "NO",
  Oman: "OM",
  Pakistan: "PK",
  Panama: "PA",
  "Papua New Guinea": "PG",
  Paraguay: "PY",
  Peru: "PE",
  Philippines: "PH",
  Poland: "PL",
  Portugal: "PT",
  Qatar: "QA",
  Romania: "RO",
  Russia: "RU",
  Rwanda: "RW",
  "Saudi Arabia": "SA",
  Senegal: "SN",
  Serbia: "RS",
  Singapore: "SG",
  Slovakia: "SK",
  Slovenia: "SI",
  "South Africa": "ZA",
  "South Korea": "KR",
  Spain: "ES",
  "Sri Lanka": "LK",
  Sudan: "SD",
  Sweden: "SE",
  Switzerland: "CH",
  Syria: "SY",
  Tanzania: "TZ",
  Thailand: "TH",
  Tunisia: "TN",
  Turkey: "TR",
  Uganda: "UG",
  Ukraine: "UA",
  "United Arab Emirates": "AE",
  "United Kingdom": "GB",
  "United States": "US",
  Uruguay: "UY",
  Uzbekistan: "UZ",
  Venezuela: "VE",
  Vietnam: "VN",
  Zambia: "ZM",
  Zimbabwe: "ZW",
} as const;

export const timezones = {
  // UTC
  UTC: "UTC",

  // North America - Major Time Zones
  "Eastern Time (US & Canada)": "America/New_York",
  "Central Time (US & Canada)": "America/Chicago",
  "Mountain Time (US & Canada)": "America/Denver",
  "Pacific Time (US & Canada)": "America/Los_Angeles",
  Alaska: "America/Anchorage",
  Hawaii: "Pacific/Honolulu",
  "Atlantic Time (Canada)": "America/Halifax",
  Newfoundland: "America/St_Johns",

  // North America - Major Cities
  "New York": "America/New_York",
  Toronto: "America/Toronto",
  Chicago: "America/Chicago",
  "Los Angeles": "America/Los_Angeles",
  Vancouver: "America/Vancouver",
  "Mexico City": "America/Mexico_City",

  // North America - Caribbean & Central America
  Jamaica: "America/Jamaica",
  Cuba: "America/Havana",
  "Dominican Republic": "America/Santo_Domingo",
  Bahamas: "America/Nassau",
  Barbados: "America/Barbados",
  Guatemala: "America/Guatemala",
  "Costa Rica": "America/Costa_Rica",
  Panama: "America/Panama",
  "El Salvador": "America/El_Salvador",
  Honduras: "America/Tegucigalpa",
  Nicaragua: "America/Managua",
  Belize: "America/Belize",

  // South America
  "Buenos Aires": "America/Argentina/Buenos_Aires",
  "São Paulo": "America/Sao_Paulo",
  Brasilia: "America/Sao_Paulo",
  Santiago: "America/Santiago",
  Lima: "America/Lima",
  Bogotá: "America/Bogota",
  Caracas: "America/Caracas",
  "La Paz": "America/La_Paz",
  Asunción: "America/Asuncion",
  Montevideo: "America/Montevideo",
  Quito: "America/Guayaquil",
  Georgetown: "America/Guyana",
  Paramaribo: "America/Paramaribo",
  Cayenne: "America/Cayenne",

  // Europe - Western Europe
  London: "Europe/London",
  Dublin: "Europe/Dublin",
  Lisbon: "Europe/Lisbon",
  Madrid: "Europe/Madrid",
  Paris: "Europe/Paris",
  Amsterdam: "Europe/Amsterdam",
  Brussels: "Europe/Brussels",
  Zurich: "Europe/Zurich",
  Rome: "Europe/Rome",

  // Europe - Central Europe
  Berlin: "Europe/Berlin",
  Vienna: "Europe/Vienna",
  Prague: "Europe/Prague",
  Warsaw: "Europe/Warsaw",
  Budapest: "Europe/Budapest",
  Zagreb: "Europe/Zagreb",
  Ljubljana: "Europe/Ljubljana",
  Bratislava: "Europe/Bratislava",

  // Europe - Northern Europe
  Stockholm: "Europe/Stockholm",
  Oslo: "Europe/Oslo",
  Copenhagen: "Europe/Copenhagen",
  Helsinki: "Europe/Helsinki",
  Reykjavik: "Atlantic/Reykjavik",

  // Europe - Eastern Europe
  Moscow: "Europe/Moscow",
  Kiev: "Europe/Kiev",
  Minsk: "Europe/Minsk",
  Bucharest: "Europe/Bucharest",
  Sofia: "Europe/Sofia",
  Belgrade: "Europe/Belgrade",

  // Europe - Baltic States
  Tallinn: "Europe/Tallinn",
  Riga: "Europe/Riga",
  Vilnius: "Europe/Vilnius",

  // Europe - Southeastern Europe
  Athens: "Europe/Athens",
  Istanbul: "Europe/Istanbul",
  Ankara: "Europe/Istanbul", // Same timezone as Istanbul

  // Europe - Other
  Luxembourg: "Europe/Luxembourg",
  Monaco: "Europe/Monaco",
  Andorra: "Europe/Andorra",
  Malta: "Europe/Malta",
  Valletta: "Europe/Malta", // Same timezone as Malta
  Chisinau: "Europe/Chisinau",

  // Africa - North Africa
  Cairo: "Africa/Cairo",
  Casablanca: "Africa/Casablanca",
  Tunis: "Africa/Tunis",
  Algiers: "Africa/Algiers",
  Tripoli: "Africa/Tripoli",

  // Africa - West Africa
  Lagos: "Africa/Lagos",
  Accra: "Africa/Accra",
  Abidjan: "Africa/Abidjan",
  Dakar: "Africa/Dakar",
  Bamako: "Africa/Bamako",
  Ouagadougou: "Africa/Ouagadougou",
  Lomé: "Africa/Lome",
  Cotonou: "Africa/Porto-Novo",
  Conakry: "Africa/Conakry",
  Freetown: "Africa/Freetown",
  Monrovia: "Africa/Monrovia",
  Nouakchott: "Africa/Nouakchott",
  Banjul: "Africa/Banjul",
  Bissau: "Africa/Bissau",

  // Africa - Central Africa
  Kinshasa: "Africa/Kinshasa",
  Bangui: "Africa/Bangui",
  "N'Djamena": "Africa/Ndjamena",
  Libreville: "Africa/Libreville",
  Malabo: "Africa/Malabo",
  Luanda: "Africa/Luanda",

  // Africa - East Africa
  Nairobi: "Africa/Nairobi",
  "Addis Ababa": "Africa/Addis_Ababa",
  "Dar es Salaam": "Africa/Dar_es_Salaam",
  Kampala: "Africa/Kampala",
  Kigali: "Africa/Kigali",
  Khartoum: "Africa/Khartoum",

  // Africa - Southern Africa
  Johannesburg: "Africa/Johannesburg",
  "Cape Town": "Africa/Johannesburg", // Same timezone as Johannesburg
  Lusaka: "Africa/Lusaka",
  Harare: "Africa/Harare",
  Maputo: "Africa/Maputo",
  Gaborone: "Africa/Gaborone",
  Windhoek: "Africa/Windhoek",
  Maseru: "Africa/Maseru",
  Mbabane: "Africa/Mbabane",

  // Middle East
  Dubai: "Asia/Dubai",
  Riyadh: "Asia/Riyadh",
  Jerusalem: "Asia/Jerusalem",
  Tehran: "Asia/Tehran",
  Kuwait: "Asia/Kuwait",
  Doha: "Asia/Qatar",
  Manama: "Asia/Bahrain",
  Muscat: "Asia/Muscat",
  Baghdad: "Asia/Baghdad",
  Damascus: "Asia/Damascus",
  Beirut: "Asia/Beirut",
  Amman: "Asia/Amman",

  // Asia - South Asia
  "India Standard Time": "Asia/Kolkata",
  Karachi: "Asia/Karachi",
  Islamabad: "Asia/Karachi", // Same timezone as Karachi
  Dhaka: "Asia/Dhaka",
  Colombo: "Asia/Colombo",
  Kathmandu: "Asia/Kathmandu",
  Thimphu: "Asia/Thimphu",
  Male: "Indian/Maldives",

  // Asia - Southeast Asia
  Bangkok: "Asia/Bangkok",
  Jakarta: "Asia/Jakarta",
  Singapore: "Asia/Singapore",
  Manila: "Asia/Manila",
  "Kuala Lumpur": "Asia/Kuala_Lumpur",
  "Ho Chi Minh": "Asia/Ho_Chi_Minh",
  Hanoi: "Asia/Ho_Chi_Minh", // Same timezone as Ho Chi Minh
  Yangon: "Asia/Yangon",
  Vientiane: "Asia/Vientiane",
  "Phnom Penh": "Asia/Phnom_Penh",

  // Asia - East Asia
  Beijing: "Asia/Shanghai",
  Shanghai: "Asia/Shanghai", // Same timezone as Beijing
  "Hong Kong": "Asia/Hong_Kong",
  Taipei: "Asia/Taipei",
  Tokyo: "Asia/Tokyo",
  Seoul: "Asia/Seoul",
  Pyongyang: "Asia/Pyongyang",
  Ulaanbaatar: "Asia/Ulaanbaatar",

  // Asia - Central Asia & Caucasus
  Tashkent: "Asia/Tashkent",
  Almaty: "Asia/Almaty",
  Astana: "Asia/Almaty", // Same timezone as Almaty
  Bishkek: "Asia/Bishkek",
  Dushanbe: "Asia/Dushanbe",
  Ashgabat: "Asia/Ashgabat",
  Kabul: "Asia/Kabul",
  Baku: "Asia/Baku",
  Yerevan: "Asia/Yerevan",
  Tbilisi: "Asia/Tbilisi",

  // Oceania - Australia
  Sydney: "Australia/Sydney",
  Melbourne: "Australia/Melbourne",
  Brisbane: "Australia/Brisbane",
  Perth: "Australia/Perth",
  Adelaide: "Australia/Adelaide",
  Darwin: "Australia/Darwin",

  // Oceania - Pacific Islands
  Auckland: "Pacific/Auckland",
  Wellington: "Pacific/Auckland", // Same timezone as Auckland
  Fiji: "Pacific/Fiji",
  Suva: "Pacific/Fiji", // Same timezone as Fiji
  "Port Moresby": "Pacific/Port_Moresby",

  // Atlantic Islands
  Azores: "Atlantic/Azores",
  "Canary Islands": "Atlantic/Canary",
  Greenland: "America/Godthab",

  // Indian Ocean
  Madagascar: "Indian/Antananarivo",
  Mauritius: "Indian/Mauritius",
  Seychelles: "Indian/Mahe",
  Réunion: "Indian/Reunion",
  Comoros: "Indian/Comoro",

  // Regional Time Zone Names
  "West Africa Time": "Africa/Lagos",
  "Central Africa Time": "Africa/Maputo",
  "East Africa Time": "Africa/Nairobi",
} as const;
