export const getBadgeVariantForCategory = (category?: string) => {
  if (!category) return "gray" as const;
  const key = category.toLowerCase();
  switch (key) {
    case "application":
    case "applications":
      return "indigo" as const;
    case "contact":
    case "contacts":
      return "teal" as const;
    case "feedback":
      return "purple" as const;
    case "registration":
    case "registrations":
      return "amber" as const;
    case "survey":
    case "surveys":
      return "pink" as const;
    default:
      return "gray" as const;
  }
};

export type BadgeVariantFromCategory = ReturnType<
  typeof getBadgeVariantForCategory
>;
