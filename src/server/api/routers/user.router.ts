import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const UserSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  email: z.string(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  image: z.string().url().optional(),
  accounts: z.array(
    z.object({
      id: z.string(),
      provider: z.string(),
      providerAccountId: z.string(),
    }),
  ),
});

export const userRouter = createTRPCRouter({
  findUserByEmail: protectedProcedure
    .input(z.object({ email: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { email: input.email },
      });
      if (user) {
        return true;
      }
      return false;
    }),
  getUser: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      include: {
        accounts: {
          select: {
            id: true,
            accountId: true,
            providerId: true,
          },
        },
        passkeys: true,
      },
    });

    if (user) {
      return user;
    }

    return null;
  }),
  updateUser: protectedProcedure
    .input(
      z.object({
        name: z.string().optional().nullish(),
        firstName: z.string().optional().nullish(),
        lastName: z.string().optional().nullish(),
        image: z.string().url().optional().nullish(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.user.update({
        where: { id: ctx.session.user.id },
        data: input,
        include: {
          accounts: {
            select: {
              id: true,
              accountId: true,
              providerId: true,
            },
          },
        },
      });
    }),
  sendEmailVerificationToken: protectedProcedure
    .input(z.object({ email: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // const { token } = await ctx.db.verificationToken.create({
      //   data: {
      //     identifier: input.email,
      //     token: nanoid(32),
      //     expires: dayjs().add(1, "hour").toISOString(),
      //   },
      // });

      // const link = `${env.APP_URL}/api/verify-token?token=${token}&email=${input.email}`;
      // await sendVerifyEmail(input.email, link);

      return { message: "sent" };
    }),
  getAccounts: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.user.findUnique({
      where: { id: ctx.session.user.id },
      select: {
        accounts: {
          select: {
            id: true,
            accountId: true,
            providerId: true,
          },
        },
      },
    });
  }),
  updatePasskeyName: protectedProcedure
    .input(z.object({ id: z.string(), name: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const passkey = await ctx.db.passkey.update({
        where: { id: input.id },
        data: { name: input.name },
      });

      return passkey;
    }),
});
