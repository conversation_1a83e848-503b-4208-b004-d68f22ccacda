import { env } from "@/env";
import { redis } from "@/libs/upstash";
import type { FormField } from "@/types/form.types";
import { FILTER_TAKE } from "@/utils/constants";
import { getSubmissionDuration } from "@/utils/get-submission-duration";
import { isFormClosedByAutoClose } from "@/utils/is-form-closed";
import {
  filterSchema,
  formCreateSchema,
  formDuplicateSchema,
  formUpdateSchema,
} from "@/utils/schemas";
import type { Prisma } from "@prisma/client";
import { TRPCError } from "@trpc/server";
import qrcode from "qrcode";
import { omit } from "radash";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "../trpc";

export const formsRouter = createTRPCRouter({
  create: protectedProcedure
    .input(formCreateSchema)
    .mutation(async ({ ctx, input }) => {
      const org = await ctx.db.organization.findUnique({
        where: { id: input.organizationId },
        select: { stripePlan: true },
      });
      const duration = getSubmissionDuration(org?.stripePlan ?? "free");
      return ctx.db.form.create({
        data: {
          ...input,
          emailsToNotify: [ctx.session.user.email as string],
          submissionStorageDuration: duration,
        },
      });
    }),
  duplicate: protectedProcedure
    .input(formDuplicateSchema)
    .mutation(async ({ ctx, input }) => {
      const form = await ctx.db.form.findUnique({
        where: { id: input.id },
      });

      if (!form) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Form not found",
        });
      }

      const duplicatedForm = omit(form, ["id", "createdAt", "updatedAt"]);

      return ctx.db.form.create({
        data: {
          ...duplicatedForm,
          organizationId: duplicatedForm.organizationId,
          name: `${duplicatedForm.name} (copy)`,
          fields: duplicatedForm.fields as Prisma.InputJsonValue,
        },
      });
    }),
  getAll: protectedProcedure
    .input(
      z.object({
        orgId: z.string(),
        type: z.string().optional(),
        ...filterSchema,
      }),
    )
    .query(async ({ ctx, input }) => {
      const take = input?.take ?? FILTER_TAKE;

      const type = input.type === "all" ? "" : input.type;

      const data = await ctx.db.form.findMany({
        where: {
          organizationId: input.orgId,
          name: {
            contains: input?.searchString,
            mode: "insensitive",
          },
          ...(type && { type }),
        },
        select: {
          id: true,
          organizationId: true,
          name: true,
          isClosed: true,
          autoCloseEnabled: true,
          autoCloseDate: true,
          autoCloseTimezone: true,
          limitResponses: true,
          maxResponses: true,
          type: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: { submissions: true },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      // Update isClosed status based on auto-close settings and max responses (application-side only)
      const updatedData = data.map((form) => {
        const isClosedByAutoClose = isFormClosedByAutoClose(form);
        const isClosedByMaxResponses = !!(
          form.limitResponses &&
          form.maxResponses &&
          form._count.submissions >= form.maxResponses
        );

        return {
          ...form,
          isClosed:
            form.isClosed || isClosedByAutoClose || isClosedByMaxResponses,
        };
      });

      // Group submissions for the returned forms.
      const groupedSubmissions = await ctx.db.submission.groupBy({
        by: ["formId", "isSpam"],
        where: {
          formId: { in: updatedData.map((form) => form.id) },
          isProcessed: true,
        },
        _count: { id: true },
      });

      // Merge the counts into the form objects.
      const formsWithCounts = updatedData.map((form) => {
        const counts = groupedSubmissions.filter((g) => g.formId === form.id);
        const successfulSubmissions =
          counts.find((g) => g.isSpam === false)?._count.id || 0;
        const spamSubmissions =
          counts.find((g) => g.isSpam === true)?._count.id || 0;
        return { ...form, successfulSubmissions, spamSubmissions };
      });

      const result = { data: formsWithCounts, cursor: "" };

      if (data.length < take) return result;

      return { ...result, cursor: data.at(-1)?.id };
    }),
  getQrCode: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const formUrl = `${env.APP_URL}/forms/${input.id}`;
      try {
        const qrCodeDataUrl = await qrcode.toDataURL(formUrl, {
          errorCorrectionLevel: "H",
          margin: 2,
          width: 256,
        });
        return qrCodeDataUrl;
      } catch (err) {
        console.error("Failed to generate QR code", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate QR code.",
        });
      }
    }),
  getFiles: protectedProcedure
    .input(z.object({ id: z.string(), ...filterSchema }))
    .query(async ({ ctx, input }) => {
      const take = input?.take ?? FILTER_TAKE;

      const files = await ctx.db.file.findMany({
        where: { formId: input.id },
        select: {
          id: true,
          name: true,
          url: true,
          size: true,
          createdAt: true,
          type: true,
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      const result = { data: files, cursor: "" };

      if (files.length < take) return result;

      return { ...result, cursor: files.at(-1)?.id };
    }),
  getById: protectedProcedure
    .input(z.object({ id: z.string(), orgId: z.string() }))
    .query(async ({ ctx, input }) => {
      const form = await ctx.db.form.findUnique({
        where: { id: input.id },
        include: {
          organization: {
            select: { stripePlan: true },
          },
          _count: {
            select: { submissions: true },
          },
        },
      });

      if (!form) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Form not found",
        });
      }

      if (form?.organizationId !== input.orgId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Form not found",
        });
      }

      // Dynamically compute isClosed (like getAll)
      const isClosedByAutoClose = isFormClosedByAutoClose(form);
      const isClosedByMaxResponses = !!(
        form.limitResponses &&
        form.maxResponses &&
        form._count.submissions >= form.maxResponses
      );

      const computedIsClosed =
        form.isClosed || isClosedByAutoClose || isClosedByMaxResponses;

      if (form?.type === "hosted") {
        return {
          ...form,
          isClosed: computedIsClosed,
          fields: form?.fields as FormField[],
        };
      }

      return {
        ...form,
        isClosed: computedIsClosed,
        fields: [] as FormField[],
      };
    }),
  getByIdPublic: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const form = await ctx.db.form.findUnique({
        where: { id: input.id },
        include: {
          organization: {
            select: { stripePlan: true },
          },
          _count: {
            select: { submissions: true },
          },
        },
      });

      if (!form) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Form not found",
        });
      }

      if (form?.type !== "hosted") {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Form not found",
        });
      }

      // Dynamically compute isClosed (like getAll)
      const isClosedByAutoClose = isFormClosedByAutoClose(form);
      const isClosedByMaxResponses = !!(
        form.limitResponses &&
        form.maxResponses &&
        form._count.submissions >= form.maxResponses
      );

      const computedIsClosed =
        form.isClosed || isClosedByAutoClose || isClosedByMaxResponses;

      return {
        ...form,
        isClosed: computedIsClosed,
        fields: form?.fields as FormField[],
      };
    }),
  getCustomTYPageSettings: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const settings = await ctx.db.form.findUnique({
        where: { id: input.id },
        select: {
          tpHeader: true,
          tpMessage: true,
          tpButtonText: true,
          tpButtonUrl: true,
          tpBackgroundColor: true,
          tpTextColor: true,
          tpButtonColor: true,
          tpButtonBackgroundColor: true,
          removeFormboxBranding: true,
          organization: {
            select: { stripePlan: true },
          },
        },
      });

      if (!settings) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Form settings not found",
        });
      }

      return settings;
    }),
  updateById: protectedProcedure
    .input(formUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      const updatedForm = await ctx.db.form.update({
        where: { id: input.id },
        data: {
          ...omit(input, ["id"]),
          fields: input.fields as unknown as Prisma.JsonObject,
        },
      });

      // Invalidate form cache
      await redis.del(`form_api:${updatedForm.organizationId}:${input.id}`);

      return updatedForm;
    }),
  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Delete the form
      const form = await ctx.db.form.delete({ where: { id: input.id } });
      // Invalidate form cache
      await redis.del(`form_api:${form.organizationId}:${input.id}`);
      // Return the deleted form
      return form;
    }),
});
