import { FILTER_TAKE } from "@/utils/constants";
import { filterSchema } from "@/utils/schemas";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const submissionsRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        formId: z.string(),
        ...filterSchema,
        isSpam: z.boolean().default(false).optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const formQuery = { formId: input.formId };

      const take = input?.take ?? FILTER_TAKE;

      const data = await ctx.db.submission.findMany({
        where: {
          ...formQuery,
          isSpam: input.isSpam,
          isProcessed: true,
          answers: {
            some: {
              value: {
                contains: input.searchString,
                mode: "insensitive",
              },
            },
          },
        },
        include: {
          answers: true,
          files: true,
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      const total = await ctx.db.submission.count({
        where: {
          ...formQuery,
          isSpam: input.isSpam,
          isProcessed: true,
        },
      });

      const result = { total, data, cursor: "" };

      if (data.length < take) return result;

      return { ...result, cursor: data.at(-1)?.id };
    }),
  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.submission.delete({ where: { id: input.id } });
    }),
});
