import { dayjs } from "@/libs/dayjs";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const analyticsRouter = createTRPCRouter({
  getFormOverview: protectedProcedure
    .input(z.object({ formId: z.string(), orgId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Get submissions over time (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Get basic form stats
      const [totalSubmissions, spamSubmissions, submissionsLast30Days, form] =
        await Promise.all([
          ctx.db.submission.count({
            where: { formId: input.formId, isSpam: false },
          }),
          ctx.db.submission.count({
            where: { formId: input.formId, isSpam: true },
          }),
          ctx.db.submission.count({
            where: {
              formId: input.formId,
              isSpam: false,
              createdAt: { gte: thirtyDaysAgo },
            },
          }),
          ctx.db.form.findUnique({
            where: { id: input.formId },
            select: { createdAt: true, organizationId: true },
          }),
        ]);

      if (!form || form.organizationId !== input.orgId) {
        throw new Error("Form not found");
      }

      const submissionsOverTime = await ctx.db.submission.groupBy({
        by: ["createdAt"],
        where: {
          formId: input.formId,
          isSpam: false,
          createdAt: { gte: thirtyDaysAgo },
        },
        _count: { id: true },
        orderBy: { createdAt: "asc" },
      });

      // Group by day
      const dailyStats = submissionsOverTime.reduce(
        (acc, curr) => {
          const date = curr.createdAt.toISOString().split("T")[0] as string;
          acc[date] = (acc[date] || 0) + curr._count.id;
          return acc;
        },
        {} as Record<string, number>,
      );

      // Get geographic data
      const geoData = await ctx.db.submission.groupBy({
        by: ["countryCode"],
        where: {
          formId: input.formId,
          isSpam: false,
          countryCode: { not: "" },
        },
        _count: { id: true },
        orderBy: { _count: { id: "desc" } },
        take: 10,
      });

      // Get device/platform data
      const deviceData = await ctx.db.submission.groupBy({
        by: ["platform"],
        where: {
          formId: input.formId,
          isSpam: false,
          platform: { not: "" },
        },
        _count: { id: true },
        orderBy: { _count: { id: "desc" } },
      });

      const browserData = await ctx.db.submission.groupBy({
        by: ["browser"],
        where: {
          formId: input.formId,
          isSpam: false,
          browser: { not: "" },
        },
        _count: { id: true },
        orderBy: { _count: { id: "desc" } },
      });

      return {
        overview: {
          totalSubmissions,
          spamSubmissions,
          submissionsLast30Days,
        },
        submissionsOverTime: dailyStats,
        geoData: geoData.map((item) => ({
          country: item.countryCode,
          count: item._count.id,
        })),
        deviceData: deviceData.map((item) => ({
          platform: item.platform,
          count: item._count.id,
        })),
        browserData: browserData.map((item) => ({
          browser: item.browser,
          count: item._count.id,
        })),
      };
    }),

  getQuestionAnalytics: protectedProcedure
    .input(z.object({ formId: z.string(), orgId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Verify form ownership and get form fields
      const form = await ctx.db.form.findUnique({
        where: { id: input.formId },
        select: { organizationId: true, fields: true, type: true },
      });

      if (!form || form.organizationId !== input.orgId) {
        throw new Error("Form not found");
      }

      console.log("form: ", form);

      // Parse form fields to get field types (for hosted forms)
      const formFields = form.type === "hosted" ? (form.fields as any[]) : [];
      const fieldTypeMap = formFields.reduce(
        (acc, field) => {
          acc[field.label] = {
            subtype: field.subtype,
            options: field.options || [],
          };
          return acc;
        },
        {} as Record<string, { subtype: string; options: any[] }>,
      );

      // Get all answers for analysis
      const answers = await ctx.db.answer.findMany({
        where: {
          submission: {
            formId: input.formId,
            isSpam: false,
          },
        },
        select: { label: true, value: true },
        orderBy: { label: "asc" },
      });

      // Group answers by question (label) and process multiple choice values
      const questionAnalytics = answers.reduce(
        (acc, answer) => {
          if (!acc[answer.label]) {
            const fieldInfo = fieldTypeMap[answer.label];
            acc[answer.label] = {
              questionLabel: answer.label,
              totalResponses: 0,
              responses: new Map<string, number>(),
              fieldType: fieldInfo?.subtype || "text",
              isChoiceField:
                fieldInfo?.subtype === "single_choice" ||
                fieldInfo?.subtype === "multiple_choice" ||
                fieldInfo?.subtype === "dropdown",
              options: fieldInfo?.options || [],
            };
          }

          const questionData = acc[answer.label]!;

          // Handle multiple choice fields with comma-separated values
          if (
            questionData.isChoiceField &&
            questionData.fieldType === "multiple_choice" &&
            answer.value.includes(",")
          ) {
            // Split comma-separated values and process each choice
            const choices = answer.value
              .split(",")
              .map((choice) => choice.trim())
              .filter((choice) => choice);
            choices.forEach((choice) => {
              const currentCount = questionData.responses.get(choice) || 0;
              questionData.responses.set(choice, currentCount + 1);
            });
            questionData.totalResponses += 1; // Count as one response even if multiple choices
          } else {
            // Handle single values (including single choice, dropdown, text, etc.)
            const currentCount = questionData.responses.get(answer.value) || 0;
            questionData.responses.set(answer.value, currentCount + 1);
            questionData.totalResponses += 1;
          }

          return acc;
        },
        {} as Record<
          string,
          {
            questionLabel: string;
            totalResponses: number;
            responses: Map<string, number>;
            fieldType: string;
            isChoiceField: boolean;
            options: any[];
          }
        >,
      );

      // Convert Map to array and sort by count, preserving field order from form definition
      const orderedResults =
        formFields.length > 0
          ? // For hosted forms, use the original field order
            formFields
              .map((field) => questionAnalytics[field.label])
              .filter((question): question is NonNullable<typeof question> =>
                Boolean(question),
              ) // Remove undefined entries for fields with no responses
              .map((question) => ({
                ...question,
                responses: Array.from(question.responses.entries())
                  .map(([value, count]) => ({ value, count }))
                  .sort((a, b) => b.count - a.count),
              }))
          : // For embedded forms or when no field order is available, fall back to alphabetical
            Object.values(questionAnalytics)
              .sort((a, b) => a.questionLabel.localeCompare(b.questionLabel))
              .map((question) => ({
                ...question,
                responses: Array.from(question.responses.entries())
                  .map(([value, count]) => ({ value, count }))
                  .sort((a, b) => b.count - a.count),
              }));

      return orderedResults;
    }),

  getResponseTimes: protectedProcedure
    .input(z.object({ formId: z.string(), orgId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Get submissions for this week (Monday to Sunday)
      const now = dayjs();
      const startOfWeek = now.startOf("isoWeek"); // ISO week starts on Monday
      const endOfWeek = now.endOf("isoWeek");

      const submissions = await ctx.db.submission.findMany({
        where: {
          formId: input.formId,
          isSpam: false,
          createdAt: {
            gte: startOfWeek.toDate(),
            lte: endOfWeek.toDate(),
          },
        },
        select: { createdAt: true },
        orderBy: { createdAt: "asc" },
      });

      // Group by day for this week (Monday to Sunday)
      const weeklyData = submissions.reduce(
        (acc, submission) => {
          const submissionDay = dayjs(submission.createdAt);
          const dayIndex = submissionDay.isoWeekday() - 1; // Monday=0, Tuesday=1, ..., Sunday=6
          acc[dayIndex] = (acc[dayIndex] || 0) + 1;
          return acc;
        },
        {} as Record<number, number>,
      );

      return {
        weeklyData,
        weekStartDate: startOfWeek.toDate(),
        weekEndDate: endOfWeek.toDate(),
      };
    }),
});
