import {
  templateCreateSchema,
  templateDuplicateSchema,
  templateUpdateSchema,
} from "@/libs/schemas/template.schemas";
import type { FormField } from "@/types/form.types";
import { FILTER_TAKE } from "@/utils/constants";
import { getSubmissionDuration } from "@/utils/get-submission-duration";
import { filterSchema } from "@/utils/schemas";
import { TRPCError } from "@trpc/server";
import { omit } from "radash";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const templatesRouter = createTRPCRouter({
  create: protectedProcedure
    .input(templateCreateSchema)
    .mutation(async ({ ctx, input }) => {
      const org = await ctx.db.organization.findUnique({
        where: { id: input.organizationId },
        select: { stripePlan: true },
      });
      const duration = getSubmissionDuration(org?.stripePlan ?? "free");
      return ctx.db.template.create({
        data: {
          ...input,
          fields: input.fields as FormField[],
          submissionStorageDuration: duration,
        },
      });
    }),

  duplicate: protectedProcedure
    .input(templateDuplicateSchema)
    .mutation(async ({ ctx, input }) => {
      const template = await ctx.db.template.findUnique({
        where: { id: input.id },
      });

      if (!template) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found",
        });
      }

      // Check if user has access to this template (either it's a Formbox template or they own it)
      if (
        !template.isFormboxTemplate &&
        template.organizationId !== input.organizationId
      ) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found",
        });
      }

      const duplicatedTemplate = omit(template, [
        "id",
        "createdAt",
        "updatedAt",
      ]);

      return ctx.db.template.create({
        data: {
          ...duplicatedTemplate,
          organizationId: input.organizationId,
          name: `${duplicatedTemplate.name} (copy)`,
          category: "",
          isFormboxTemplate: false, // User copies are never Formbox templates
          fields: duplicatedTemplate.fields as FormField[],
          // If it's a Formbox template, replace emails with user's email
          emailsToNotify: template.isFormboxTemplate
            ? [ctx.session.user.email]
            : template.emailsToNotify,
        },
      });
    }),

  getAllFormboxTemplates: protectedProcedure
    .input(
      z.object({
        category: z.string().optional(),
        ...filterSchema,
      }),
    )
    .query(async ({ ctx, input }) => {
      const take = input?.take ?? FILTER_TAKE;

      const data = await ctx.db.template.findMany({
        where: {
          isFormboxTemplate: true,
          name: {
            contains: input?.searchString,
            mode: "insensitive",
          },
          ...(input.category && { category: input.category }),
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      const result = { data, cursor: "" };

      if (data.length < take) return result;

      return { ...result, cursor: data.at(-1)?.id };
    }),

  getAllOrgTemplates: protectedProcedure
    .input(
      z.object({
        orgId: z.string(),
        category: z.string().optional(),
        ...filterSchema,
      }),
    )
    .query(async ({ ctx, input }) => {
      const take = input?.take ?? FILTER_TAKE;

      const data = await ctx.db.template.findMany({
        where: {
          isFormboxTemplate: false,
          organizationId: input.orgId,
          name: {
            contains: input?.searchString,
            mode: "insensitive",
          },
          ...(input.category && { category: input.category }),
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { createdAt: "desc" },
      });

      const result = { data, cursor: "" };

      if (data.length < take) return result;

      return { ...result, cursor: data.at(-1)?.id };
    }),

  getTemplateCategories: protectedProcedure.query(async ({ ctx }) => {
    const categories = await ctx.db.template.findMany({
      where: {
        isFormboxTemplate: true,
        category: {
          not: "",
        },
      },
      select: {
        category: true,
      },
      distinct: ["category"],
      orderBy: { category: "asc" },
    });

    return categories.map((c) => c.category);
  }),

  getOrgCategories: protectedProcedure
    .input(z.object({ orgId: z.string() }))
    .query(async ({ ctx, input }) => {
      const categories = await ctx.db.template.findMany({
        where: {
          organizationId: input.orgId,
          isFormboxTemplate: false,
          category: {
            not: "",
          },
        },
        select: {
          category: true,
        },
        distinct: ["category"],
        orderBy: { category: "asc" },
      });

      return categories.map((c) => c.category);
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string(), orgId: z.string().optional() }))
    .query(async ({ ctx, input }) => {
      const template = await ctx.db.template.findUnique({
        where: { id: input.id },
      });

      if (!template) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found",
        });
      }

      // Check if user has access to this template
      if (
        !template.isFormboxTemplate &&
        template.organizationId !== input.orgId
      ) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found",
        });
      }

      return {
        ...template,
        fields: template.fields as FormField[],
      };
    }),

  updateById: protectedProcedure
    .input(templateUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      // First, check if the template exists and user has permission
      const existingTemplate = await ctx.db.template.findUnique({
        where: { id: input.id },
      });

      if (!existingTemplate) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found",
        });
      }

      // Only allow updating if it's not a Formbox template or user is admin
      if (existingTemplate.isFormboxTemplate) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Cannot modify Formbox templates",
        });
      }

      const updatedTemplate = await ctx.db.template.update({
        where: { id: input.id },
        data: {
          ...omit(input, ["id"]),
          fields: input.fields as FormField[],
        },
      });

      return updatedTemplate;
    }),

  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // First, check if the template exists and user has permission
      const existingTemplate = await ctx.db.template.findUnique({
        where: { id: input.id },
      });

      if (!existingTemplate) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found",
        });
      }

      // Only allow deleting if it's not a Formbox template and user owns it
      if (existingTemplate.isFormboxTemplate) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Cannot delete Formbox templates",
        });
      }

      // Note: Additional org membership check should be done on the client side
      // since we don't have access to orgId in this endpoint

      return await ctx.db.template.delete({
        where: { id: input.id },
      });
    }),
});
