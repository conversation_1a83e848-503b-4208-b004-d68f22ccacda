import { createCallerFactory, createTRPCRouter } from "@/server/api/trpc";
import { analyticsRouter } from "./routers/analytics.router";
import { apiKeysRouter } from "./routers/api-keys.router";
import { formsRouter } from "./routers/forms.router";
import { integrationsRouter } from "./routers/integrations.router";
import { orgRouter } from "./routers/org.router";
import { paymentRouter } from "./routers/payment.router";
import { storageRouter } from "./routers/storage.router";
import { submissionsRouter } from "./routers/submissions.router";
import { templatesRouter } from "./routers/templates.router";
import { userRouter } from "./routers/user.router";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  user: userRouter,
  storage: storageRouter,
  payment: paymentRouter,
  org: orgRouter,
  form: formsRouter,
  template: templatesRouter,
  submission: submissionsRouter,
  integration: integrationsRouter,
  apiKey: apiKeysRouter,
  analytics: analyticsRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 */
export const createCaller = createCallerFactory(appRouter);
