import { type Answer } from "@prisma/client";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import * as React from "react";
import { FooterCenter } from "./components/footer";
import { Header } from "./components/header";
import {
  button,
  container,
  h1,
  text,
  wrapper,
} from "./components/sharedStyles";

const maskUrls = (text: string): string => {
  // Email pattern to identify and protect email addresses
  const emailPattern = /[\w.-]+@[\w.-]+\.\w+/g;

  // URL pattern (excluding emails)
  const urlPattern =
    /(?:https?:\/\/)?(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/gi;

  // First, find all emails and temporarily replace them with a unique marker
  const emails: string[] = [];
  const textWithEmailsMarked = text.replace(emailPattern, (match) => {
    emails.push(match);
    return `__EMAIL${emails.length - 1}__`;
  });

  // Then replace URLs with password placeholder
  const textWithUrlsMasked = textWithEmailsMarked.replace(
    urlPattern,
    "**********",
  );

  // Finally, restore the emails
  const finalText = emails.reduce((text, email, index) => {
    return text.replace(`__EMAIL${index}__`, email);
  }, textWithUrlsMasked);

  return finalText;
};

interface Props {
  formName?: string;
  formLink?: string;
  answers?: Answer[];
}

const SubmissionEmailTemplate = ({
  formName = "Formbox",
  formLink = "http://localhost:3000/organizations",
  answers = [
    { label: "What is your name?", value: "Jalen", id: "csdwdcwqdc" } as Answer,
    {
      label: "email",
      value: "<EMAIL>",
      id: "yhuhvuhvuhv",
    } as Answer,
    {
      label: "Message",
      value:
        "Hello Admin. my name is Eric and I’m betting you’d like your website to generate more leads. Here’s how: Web Visitors Into Leads is a software widget that’s works on your site, ready to capture any visitor’s Name, Email address and Phone Number. It signals you as soon as they say they’re interested – so that you can talk to that lead while they’re still there. Web Visitors Into Leads – CLICK HERE http://advanceleadgeneration.com for a live demo now. And now that you’ve got their phone number, our new SMS Text With Lead feature enables you to start a text (SMS) conversation – answer questions, provide more info, and close a deal that way. If they don’t take you up on your offer then, just follow up with text messages for new offers, content links, even just “how you doing?” notes to build a relationship. CLICK HERE http://advanceleadgeneration.com to discover what Web Visitors Into Leads can do for your business. The difference between contacting someone within 5 minutes versus a half-hour means you could be converting up to 100X more leads today! Try Web Visitors Into Leads and get more leads now. Eric PS: The studies show 7 out of 10 visitors don’t hang around – you can’t afford to lose them! Web Visitors Into Leads offers a FREE 14 days trial – and it even includes International Long Distance Calling. You have customers waiting to talk with you right now… don’t keep them waiting. CLICK HERE http://advanceleadgeneration.com to try Web Visitors Into Leads now. If you'd like to unsubscribe click here http://advanceleadgeneration.com/unsubscribe.aspx",
      id: "csdwdcwqdc",
    } as Answer,
  ],
}: Props) => {
  const previewText = `New submission for ${formName}`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Tailwind>
        <Body style={wrapper}>
          <Container style={wrapper}>
            <Container style={container}>
              <Header />
              <Section className="px-6 py-10">
                <Heading style={{ ...h1 }} className="font-normal">
                  New submission for <strong>{formName}</strong>
                </Heading>

                <Section>
                  {answers.map((answer) => (
                    <div key={answer.id}>
                      <Text className="text-[16px]">
                        <strong>{answer.label}</strong>
                      </Text>
                      <Text style={text}>{maskUrls(answer.value)}</Text>
                    </div>
                  ))}
                </Section>
                <Button style={button} href={formLink} className="mt-4">
                  View submission
                </Button>
              </Section>
              <FooterCenter />
            </Container>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

SubmissionEmailTemplate.PreviewProps = {};

export default SubmissionEmailTemplate;
