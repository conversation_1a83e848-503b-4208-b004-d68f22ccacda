"use client";

import { PasskeyCreateDialog } from "@/components/settings/passkey-create-dialog";
import { PasskeyDeleteDialog } from "@/components/settings/passkey-delete-dialog";
import { PasskeyUpdateDialog } from "@/components/settings/passkey-update-dialog";
import { env } from "@/env";
import { useDialog } from "@/hooks/use-dialog";
import { useWindow } from "@/hooks/use-window";
import GoogleLogo from "@/images/google-logo.svg";
import MicrosoftLogo from "@/images/microsoft-logo.svg";
import { type AuthProvider, linkSocialProvider } from "@/libs/auth-client";
import { nanoid } from "@/libs/nanoid";
import {
  useFileDeleteMutation,
  useFileUploadUrlMutation,
} from "@/queries/storage.queries";
import {
  useAuthUser,
  useChangeEmailMutation,
  useDeleteUserPasskeyMutation,
  useUserPasswordResetMutation,
  useUserUpdateMutation,
} from "@/queries/user.queries";
import { type Passkey } from "@/types/auth.types";
import { type UserNameFields } from "@/types/user.types";
import { compressImage } from "@/utils/compress-image";
import { IMAGE_MIME_TYPE, type ImageMimeType } from "@/utils/constants";
import { formatDate } from "@/utils/format-date";
import { getInitials } from "@/utils/get-initials";
import { UserSchema } from "@/utils/schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  IconArrowLeft,
  IconCircleCheck,
  IconKey,
  IconPencil,
  IconTrash,
  IconX,
} from "@tabler/icons-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Divider } from "../ui/divider";
import { ImageUploader } from "../ui/image-uploader";
import { Input } from "../ui/input";
import { MaxWidthWrapper } from "../ui/max-width-wrapper";
import { PageTitle } from "../ui/page-title";
import { Skeleton } from "../ui/skeleton";
import { SettingsChangeEmailDialog } from "./settings-change-email-dialog";

interface Props {
  searchParams: { [key: string]: string | string[] | undefined };
}

export function SettingsView({ searchParams }: Props) {
  const window = useWindow();
  const [openPasskeyDialog, openPasskeyDialogHandlers] = useDialog();
  const [logoUploaderOpen, logoUploaderHandler] = useDialog();
  const [changeEmailModal, changeEmailModalHandler] = useDialog();
  const [showEmailSent, setShowEmailSent] = useState(false);
  const [newEmail, setNewEmail] = useState("");
  const [emailError, setEmailError] = useState(
    (searchParams?.error as string) || "",
  );
  const { register, handleSubmit } = useForm<UserNameFields>({
    resolver: zodResolver(UserSchema),
  });

  const user = useAuthUser();

  const googleAccount = user?.accounts.find(
    (account) => account.providerId === "google",
  );

  const microsoftAccount = user?.accounts.find(
    (account) => account.providerId === "microsoft",
  );

  const credentialsAccount = user?.accounts.find(
    (account) => account.providerId === "credential",
  );

  const uploadUrlMutation = useFileUploadUrlMutation();
  const deleteFileMutation = useFileDeleteMutation();
  const userUpdateMutation = useUserUpdateMutation();
  const passwordResetMutation = useUserPasswordResetMutation();
  const changeEmailMutation = useChangeEmailMutation({
    onSuccess: () => {
      setShowEmailSent(true);
    },
  });

  async function connectSocialProvider(provider: AuthProvider) {
    await linkSocialProvider(provider, `${window?.location.origin}/settings`);
  }

  const onSubmit = async (data: UserNameFields) => {
    try {
      const name = data.name || user?.name;
      await userUpdateMutation.mutateAsync({ name });
    } catch (error) {
      console.log(error);
    }
  };

  const deleteCurrentUserImage = async (
    userImage: string | null | undefined,
  ) => {
    if (!userImage) return;

    if (!userImage.startsWith(env.NEXT_PUBLIC_AWS_PUBLIC_BUCKET_URL)) return;

    const fileKey = userImage.replace(
      `${env.NEXT_PUBLIC_AWS_PUBLIC_BUCKET_URL}/`,
      "",
    );

    try {
      return await deleteFileMutation.mutateAsync({ fileKey });
    } catch (error) {
      console.log(error);
    }
  };

  const onFileUpload = async (file: File) => {
    await deleteCurrentUserImage(user?.image);
    let compressedFile = file;

    if (IMAGE_MIME_TYPE.includes(compressedFile.type as ImageMimeType)) {
      compressedFile = await compressImage(file);
    }

    const fileKey = `users/${user?.id}/${nanoid()}-${compressedFile.name}`;
    const { uploadUrl } = await uploadUrlMutation.mutateAsync({
      fileKey,
    });
    if (uploadUrl) {
      await fetch(uploadUrl, {
        method: "PUT",
        headers: { "Content-Type": compressedFile.type },
        body: compressedFile,
      });
      await userUpdateMutation.mutateAsync({
        image: `${env.NEXT_PUBLIC_AWS_PUBLIC_BUCKET_URL}/${fileKey}`,
      });
    }
  };

  const onUrlUpload = async (url: string) => {
    await deleteCurrentUserImage(user?.image);

    await userUpdateMutation.mutateAsync({
      image: url,
    });
  };

  const handleUpdateEmail = async (email: string) => {
    setNewEmail(email);
    await changeEmailMutation.mutateAsync({ newEmail: email });
  };

  const handlePasswordReset = async () => {
    await passwordResetMutation.mutateAsync({
      email: user?.email ?? "",
    });
  };

  const isEmailVerificationError =
    emailError?.toLowerCase() === "emailverificationexpired";

  let errorMessage = "";
  let errorMessageTitle = "";

  if (searchParams?.error) {
    errorMessage = isEmailVerificationError
      ? "Email verification links expire after one hour. Please try changing your email again to get a new link."
      : "An error occurred when verifying your email. Please try changing your email again.";

    errorMessageTitle = isEmailVerificationError
      ? "Your link has expired."
      : "Email verification failed.";
  }

  return (
    <MaxWidthWrapper className="pt-10 pb-32">
      <header className="mx-auto">
        <Link className="sm:hidden" href="/organizations">
          <Button
            variant="ghost"
            size="sm"
            leftIcon={<IconArrowLeft size={16} />}
          >
            Organizations
          </Button>
        </Link>
        <div className="mt-4 flex items-center justify-between sm:mt-0">
          <PageTitle>Settings</PageTitle>
        </div>
      </header>

      <div className="flexitems-center mt-6 justify-between">
        <div className="w-full space-y-8">
          <Card className="w-full">
            <div className="p-6">
              <div>
                <h2 className="text-lg font-semibold">Avatar</h2>
                <p className="mt-2 max-w-[600px] text-gray-500">
                  This is your avatar. Click on the upload button to add a new
                  photo.
                </p>
                <div className="mt-6 flex items-center gap-x-4">
                  <Avatar className="h-20 w-20">
                    <AvatarImage src={user?.image || ""} />
                    <AvatarFallback className="text-white uppercase">
                      {getInitials(user?.name, 1) ||
                        getInitials(user?.email, 1)}
                    </AvatarFallback>
                  </Avatar>

                  <div className="relative">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={logoUploaderHandler.open}
                    >
                      Change
                    </Button>
                  </div>
                </div>
              </div>
            </div>
            <Divider />
            <div className="p-6">
              <p className="text-gray-500">
                An avatar is optional but strongly recommended.
              </p>
            </div>
          </Card>

          <Card className="w-full">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="p-6">
                <div>
                  <h2 className="text-lg font-semibold">Display name</h2>
                  <p className="mt-2 text-gray-500">
                    Please enter your full name, or a display name you are
                    comfortable with.
                  </p>
                </div>
                <div className="mt-5">
                  {!user && (
                    <Skeleton className="h-[36px] rounded-lg md:w-[420px]" />
                  )}
                  {user && (
                    <Input
                      className="md:w-[420px]"
                      defaultValue={user?.name || ""}
                      {...register("name")}
                    />
                  )}
                </div>
              </div>
              <Divider />
              <div className="p-6">
                <Button type="submit" loading={userUpdateMutation.isPending}>
                  Save changes
                </Button>
              </div>
            </form>
          </Card>

          <Card className="w-full">
            <div className="p-6">
              <div>
                <h2 className="text-lg font-semibold">Email</h2>
                <p className="mt-2 text-gray-500">
                  Please enter the email address you want to use to log in with
                  Formbox.
                </p>
              </div>
              <div className="mt-5">
                {!user && (
                  <Skeleton className="h-[36px] rounded-lg md:w-[420px]" />
                )}
                {user && (
                  <Input
                    className="md:w-[420px]"
                    defaultValue={user?.email || ""}
                    disabled
                  />
                )}
              </div>

              <div className="sm:col-span-5">
                {showEmailSent && (
                  <Alert className="relative mt-6">
                    <AlertTitle>Check your email</AlertTitle>
                    <AlertDescription>
                      <p className="text-gray-900">
                        We&apos;ve sent an email to your inbox to change you
                        email to{" "}
                        <span className="font-semibold">{newEmail}</span>. To
                        verify this change, click the link in the email.
                      </p>
                    </AlertDescription>
                    <Button
                      size="icon"
                      type="button"
                      className="absolute top-1 right-1 h-8 w-8"
                      variant="ghost"
                      onClick={() => setShowEmailSent(false)}
                    >
                      <IconX className="h-4 w-4" />
                    </Button>
                  </Alert>
                )}

                {emailError && (
                  <Alert className="relative mt-6">
                    <AlertTitle>{errorMessageTitle}</AlertTitle>
                    <AlertDescription>
                      <p className="text-gray-900">{errorMessage}</p>
                    </AlertDescription>
                    <Button
                      size="icon"
                      type="button"
                      className="absolute top-1 right-1 h-8 w-8"
                      variant="ghost"
                      onClick={() => setEmailError("")}
                    >
                      <IconX className="h-4 w-4" />
                    </Button>
                  </Alert>
                )}
              </div>
            </div>
            <Divider />
            <div className="p-6">
              <Button type="button" onClick={changeEmailModalHandler.open}>
                Change email
              </Button>
            </div>
          </Card>

          <Card className="divide-y divide-gray-200 md:col-span-2">
            <div className="flex items-center justify-between bg-sidebar px-6 py-4">
              <h2 className="text-lg font-semibold">Your account password</h2>
            </div>
            <div className="p-6">
              {credentialsAccount && (
                <>
                  <Input
                    type="password"
                    value="********************************"
                    className="md:w-[420px]"
                    disabled
                  />
                </>
              )}
              {!credentialsAccount && (
                <>
                  <p className="max-w-2xl leading-6 text-gray-500">
                    You dont have a password. You can create one by going
                    through the reset password process.
                  </p>
                </>
              )}
            </div>
            <div className="border-t border-gray-200 p-6">
              <Button
                onClick={handlePasswordReset}
                loading={passwordResetMutation.isPending}
              >
                {credentialsAccount ? "Change password" : "Create a password"}
              </Button>
            </div>
          </Card>

          <Card className="divide-y divide-gray-200 overflow-hidden md:col-span-2">
            <div className="flex items-center justify-between bg-sidebar px-6 py-4">
              <h2 className="text-lg font-semibold">Your connected accounts</h2>
            </div>

            {!user && (
              <Skeleton className="flex h-[80px] w-full items-center justify-between rounded-none px-6 py-4" />
            )}

            {user && (
              <>
                <div className="flex items-center justify-between px-6 py-4">
                  <div className="flex items-center gap-3">
                    <Image src={GoogleLogo} alt="alt" className="size-7" />
                    <p className="font-medium">Google</p>
                  </div>

                  <div>
                    {googleAccount ? (
                      <IconCircleCheck className="size-7 text-green-600" />
                    ) : (
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => connectSocialProvider("google")}
                      >
                        Connect
                      </Button>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between px-6 py-4">
                  <div className="flex items-center gap-3">
                    <Image src={MicrosoftLogo} alt="alt" className="size-7" />
                    <p className="font-medium">Microsoft</p>
                  </div>

                  <div>
                    {microsoftAccount ? (
                      <IconCircleCheck className="size-7 text-green-600" />
                    ) : (
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => connectSocialProvider("microsoft")}
                      >
                        Connect
                      </Button>
                    )}
                  </div>
                </div>
              </>
            )}
          </Card>

          {/* <Card className="overflow-hidden">
            <div className="bg-sidebar flex items-center justify-between border-b border-gray-200 px-6 py-4">
              <h2 className="text-lg font-semibold">Passkeys</h2>
              <Button
                variant="outline"
                leftIcon={<IconPlus size={16} />}
                onClick={openPasskeyDialogHandlers.open}
              >
                Add a passkey
              </Button>
            </div>
            <div>
              {!user && <Skeleton className="h-[80px] w-full rounded-none" />}
              {user?.passkeys && user?.passkeys.length > 0 && (
                <div className="divide-y divide-gray-200">
                  {user.passkeys.map((passkey) => (
                    <Passkey key={passkey.id} passkey={passkey} />
                  ))}
                </div>
              )}
              {user?.passkeys?.length === 0 && (
                <div className="py-12">
                  <EmptyState
                    icon={<IconKey className="size-8" />}
                    title="No Passkeys"
                    subtitle="Add a passkey to login more securely"
                  />
                </div>
              )}
            </div>
          </Card> */}

          {/* <Card className="w-full">
            <div className="p-6">
              <div>
                <h2 className="text-lg font-semibold">Delete account</h2>
                <p className="mt-2 max-w-[600px] text-gray-500">
                  Permanently delete your account, organizations, workspaces,
                  and all associated forms plus thier submissions. This action
                  cannot be undone - please proceed with caution.
                </p>
              </div>
            </div>
            <Divider />
            <div className="p-6">
              <Button variant="destructive">Delete account</Button>
            </div>
          </Card> */}
        </div>

        <SettingsChangeEmailDialog
          open={changeEmailModal}
          onClose={changeEmailModalHandler.close}
          submit={handleUpdateEmail}
        />

        <PasskeyCreateDialog
          open={openPasskeyDialog}
          onClose={openPasskeyDialogHandlers.close}
        />

        <ImageUploader
          open={logoUploaderOpen}
          onClose={logoUploaderHandler.close}
          submit={onUrlUpload}
          onUpload={onFileUpload}
          showUnsplash={false}
        />
      </div>
    </MaxWidthWrapper>
  );
}

function Passkey({ passkey }: { passkey: Passkey }) {
  const [openPasskeyDialog, openPasskeyDialogHandlers] = useDialog();
  const [openDeletePasskeyDialog, openDeletePasskeyDialogHandlers] =
    useDialog();

  const deletePasskeyMutation = useDeleteUserPasskeyMutation();

  async function deletePasskey() {
    await deletePasskeyMutation.mutateAsync(passkey.id);
  }

  return (
    <div key={passkey.id} className="px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="rounded-full bg-gray-100 p-3">
            <IconKey />
          </div>
          <div>
            <p className="leading-6 font-medium">{passkey.name}</p>
            <div className="flex items-center gap-4">
              <p className="text-sm leading-6 text-gray-500">
                Added on {formatDate(passkey.createdAt as Date)}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="icon"
            onClick={openPasskeyDialogHandlers.open}
          >
            <IconPencil className="size-5" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={openDeletePasskeyDialogHandlers.open}
          >
            <IconTrash className="size-5 text-red-500" />
          </Button>
        </div>
      </div>

      <PasskeyUpdateDialog
        open={openPasskeyDialog}
        onClose={openPasskeyDialogHandlers.close}
        passkey={passkey}
      />

      <PasskeyDeleteDialog
        passkeyName={passkey.name ?? ""}
        open={openDeletePasskeyDialog}
        onClose={openDeletePasskeyDialogHandlers.close}
        onDelete={deletePasskey}
        loading={deletePasskeyMutation.isPending}
      />
    </div>
  );
}
