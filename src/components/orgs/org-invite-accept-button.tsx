"use client";

import { Button } from "@/components/ui/button";
import { authClient } from "@/libs/auth-client";
import { api } from "@/trpc/react";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface Props {
  orgId: string;
  inviteId: string;
}

export function OrgInviteAcceptButton({ orgId, inviteId }: Props) {
  const router = useRouter();
  const apiUtils = api.useUtils();

  const acceptInviteMutation = useMutation({
    mutationFn: async () => {
      return await authClient.organization.acceptInvitation({
        invitationId: inviteId,
      });
    },
    onSuccess: async (data) => {
      if (data.error) {
        return toast.error("Error", { description: data.error.message });
      }
      router.push(`/dashboard/${orgId}/forms`);
    },
    onError: (error) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await apiUtils.org.getById.invalidate({ id: orgId });
      await apiUtils.org.getAll.invalidate();
    },
  });

  const onSubmit = async () => {
    await acceptInviteMutation.mutateAsync();
  };

  return (
    <div>
      <Button
        className="w-full"
        onClick={onSubmit}
        loading={acceptInviteMutation.isPending}
      >
        Accept invitation
      </Button>
    </div>
  );
}
