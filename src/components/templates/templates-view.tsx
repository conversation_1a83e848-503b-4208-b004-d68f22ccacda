"use client";

import { TemplateCard } from "@/components/templates/template-card";
import { AdminRequiredTooltip } from "@/components/ui/admin-required-tooltip";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { EmptyState } from "@/components/ui/empty-state";
import { Loader } from "@/components/ui/loader";
import { PageTitle } from "@/components/ui/page-title";
import { SearchInput } from "@/components/ui/search-input";
import { useDebouncedState } from "@/hooks/use-debounced-state";
import { useSearchTab } from "@/hooks/use-tab";
import { useOrgMemberRole } from "@/queries/org.queries";
import {
  useFormboxTemplates,
  useOrgTemplates,
  useTemplateCategories,
} from "@/queries/template.queries";
import { useAuthUser } from "@/queries/user.queries";
import type { Template } from "@/types/template.types";
import {
  IconFileDescription,
  IconPlus,
  IconTemplate,
} from "@tabler/icons-react";
import Link from "next/link";
import { isEmpty } from "radash";
import { useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";
import { MaxWidthWrapper } from "../ui/max-width-wrapper";
import { CategoryChips } from "./category-chips";

const loadingItems = new Array(8).fill("");

interface Props {
  orgId: string;
}

export function TemplatesView({ orgId }: Props) {
  const { ref, inView } = useInView();
  const [searchString, setSearchString] = useDebouncedState("", 250);
  const [activeTab, setActiveTab] = useSearchTab<"formbox" | "organization">({
    params: ["formbox", "organization"],
    defaultValue: "formbox",
  });

  const user = useAuthUser();
  const { data: userRole } = useOrgMemberRole(user?.id as string, orgId);

  // categories only needed for official/formbox templates
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(
    undefined,
  );
  const formboxCategoriesQuery = useTemplateCategories();

  // Template queries
  const formboxTemplates = useFormboxTemplates({
    searchString,
    category: selectedCategory,
  });

  const orgTemplates = useOrgTemplates(orgId, {
    searchString,
    category: selectedCategory,
  });

  // Clear selected category when switching away from formbox tab
  useEffect(() => {
    if (activeTab !== "formbox") {
      setSelectedCategory(undefined);
    }
  }, [activeTab]);

  // Infinite scroll for Formbox templates
  useEffect(() => {
    if (activeTab === "formbox" && formboxTemplates.hasNextPage && inView) {
      formboxTemplates.fetchNextPage();
    }
  }, [inView, formboxTemplates, activeTab]);

  // Infinite scroll for organization templates
  useEffect(() => {
    if (activeTab === "organization" && orgTemplates.hasNextPage && inView) {
      orgTemplates.fetchNextPage();
    }
  }, [inView, orgTemplates, activeTab]);

  // Get current templates and loading state based on active tab
  const currentTemplates =
    activeTab === "formbox" ? formboxTemplates : orgTemplates;

  const formboxCategories = formboxCategoriesQuery?.data ?? [];

  // Format template data
  const data = useMemo(() => {
    if (currentTemplates.data) {
      const templates: Template[] = [];
      for (const page of currentTemplates.data.pages) {
        templates.push(...(page.data as Template[]));
      }
      return templates;
    }
    return [] as Template[];
  }, [currentTemplates.data]);

  const noSearchResults = isEmpty(data) && !isEmpty(searchString);

  return (
    <MaxWidthWrapper className="py-10">
      <div className="w-full">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div>
            <PageTitle>Templates</PageTitle>
            <p className="mt-2 text-gray-600">
              Browse and use pre-built form templates to get started quickly.
            </p>
          </div>
        </div>

        {/* Tabs + Search inline */}
        <div className="mt-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="order-1 flex-1 sm:order-none">
            <SearchInput
              placeholder="Search templates..."
              defaultValue={searchString}
              onChange={(event) => setSearchString(event.currentTarget.value)}
              className="w-full"
            />
          </div>
          <div
            className="order-2 flex shrink-0 gap-2 sm:order-none"
            role="tablist"
            aria-label="Template source tabs"
          >
            <Button
              type="button"
              role="tab"
              aria-selected={activeTab === "formbox"}
              variant={activeTab === "formbox" ? "default" : "outline"}
              size="sm"
              className="rounded-full px-5 py-4.5"
              onClick={() => setActiveTab("formbox")}
            >
              Official
            </Button>
            <Button
              type="button"
              role="tab"
              aria-selected={activeTab === "organization"}
              variant={activeTab === "organization" ? "default" : "outline"}
              size="sm"
              className="rounded-full px-5 py-4.5"
              onClick={() => setActiveTab("organization")}
            >
              My Templates
            </Button>
          </div>
        </div>
        {activeTab === "formbox" && (
          <CategoryChips
            categories={formboxCategories}
            value={selectedCategory}
            onChange={(c) => setSelectedCategory(c)}
            isLoading={formboxCategoriesQuery.isLoading}
          />
        )}
        {/* Loading State */}
        {currentTemplates?.isLoading && (
          <div className="mt-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {loadingItems.map((_, index) => (
              <Card
                key={index}
                aria-hidden
                className="h-[310px] w-full animate-pulse rounded-xl border border-gray-200 bg-gray-100/90 dark:border-gray-700 dark:bg-gray-800/70"
              />
            ))}
          </div>
        )}

        {/* Templates Grid */}
        {!currentTemplates.isLoading && !isEmpty(data) && (
          <div className="mt-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {data?.map((template) => (
              <TemplateCard
                key={template?.id}
                template={template}
                orgId={orgId}
                userRole={userRole?.role}
                isFormboxTemplate={activeTab === "formbox"}
              />
            ))}
          </div>
        )}

        {/* Empty States */}
        {!currentTemplates.isLoading && isEmpty(data) && !noSearchResults && (
          <div className="mt-6 rounded-xl border border-gray-300 p-44">
            <EmptyState
              title={
                activeTab === "formbox"
                  ? "No official templates found"
                  : "No custom templates yet"
              }
              subtitle={
                activeTab === "formbox"
                  ? "Try adjusting your search terms to find what you're looking for."
                  : "Create your first template by saving a form as a template, or duplicate one from our official collection."
              }
              icon={<IconTemplate size={40} />}
              actionButton={
                activeTab === "organization" ? (
                  <>
                    {userRole?.role === "viewer" ? (
                      <AdminRequiredTooltip message="You need to be a Admin or Member to create a template">
                        <Button leftIcon={<IconPlus size={16} />} disabled>
                          Create form
                        </Button>
                      </AdminRequiredTooltip>
                    ) : (
                      <Link href={`/dashboard/${orgId}/forms`}>
                        <Button leftIcon={<IconPlus size={16} />}>
                          Create form
                        </Button>
                      </Link>
                    )}
                  </>
                ) : undefined
              }
            />
          </div>
        )}

        {!currentTemplates.isLoading && noSearchResults && (
          <div className="mt-6 rounded-xl border border-gray-300 p-44">
            <EmptyState
              title="No search results"
              subtitle="We couldn't find any templates matching your search. Try different keywords or browse all templates."
              icon={<IconFileDescription size={40} />}
            />
          </div>
        )}

        {/* Infinite Scroll Loader */}
        <div ref={ref} className="mt-8 flex items-center justify-center">
          {currentTemplates.isFetchingNextPage && (
            <div className="flex items-center space-x-2 text-gray-600">
              <Loader className="h-6 w-6" />
              <span className="text-sm">Loading more templates...</span>
            </div>
          )}
        </div>
      </div>
    </MaxWidthWrapper>
  );
}
