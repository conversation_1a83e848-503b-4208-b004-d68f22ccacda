"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import { Loader } from "@/components/ui/loader";
import { MaxWidthWrapper } from "@/components/ui/max-width-wrapper";
import { useDialog } from "@/hooks/use-dialog";
import { useOrgMemberRole } from "@/queries/org.queries";
import {
  useTemplateById,
  useTemplateDeleteMutation,
  useTemplateDuplicateMutation,
} from "@/queries/template.queries";
import { useAuthUser } from "@/queries/user.queries";
import type { FormByIdOutput, FormField } from "@/types/form.types";
import { getBadgeVariantForCategory } from "@/utils/get-badge-variant-for-category";
import {
  IconArrowLeft,
  IconAt,
  IconChevronDown,
  IconCircleCheck,
  IconCopy,
  IconHash,
  IconHeading,
  IconPageBreak,
  IconPencil,
  IconPhone,
  IconPlayerPlay,
  IconSquareCheck,
  IconStar,
  IconTemplate,
  IconTrash,
  IconUpload,
} from "@tabler/icons-react";
import { Calendar } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";
import { CgDetailsLess, CgDetailsMore } from "react-icons/cg";
import { toast } from "sonner";
import { FormBuilderPreview } from "../forms/form-builder-preview";
import { TemplateEditDialog } from "./template-edit-dialog";
import { UseTemplateDialog } from "./use-template-dialog";

// =============================================================================
// TYPES
// =============================================================================

interface Props {
  templateId: string;
  orgId: string;
}

interface TemplateCardProps {
  template: NonNullable<ReturnType<typeof useTemplateById>["data"]>;
}

interface ActionButtonsProps {
  canDuplicate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  onUseTemplate: () => void;
  onDuplicate: () => void;
  onEdit: () => void;
  onDelete: () => void;
  isDuplicating: boolean;
}

interface TemplateFieldsProps {
  fields: FormField[];
}

// =============================================================================
// SUB-COMPONENTS
// =============================================================================

/**
 * Template information card displaying name, description, and metadata
 */
function TemplateCard({ template }: TemplateCardProps) {
  return (
    <Card className="p-6">
      <div className="space-y-4">
        {/* Header section */}
        <div className="flex items-start gap-4">
          {/* Title and badges */}
          <div className="min-w-0 flex-1">
            <h3 className="text-lg font-semibold text-gray-900">
              {template.name}
            </h3>
          </div>
        </div>

        {/* Description */}
        {template.description && (
          <p className="line-clamp-3 text-sm leading-relaxed text-gray-600">
            {template.description}
          </p>
        )}

        {/* Divider */}
        <div className="h-px bg-gray-100" />

        {/* Metadata pills */}
        <div className="flex flex-wrap items-center gap-2">
          <Badge variant="gray" className="text-xs">
            {(template.fields?.length || 0).toLocaleString()} fields
          </Badge>
          {template.category && (
            <Badge
              variant={getBadgeVariantForCategory(template.category)}
              className="text-xs capitalize"
            >
              {template.category}
            </Badge>
          )}
          {template.isFormboxTemplate && (
            <Badge variant="blue" className="text-xs">
              Official
            </Badge>
          )}
        </div>
      </div>
    </Card>
  );
}

/**
 * Action buttons for template operations
 */
function ActionButtons({
  canDuplicate,
  canEdit,
  canDelete,
  onUseTemplate,
  onDuplicate,
  onEdit,
  onDelete,
  isDuplicating,
}: ActionButtonsProps) {
  return (
    <div className="flex flex-col gap-3 md:flex-row md:items-center md:gap-2">
      <Button
        size="sm"
        onClick={onUseTemplate}
        className="w-full rounded-lg md:flex-1"
        leftIcon={<IconPlayerPlay size={16} />}
      >
        Use
      </Button>

      {canEdit && (
        <Button
          size="sm"
          variant="outline"
          onClick={onEdit}
          className="w-full rounded-lg md:flex-1"
          leftIcon={<IconPencil size={16} />}
        >
          Edit
        </Button>
      )}

      {canDuplicate && (
        <Button
          size="sm"
          variant="outline"
          onClick={onDuplicate}
          loading={isDuplicating}
          className="w-full rounded-lg md:flex-1"
          leftIcon={<IconCopy size={16} />}
        >
          Duplicate
        </Button>
      )}

      {canDelete && (
        <Button
          size="sm"
          variant="outline"
          onClick={onDelete}
          className="w-full rounded-lg md:flex-1"
          leftIcon={<IconTrash size={16} className="text-red-600" />}
        >
          Delete
        </Button>
      )}
    </div>
  );
}

/**
 * Individual field card component
 */
function FieldCard({ field }: { field: FormField }) {
  const getFieldIcon = (subtypeOrType: string) => {
    const iconMap: Record<string, React.ReactElement> = {
      short_answer: <CgDetailsLess size={16} />,
      long_answer: <CgDetailsMore size={16} />,
      email: <IconAt size={16} />,
      phone: <IconPhone size={16} />,
      number: <IconHash size={16} />,
      date: <Calendar size={16} />,
      rating: <IconStar size={16} />,
      file_upload: <IconUpload size={16} />,
      single_choice: <IconCircleCheck size={16} />,
      multiple_choice: <IconSquareCheck size={16} />,
      dropdown: <IconChevronDown size={16} />,
      heading: <IconHeading size={16} />,
      page: <IconPageBreak size={16} />,
    };
    return iconMap[subtypeOrType] || <IconTemplate size={16} />;
  };

  const formatFieldType = (key: string | undefined) => {
    if (!key) return "Unknown";
    return key
      .split("_")
      .map((p) => (p ? p[0]?.toUpperCase() + p.slice(1) : ""))
      .join(" ");
  };

  const kind = field.subtype || field.type;
  const hint = field.description || "";

  return (
    <Card key={field.id} className="group relative rounded-xl p-5">
      {/* Header with icon, title */}
      <div className="mb-3 flex items-center">
        <div className="flex min-w-0 items-center gap-3">
          <div className="flex h-9 w-9 shrink-0 items-center justify-center rounded-md border border-gray-200 bg-gray-50 text-gray-500">
            {getFieldIcon(kind)}
          </div>
          <div className="flex min-w-0 items-center gap-2">
            <h5 className="text-sm leading-snug font-medium break-words text-gray-900">
              {field.label}
            </h5>
          </div>
        </div>
      </div>

      {/* Description */}
      {hint && (
        <p className="mb-3 line-clamp-2 text-sm text-gray-600">{hint}</p>
      )}

      {/* Footer with meta badges */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Badge variant="gray" className="text-xs">
            {formatFieldType(field.subtype)}
          </Badge>

          {field.options && field.options.length > 0 && (
            <Badge variant="blue" className="text-xs">
              {field.options.length} options
            </Badge>
          )}

          {kind === "rating" && field.ratingCount && (
            <Badge variant="yellow" className="text-xs">
              {field.ratingCount} stars
            </Badge>
          )}

          {field.required && <Badge variant="red">Required</Badge>}
        </div>
      </div>
    </Card>
  );
}

/**
 * Template fields list component
 */
function TemplateFields({ fields }: TemplateFieldsProps) {
  if (!fields || fields.length === 0) {
    return (
      <Card className="rounded-xl border-0 p-6 text-center shadow-sm ring-1 ring-gray-200/70">
        <IconTemplate className="mx-auto h-8 w-8 text-gray-400" />
        <p className="mt-2 text-sm text-gray-500">No fields in this template</p>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {fields.map((field) => (
        <FieldCard key={field.id} field={field} />
      ))}
    </div>
  );
}

// =============================================================================
// MAIN COMPONENT
// =============================================================================

/**
 * Template Preview View Component
 *
 * Displays a template preview with sidebar containing template information,
 * action buttons, and field list alongside a form preview.
 *
 * Features:
 * - Template metadata and description
 * - Use/duplicate template actions
 * - Field list with icons and badges
 * - Live form preview
 */
export function TemplatePreviewView({ templateId, orgId }: Props) {
  // =============================================================================
  // HOOKS & STATE
  // =============================================================================

  const router = useRouter();
  const [showUseDialog, showUseDialogHandlers] = useDialog();
  const [showEditDialog, showEditDialogHandlers] = useDialog();
  const [deleteDialogOpen, deleteDialogHandlers] = useDialog();

  const user = useAuthUser();
  const { data: userRole } = useOrgMemberRole(user?.id as string, orgId);
  const template = useTemplateById({ id: templateId, orgId });
  const duplicateMutation = useTemplateDuplicateMutation();
  const deleteMutation = useTemplateDeleteMutation();

  // =============================================================================
  // COMPUTED VALUES
  // =============================================================================

  const canDuplicate = Boolean(userRole?.role !== "viewer" && orgId);
  const canEdit = Boolean(
    userRole?.role !== "viewer" &&
      orgId &&
      template.data &&
      !template.data.isFormboxTemplate &&
      template.data.organizationId === orgId,
  );
  const canDelete = Boolean(
    userRole?.role !== "viewer" &&
      orgId &&
      template.data &&
      !template.data.isFormboxTemplate &&
      template.data.organizationId === orgId,
  );

  // Convert template to form-like structure for preview
  const templateAsForm = template.data
    ? createFormFromTemplate(template.data)
    : null;

  // =============================================================================
  // EVENT HANDLERS
  // =============================================================================

  const handleDuplicate = async () => {
    if (!template.data) return;

    try {
      const duplicatedTemplate = await duplicateMutation.mutateAsync({
        id: template.data.id,
        organizationId: orgId,
      });
      toast.success("Template duplicated successfully");
      router.push(
        `/dashboard/${orgId}/templates/${duplicatedTemplate.id}/preview`,
      );
    } catch (error) {
      console.error("Failed to duplicate template:", error);
    }
  };

  const handleGoBack = () => {
    if (orgId) {
      router.push(`/dashboard/${orgId}/templates`);
    } else {
      router.back();
    }
  };

  const handleUseTemplate = () => {
    showUseDialogHandlers.open();
  };

  const handleEdit = () => {
    showEditDialogHandlers.open();
  };

  const handleDelete = () => {
    deleteDialogHandlers.open();
  };

  const handleDeleteConfirm = async () => {
    if (!template.data) return;

    try {
      await deleteMutation.mutateAsync({ id: template.data.id });
      toast.success("Template deleted successfully");
      router.push(`/dashboard/${orgId}/templates`);
    } catch (error) {
      console.error("Failed to delete template:", error);
    }
  };

  // =============================================================================
  // LOADING & ERROR STATES
  // =============================================================================

  if (template.isLoading) {
    return (
      <div className="flex h-[calc(100vh-4rem)] items-center justify-center pt-16">
        <Loader />
      </div>
    );
  }

  if (template.error || !template.data) {
    return (
      <MaxWidthWrapper className="py-10 pt-16">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900">
            Template not found
          </h2>
          <p className="mt-2 text-gray-600">
            The template you&apos;re looking for doesn&apos;t exist or you
            don&apos;t have permission to view it.
          </p>
          <Button className="mt-4" onClick={handleGoBack}>
            Go back
          </Button>
        </div>
      </MaxWidthWrapper>
    );
  }

  // =============================================================================
  // RENDER
  // =============================================================================

  return (
    <div className="flex">
      {/* Left Sidebar - Template Overview */}
      <div className="fixed top-16 left-0 z-10 h-[calc(100vh-4rem)] w-full border-r border-gray-200 bg-gradient-to-b from-white to-gray-50 md:w-[420px]">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="sticky top-0 z-20 border-b border-gray-200/80 bg-gradient-to-b from-white to-gray-50 p-6">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" onClick={handleGoBack}>
                <IconArrowLeft size={16} />
              </Button>
              <div className="flex-1">
                <h1 className="text-lg font-semibold text-gray-900">
                  Template Preview
                </h1>
                <p className="text-sm text-gray-500">
                  Preview, duplicate, or use this template
                </p>
              </div>
            </div>
          </div>

          {/* Template Info */}
          <div className="flex-1 overflow-y-auto p-6 pb-24">
            <div className="space-y-6">
              {/* Template Card */}
              <TemplateCard template={template.data} />

              {/* Action Buttons */}
              <ActionButtons
                canDuplicate={canDuplicate}
                canEdit={canEdit}
                canDelete={canDelete}
                onUseTemplate={handleUseTemplate}
                onDuplicate={handleDuplicate}
                onEdit={handleEdit}
                onDelete={handleDelete}
                isDuplicating={duplicateMutation.isPending}
              />

              {/* Template Fields */}
              <div>
                <h4 className="mb-2 text-xs font-semibold tracking-wide text-gray-500 uppercase">
                  Form fields ({template.data.fields?.length || 0})
                </h4>
                <TemplateFields fields={template.data.fields || []} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Form Preview (hidden on small screens) */}
      <div className="hidden flex-1 overflow-y-auto pb-10 md:ml-[420px] md:block">
        {templateAsForm && (
          <FormBuilderPreview form={templateAsForm as FormByIdOutput} />
        )}
      </div>

      {/* Use Template Dialog */}
      {showUseDialog && templateAsForm && (
        <UseTemplateDialog
          open={showUseDialog}
          onClose={showUseDialogHandlers.close}
          template={{
            ...templateAsForm,
            autoCloseDate: templateAsForm.autoCloseDate || undefined,
            autoCloseTime: templateAsForm.autoCloseTime || undefined,
            autoCloseTimezone: templateAsForm.autoCloseTimezone || undefined,
            maxResponses: templateAsForm.maxResponses || undefined,
            smtpHost: templateAsForm.smtpHost || undefined,
            smtpPassword: templateAsForm.smtpPassword || undefined,
            smtpPort: templateAsForm.smtpPort || undefined,
            smtpUsername: templateAsForm.smtpUsername || undefined,
            smtpSenderEmail: templateAsForm.smtpSenderEmail || undefined,
          }}
          orgId={orgId}
        />
      )}

      {/* Edit Template Dialog */}
      {showEditDialog && template.data && (
        <TemplateEditDialog
          template={template.data}
          orgId={orgId}
          open={showEditDialog}
          onClose={showEditDialogHandlers.close}
        />
      )}

      {/* Delete Template Dialog */}
      <DeleteDialog
        title="template"
        open={deleteDialogOpen}
        onClose={deleteDialogHandlers.close}
        onDelete={handleDeleteConfirm}
        loading={deleteMutation.isPending}
      />
    </div>
  );
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Converts template data to form structure for preview
 */
function createFormFromTemplate(
  template: NonNullable<ReturnType<typeof useTemplateById>["data"]>,
) {
  return {
    ...template,
    fields: template.fields || [],
    // Add required FormByIdOutput properties
    type: "hosted" as const,
    _count: { submissions: 0 },
    organization: null,
    // Ensure all required form properties exist with defaults
    removeFormboxBranding: template.removeFormboxBranding ?? false,
    sendEmailNotifications: template.sendEmailNotifications ?? true,
    emailsToNotify: template.emailsToNotify ?? [],
    submissionStorageDuration: template.submissionStorageDuration ?? "60",
    sendRespondantEmailNotifications:
      template.sendRespondantEmailNotifications ?? false,
    respondantEmailFromName: template.respondantEmailFromName ?? "",
    respondantEmailSubject: template.respondantEmailSubject ?? "",
    respondantEmailMessageHTML: template.respondantEmailMessageHTML ?? "",
    submitButtonText: template.submitButtonText ?? "Submit",
    limitResponses: template.limitResponses ?? false,
    isClosed: template.isClosed ?? false,
    autoCloseEnabled: template.autoCloseEnabled ?? false,
    useCustomRedirect: template.useCustomRedirect ?? false,
    customSuccessUrl: template.customSuccessUrl ?? "",
    webhookEnabled: template.webhookEnabled ?? false,
    webhookUrl: template.webhookUrl ?? "",
    customHoneypot: template.customHoneypot ?? "",
    googleRecaptchaEnabled: template.googleRecaptchaEnabled ?? false,
    googleRecaptchaSecretKey: template.googleRecaptchaSecretKey ?? "",
    allowedDomains: template.allowedDomains ?? "",
    allowedCountries: template.allowedCountries ?? "",
    ipBlacklist: template.ipBlacklist ?? "",
    accentColor: template.accentColor ?? "#030712",
    backgroundColor: template.backgroundColor ?? "#ffffff",
    buttonBackgroundColor: template.buttonBackgroundColor ?? "#030712",
    buttonBorderStyle: template.buttonBorderStyle ?? "rounded",
    buttonTextColor: template.buttonTextColor ?? "#ffffff",
    closeMessageDescription: template.closeMessageDescription ?? "",
    closeMessageTitle: template.closeMessageTitle ?? "",
    headerDescription: template.headerDescription ?? "",
    headerImage: template.headerImage ?? "",
    headerTitle: template.headerTitle ?? "",
    inputBorderStyle: template.inputBorderStyle ?? "rounded",
    logo: template.logo ?? "",
    pageMode: template.pageMode ?? "compact",
    saveAnswers: template.saveAnswers ?? false,
    showCustomClosedMessage: template.showCustomClosedMessage ?? false,
    textColor: template.textColor ?? "#000000",
    tpBackgroundColor: template.tpBackgroundColor ?? "#ffffff",
    tpButtonColor: template.tpButtonColor ?? "#030712",
    tpButtonText: template.tpButtonText ?? "",
    tpButtonUrl: template.tpButtonUrl ?? "",
    tpHeader: template.tpHeader ?? "",
    tpMessage: template.tpMessage ?? "",
    tpTextColor: template.tpTextColor ?? "#030712",
    useCustomThankYouPage: template.useCustomThankYouPage ?? false,
    tpButtonBackgroundColor: template.tpButtonBackgroundColor ?? "#f3f4f6",
    smtpEnabled: template.smtpEnabled ?? false,
    smtpHost: template.smtpHost ?? null,
    smtpPassword: template.smtpPassword ?? null,
    smtpPort: template.smtpPort ?? null,
    smtpUsername: template.smtpUsername ?? null,
    smtpSenderEmail: template.smtpSenderEmail ?? null,
    autoCloseDate: template.autoCloseDate ?? null,
    autoCloseTime: template.autoCloseTime ?? null,
    autoCloseTimezone: template.autoCloseTimezone ?? null,
    maxResponses: template.maxResponses ?? null,
  };
}
