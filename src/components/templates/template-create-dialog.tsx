"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  type DialogProps,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { templateCreateSchema } from "@/libs/schemas/template.schemas";
import { useTemplateCreateMutation } from "@/queries/template.queries";
import type { FormByIdOutput } from "@/types/form.types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import type { z } from "zod";

type TemplateCreateFormData = z.infer<typeof templateCreateSchema>;

interface Props extends DialogProps {
  form: FormByIdOutput | null;
  orgId: string;
  onClose: () => void;
}

export function TemplateCreateDialog({ open, onClose, form, orgId }: Props) {
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    resolver: zodResolver(templateCreateSchema),
    defaultValues: { name: form?.name ?? "", description: "" },
  });

  const createMutation = useTemplateCreateMutation();

  useEffect(() => {
    reset({ name: form?.name ?? "", description: "" });
  }, [form, reset]);

  const closeModal = () => {
    reset({ name: form?.name ?? "", description: "" });
    onClose();
  };

  const onSubmit = async (data: TemplateCreateFormData) => {
    if (!form) return;
    try {
      // Map form settings into template fields
      const templateSettings = {
        removeFormboxBranding: form?.removeFormboxBranding ?? false,
        sendEmailNotifications: form?.sendEmailNotifications ?? true,
        emailsToNotify: form?.emailsToNotify ?? [],
        submissionStorageDuration: form?.submissionStorageDuration ?? "60",
        sendRespondantEmailNotifications:
          form?.sendRespondantEmailNotifications ?? false,
        respondantEmailFromName: form?.respondantEmailFromName ?? "",
        respondantEmailSubject: form?.respondantEmailSubject ?? "",
        respondantEmailMessageHTML: form?.respondantEmailMessageHTML ?? "",
        submitButtonText: form?.submitButtonText ?? "Submit",
        limitResponses: form?.limitResponses ?? false,
        isClosed: form?.isClosed ?? false,
        autoCloseEnabled: form?.autoCloseEnabled ?? false,
        autoCloseDate: form?.autoCloseDate || undefined,
        autoCloseTime: form?.autoCloseTime || undefined,
        autoCloseTimezone: form?.autoCloseTimezone || undefined,
        maxResponses: form?.maxResponses || undefined,
        useCustomRedirect: form?.useCustomRedirect ?? false,
        customSuccessUrl: form?.customSuccessUrl ?? "",
        webhookEnabled: form?.webhookEnabled ?? false,
        webhookUrl: form?.webhookUrl ?? "",
        customHoneypot: form?.customHoneypot ?? "",
        googleRecaptchaEnabled: form?.googleRecaptchaEnabled ?? false,
        googleRecaptchaSecretKey: form?.googleRecaptchaSecretKey ?? "",
        allowedDomains: form?.allowedDomains ?? "",
        allowedCountries: form?.allowedCountries ?? "",
        ipBlacklist: form?.ipBlacklist ?? "",
        accentColor: form?.accentColor ?? "#030712",
        backgroundColor: form?.backgroundColor ?? "#ffffff",
        buttonBackgroundColor: form?.buttonBackgroundColor ?? "#030712",
        buttonBorderStyle: form?.buttonBorderStyle ?? "rounded",
        buttonTextColor: form?.buttonTextColor ?? "#ffffff",
        closeMessageDescription: form?.closeMessageDescription ?? "",
        closeMessageTitle: form?.closeMessageTitle ?? "",
        headerDescription: form?.headerDescription ?? "",
        headerImage: form?.headerImage ?? "",
        headerTitle: form?.headerTitle ?? "",
        inputBorderStyle: form?.inputBorderStyle ?? "rounded",
        logo: form?.logo ?? "",
        pageMode: form?.pageMode ?? "compact",
        saveAnswers: form?.saveAnswers ?? false,
        showCustomClosedMessage: form?.showCustomClosedMessage ?? false,
        textColor: form?.textColor ?? "#000000",
        tpBackgroundColor: form?.tpBackgroundColor ?? "#ffffff",
        tpButtonColor: form?.tpButtonColor ?? "#030712",
        tpButtonText: form?.tpButtonText ?? "",
        tpButtonUrl: form?.tpButtonUrl ?? "",
        tpHeader: form?.tpHeader ?? "",
        tpMessage: form?.tpMessage ?? "",
        tpTextColor: form?.tpTextColor ?? "#030712",
        useCustomThankYouPage: form?.useCustomThankYouPage ?? false,
        tpButtonBackgroundColor: form?.tpButtonBackgroundColor ?? "#f3f4f6",
        smtpHost: form?.smtpHost || undefined,
        smtpPassword: form?.smtpPassword || undefined,
        smtpPort: form?.smtpPort || undefined,
        smtpUsername: form?.smtpUsername || undefined,
        smtpEnabled: form?.smtpEnabled ?? false,
        smtpSenderEmail: form?.smtpSenderEmail || undefined,
      } as const;

      const filteredTemplateSettings = Object.fromEntries(
        Object.entries(templateSettings).filter(
          ([_, v]) => v !== undefined && v !== null,
        ),
      );

      const created = await createMutation.mutateAsync({
        name: data.name,
        description: data.description || "",
        category: "",
        organizationId: orgId,
        isFormboxTemplate: false,
        ...(filteredTemplateSettings as Record<string, unknown>),
        fields: form.fields ?? [],
      });

      if (created?.id) {
        router.push(`/dashboard/${orgId}/templates/${created.id}/preview`);
      }

      closeModal();
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[465px]">
        <DialogHeader>
          <DialogTitle>Create template</DialogTitle>
        </DialogHeader>

        <DialogDescription>
          Create a new template from this form. You can change the template name
          and description before creating.
        </DialogDescription>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Input
              label="Template name"
              {...register("name")}
              error={errors.name !== undefined}
              errorMessage={errors?.name?.message}
              placeholder="Enter template name"
              allowAutoComplete={false}
            />
          </div>

          <div>
            <Textarea
              label="Description"
              {...register("description")}
              placeholder="Enter template description (optional)"
              rows={3}
            />
          </div>

          <DialogFooter className="mt-6">
            <Button
              variant="outline"
              className="mt-2 sm:mt-0"
              onClick={closeModal}
              type="button"
            >
              Cancel
            </Button>
            <Button loading={isSubmitting} type="submit">
              Create template
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
