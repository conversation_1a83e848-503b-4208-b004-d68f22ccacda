"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Header,
  type DialogProps,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useTemplateUpdateMutation } from "@/queries/template.queries";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

// =============================================================================
// SCHEMAS
// =============================================================================

const templateEditSchema = z.object({
  name: z.string().min(1, "Template name is a required field."),
  description: z.string().optional(),
  category: z.string().optional(),
});

type TemplateEditFormData = z.infer<typeof templateEditSchema>;

// =============================================================================
// TYPES
// =============================================================================

interface Template {
  id: string;
  name: string;
  description?: string | null;
  category?: string | null;
  organizationId?: string | null;
  isFormboxTemplate: boolean;
}

interface Props extends DialogProps {
  template: Template;
  orgId?: string;
  onClose: () => void;
}

// =============================================================================
// COMPONENT
// =============================================================================

/**
 * Template Edit Dialog Component
 *
 * Allows users to edit template name, description, and category.
 * Only works for non-Formbox templates that the user owns.
 */
export function TemplateEditDialog({ template, orgId, open, onClose }: Props) {
  // =============================================================================
  // FORM SETUP
  // =============================================================================

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<TemplateEditFormData>({
    resolver: zodResolver(templateEditSchema),
    defaultValues: {
      name: template.name || "",
      description: template.description || "",
      category: template.category || "",
    },
  });

  // =============================================================================
  // MUTATIONS
  // =============================================================================

  const updateMutation = useTemplateUpdateMutation(orgId);

  // =============================================================================
  // HANDLERS
  // =============================================================================

  const onSubmit = async (data: TemplateEditFormData) => {
    try {
      await updateMutation.mutateAsync({
        id: template.id,
        name: data.name,
        description: data.description || "",
        category: "",
      });
      closeModal();
    } catch (error) {
      console.error("Failed to update template:", error);
    }
  };

  const closeModal = () => {
    reset();
    onClose();
  };

  // =============================================================================
  // PERMISSION CHECK
  // =============================================================================

  const canEdit = !template.isFormboxTemplate && orgId;

  if (!canEdit) {
    return null; // Don't render if user can't edit
  }

  // =============================================================================
  // RENDER
  // =============================================================================

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[465px]">
        <DialogHeader>
          <DialogTitle>Edit Template</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Template Name */}
          <div>
            <Input
              label="Template name"
              {...register("name")}
              error={errors.name !== undefined}
              errorMessage={errors?.name?.message}
              placeholder="Enter template name"
              allowAutoComplete={false}
            />
          </div>

          {/* Template Description */}
          <div>
            <Textarea
              label="Description"
              {...register("description")}
              placeholder="Enter template description (optional)"
              rows={3}
            />
          </div>

          {/* Footer */}
          <DialogFooter className="mt-6">
            <Button
              variant="outline"
              className="mt-2 sm:mt-0"
              onClick={closeModal}
              type="button"
            >
              Cancel
            </Button>
            <Button loading={isSubmitting} type="submit">
              Save Changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
