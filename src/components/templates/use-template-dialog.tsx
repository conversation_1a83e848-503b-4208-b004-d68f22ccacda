"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useFormAddMutation } from "@/queries/form.queries";
import type { FormCreateFields, FormField } from "@/types/form.types";
import { formCreateSchema } from "@/utils/schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

interface Props extends DialogProps {
  template: {
    id: string;
    name: string;
    fields?: FormField[];
    // Form settings - required fields with defaults
    removeFormboxBranding: boolean;
    sendEmailNotifications: boolean;
    emailsToNotify: string[];
    submissionStorageDuration: string;
    sendRespondantEmailNotifications: boolean;
    respondantEmailFromName: string;
    respondantEmailSubject: string;
    respondantEmailMessageHTML: string;
    submitButtonText: string;
    limitResponses: boolean;
    isClosed: boolean;
    autoCloseEnabled: boolean;
    autoCloseDate?: Date;
    autoCloseTime?: string;
    autoCloseTimezone?: string;
    maxResponses?: number;
    useCustomRedirect: boolean;
    customSuccessUrl: string;
    webhookEnabled: boolean;
    webhookUrl: string;
    customHoneypot: string;
    googleRecaptchaEnabled: boolean;
    googleRecaptchaSecretKey: string;
    allowedDomains: string;
    allowedCountries: string;
    ipBlacklist: string;
    // Styling options - required fields with defaults
    accentColor: string;
    backgroundColor: string;
    buttonBackgroundColor: string;
    buttonBorderStyle: string;
    buttonTextColor: string;
    closeMessageDescription: string;
    closeMessageTitle: string;
    headerDescription: string;
    headerImage: string;
    headerTitle: string;
    inputBorderStyle: string;
    logo: string;
    pageMode: string;
    saveAnswers: boolean;
    showCustomClosedMessage: boolean;
    textColor: string;
    tpBackgroundColor: string;
    tpButtonColor: string;
    tpButtonText: string;
    tpButtonUrl: string;
    tpHeader: string;
    tpMessage: string;
    tpTextColor: string;
    useCustomThankYouPage: boolean;
    tpButtonBackgroundColor: string;
    // SMTP settings - nullable fields
    smtpHost?: string;
    smtpPassword?: string;
    smtpPort?: number;
    smtpUsername?: string;
    smtpEnabled: boolean;
    smtpSenderEmail?: string;
  };
  orgId: string;
  onClose: () => void;
}

export function UseTemplateDialog({ open, onClose, template, orgId }: Props) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    resolver: zodResolver(formCreateSchema),
    defaultValues: { name: template?.name ?? "", organizationId: orgId },
  });

  const createMutation = useFormAddMutation();

  const closeModal = () => {
    reset();
    onClose();
  };

  const onSubmit = async (data: FormCreateFields) => {
    try {
      // Extract all template settings and merge with form data, providing defaults
      const templateSettings = {
        removeFormboxBranding: template?.removeFormboxBranding ?? false,
        sendEmailNotifications: template?.sendEmailNotifications ?? true,
        emailsToNotify: template?.emailsToNotify ?? [],
        submissionStorageDuration: template?.submissionStorageDuration ?? "60",
        sendRespondantEmailNotifications:
          template?.sendRespondantEmailNotifications ?? false,
        respondantEmailFromName: template?.respondantEmailFromName ?? "",
        respondantEmailSubject: template?.respondantEmailSubject ?? "",
        respondantEmailMessageHTML: template?.respondantEmailMessageHTML ?? "",
        submitButtonText: template?.submitButtonText ?? "Submit",
        limitResponses: template?.limitResponses ?? false,
        isClosed: template?.isClosed ?? false,
        autoCloseEnabled: template?.autoCloseEnabled ?? false,
        autoCloseDate: template?.autoCloseDate || undefined,
        autoCloseTime: template?.autoCloseTime || undefined,
        autoCloseTimezone: template?.autoCloseTimezone || undefined,
        maxResponses: template?.maxResponses || undefined,
        useCustomRedirect: template?.useCustomRedirect ?? false,
        customSuccessUrl: template?.customSuccessUrl ?? "",
        webhookEnabled: template?.webhookEnabled ?? false,
        webhookUrl: template?.webhookUrl ?? "",
        customHoneypot: template?.customHoneypot ?? "",
        googleRecaptchaEnabled: template?.googleRecaptchaEnabled ?? false,
        googleRecaptchaSecretKey: template?.googleRecaptchaSecretKey ?? "",
        allowedDomains: template?.allowedDomains ?? "",
        allowedCountries: template?.allowedCountries ?? "",
        ipBlacklist: template?.ipBlacklist ?? "",
        accentColor: template?.accentColor ?? "#030712",
        backgroundColor: template?.backgroundColor ?? "#ffffff",
        buttonBackgroundColor: template?.buttonBackgroundColor ?? "#030712",
        buttonBorderStyle: template?.buttonBorderStyle ?? "rounded",
        buttonTextColor: template?.buttonTextColor ?? "#ffffff",
        closeMessageDescription: template?.closeMessageDescription ?? "",
        closeMessageTitle: template?.closeMessageTitle ?? "",
        headerDescription: template?.headerDescription ?? "",
        headerImage: template?.headerImage ?? "",
        headerTitle: template?.headerTitle ?? "",
        inputBorderStyle: template?.inputBorderStyle ?? "rounded",
        logo: template?.logo ?? "",
        pageMode: template?.pageMode ?? "compact",
        saveAnswers: template?.saveAnswers ?? false,
        showCustomClosedMessage: template?.showCustomClosedMessage ?? false,
        textColor: template?.textColor ?? "#000000",
        tpBackgroundColor: template?.tpBackgroundColor ?? "#ffffff",
        tpButtonColor: template?.tpButtonColor ?? "#030712",
        tpButtonText: template?.tpButtonText ?? "",
        tpButtonUrl: template?.tpButtonUrl ?? "",
        tpHeader: template?.tpHeader ?? "",
        tpMessage: template?.tpMessage ?? "",
        tpTextColor: template?.tpTextColor ?? "#030712",
        useCustomThankYouPage: template?.useCustomThankYouPage ?? false,
        tpButtonBackgroundColor: template?.tpButtonBackgroundColor ?? "#f3f4f6",
        smtpHost: template?.smtpHost || undefined,
        smtpPassword: template?.smtpPassword || undefined,
        smtpPort: template?.smtpPort || undefined,
        smtpUsername: template?.smtpUsername || undefined,
        smtpEnabled: template?.smtpEnabled ?? false,
        smtpSenderEmail: template?.smtpSenderEmail || undefined,
      };

      // Remove undefined and null values (only keep defined values)
      const filteredTemplateSettings = Object.fromEntries(
        Object.entries(templateSettings).filter(
          ([_, v]) => v !== undefined && v !== null,
        ),
      );

      await createMutation.mutateAsync({
        ...data,
        ...filteredTemplateSettings,
        type: "hosted",
        fields: template?.fields,
      });

      closeModal();
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Use template</DialogTitle>
        </DialogHeader>

        <DialogDescription>
          Create a new form from this template. You can change the form name
          before creating.
        </DialogDescription>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div>
            <Input
              label="Form name"
              {...register("name")}
              allowAutoComplete={false}
              error={errors.name !== undefined}
              errorMessage={errors?.name?.message}
            />
          </div>

          <DialogFooter className="mt-6">
            <Button
              variant="outline"
              className="mt-2 sm:mt-0"
              onClick={closeModal}
              type="button"
            >
              Close
            </Button>
            <Button loading={isSubmitting} type="submit">
              Create form
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
