"use client";

import { TemplateCardActionsMenu } from "@/components/templates/template-card-actions-menu";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card"; // Removed CardDescription
import { useDialog } from "@/hooks/use-dialog";
import type { Template } from "@/types/template.types";
import { getBadgeVariantForCategory } from "@/utils/get-badge-variant-for-category";
import Link from "next/link";
import { UseTemplateDialog } from "./use-template-dialog";

interface Props {
  template: Template;
  orgId: string;
  userRole?: string;
  isFormboxTemplate: boolean;
}

export function TemplateCard({ template, orgId, isFormboxTemplate }: Props) {
  const [openDialog, openDialogHandlers] = useDialog();

  return (
    <Card
      className="relative flex h-full flex-col overflow-hidden border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900"
      aria-label={template.name}
    >
      {/* Neutral SVG Form Preview */}
      <div className="relative aspect-[16/9] w-full bg-gradient-to-br from-gray-100 via-gray-50 to-white dark:from-gray-800 dark:via-gray-900 dark:to-gray-950">
        <div className="absolute top-3 left-3 z-20 flex items-center gap-2">
          {template.isFormboxTemplate ? (
            <Badge variant="blue" className="text-xs">
              Official
            </Badge>
          ) : null}
          {template.category ? (
            <Badge
              variant={getBadgeVariantForCategory(template.category)}
              className="text-xs capitalize"
            >
              {template.category}
            </Badge>
          ) : null}
        </div>
        <div className="absolute top-2 right-2 z-10">
          <TemplateCardActionsMenu
            templateId={template.id}
            isFormboxTemplate={isFormboxTemplate}
            orgId={orgId}
          />
        </div>
        <div className="absolute inset-0 flex items-center justify-center px-6 pt-6">
          <svg
            viewBox="0 0 240 150"
            role="img"
            aria-label={`${template.name} form preview`}
            className="h-full w-full text-gray-400"
          >
            {/* Outer container with only top corners rounded */}
            <path
              d="M0 12C0 5.373 5.373 0 12 0H228C234.627 0 240 5.373 240 12V150H0Z"
              className="fill-white stroke-gray-200 dark:fill-gray-950 dark:stroke-gray-800"
            />
            <rect
              x="20"
              y="20"
              width="80"
              height="10"
              rx="4"
              className="fill-gray-300/70 dark:fill-gray-600/60"
            />
            <rect
              x="20"
              y="40"
              width="200"
              height="12"
              rx="4"
              className="fill-gray-100 dark:fill-gray-800"
            />
            <rect
              x="20"
              y="60"
              width="200"
              height="12"
              rx="4"
              className="fill-gray-100 dark:fill-gray-800"
            />
            <rect
              x="20"
              y="80"
              width="120"
              height="12"
              rx="4"
              className="fill-gray-100 dark:fill-gray-800"
            />
            <rect
              x="20"
              y="110"
              width="70"
              height="16"
              rx="6"
              className="fill-gray-300/80 dark:fill-gray-700"
            />
            <rect
              x="100"
              y="110"
              width="70"
              height="16"
              rx="6"
              className="fill-gray-200/80 dark:fill-gray-800"
            />
          </svg>
        </div>
      </div>
      {/* Name Section */}
      <div className="border-t border-gray-200 px-5 py-3 dark:border-gray-800">
        <h3 className="line-clamp-1 text-base font-semibold tracking-tight text-gray-900 md:text-[17px] dark:text-gray-50">
          {template.name}
        </h3>
      </div>
      {/* Content */}
      <CardContent className="flex flex-1 flex-col px-5 pb-5">
        <p className="line-clamp-3 text-[13.5px] leading-relaxed text-gray-600 md:text-sm dark:text-gray-400">
          {template.description ||
            "A beautifully designed form template ready to use."}
        </p>
      </CardContent>
      {/* Actions */}
      <CardFooter className="mt-auto flex gap-2 px-5 pt-0 pb-5">
        <div className="flex-1">
          <Button
            size="sm"
            className="h-8 w-full font-medium"
            onClick={openDialogHandlers.open}
          >
            Use
          </Button>
        </div>
        <UseTemplateDialog
          open={openDialog}
          onClose={openDialogHandlers.close}
          template={template}
          orgId={orgId}
        />
        <Link
          href={`/dashboard/${orgId}/templates/${template.id}/preview`}
          className="flex-1"
        >
          <Button
            size="sm"
            variant="outline"
            className="h-8 w-full font-medium"
          >
            Preview
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
