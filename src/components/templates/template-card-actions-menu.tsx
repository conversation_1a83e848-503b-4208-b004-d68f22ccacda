"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useClipboard } from "@/hooks/use-clipboard";
import { useDialog } from "@/hooks/use-dialog";
import {
  useTemplateDeleteMutation,
  useTemplateDuplicateMutation,
} from "@/queries/template.queries";
import { IconCheck, IconCopy, IconDots, IconTrash } from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface Props {
  templateId: string;
  disabled?: boolean;
  isFormboxTemplate: boolean;
  orgId?: string;
}

export function TemplateCardActionsMenu({
  templateId,
  isFormboxTemplate,
  disabled = false,
  orgId,
}: Props) {
  const { copy, copied } = useClipboard();

  const [deleteDialogOpen, deleteDialogHandlers] = useDialog();

  const deleteTemplateMutation = useTemplateDeleteMutation();
  const duplicateTemplateMutation = useTemplateDuplicateMutation();
  const router = useRouter();

  const onCopyId = () => {
    copy(templateId);
    toast.success("Copied template ID");
  };

  const handleDuplicate = async () => {
    try {
      const data = await duplicateTemplateMutation.mutateAsync({
        id: templateId,
        organizationId: orgId,
      });

      if (data?.id) {
        // Navigate to duplicated template preview
        router.push(`/dashboard/${orgId}/templates/${data.id}/preview`);
      }
    } catch (err) {
      // error handling is surfaced by the mutation's onError toast
      console.error(err);
    }
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteTemplateMutation.mutateAsync({ id: templateId });
    } catch (err) {
      console.error(err);
      // error handling surfaced by mutation's onError if configured
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="h-auto p-1.5 text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
            disabled={disabled}
          >
            <IconDots size={18} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[170px]">
          <DropdownMenuItem onClick={onCopyId} disabled={disabled}>
            {copied ? (
              <IconCheck className="mr-2 h-4 w-4 text-green-500" />
            ) : (
              <IconCopy className="mr-2 h-4 w-4" />
            )}
            <span>Copy ID</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleDuplicate}>
            <IconCopy className="mr-2 h-4 w-4" />
            <span>Duplicate</span>
          </DropdownMenuItem>
          {!isFormboxTemplate && (
            <DropdownMenuItem
              className="text-red-500! hover:bg-red-500/5!"
              onClick={deleteDialogHandlers.open}
            >
              <IconTrash className="mr-2 h-4 w-4" />
              <span>Delete</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      {/* Delete confirmation dialog */}
      <DeleteDialog
        title="template"
        open={deleteDialogOpen}
        onClose={deleteDialogHandlers.close}
        onDelete={handleDeleteConfirm}
        loading={deleteTemplateMutation.isPending}
      />
    </>
  );
}
