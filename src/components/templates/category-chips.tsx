"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { IconCheck } from "@tabler/icons-react";

type Props = {
  categories?: string[];
  value?: string | undefined;
  onChange: (category?: string) => void;
  isLoading?: boolean;
};

export function CategoryChips({
  categories = [],
  value,
  onChange,
  isLoading,
}: Props) {
  if (isLoading) {
    // Render a few skeleton chips while categories load
    return (
      <div className="mt-3 flex w-full flex-wrap gap-2">
        {[0, 1, 2, 3, 4, 5].map((i) => (
          <Skeleton
            key={i}
            className="h-8 w-20 rounded-full first:w-[62px]"
            aria-hidden
          />
        ))}
      </div>
    );
  }

  if (!categories || categories.length === 0) return null;

  return (
    <div className="mt-3 flex w-full flex-wrap gap-2">
      <button
        type="button"
        onClick={() => onChange(undefined)}
        aria-pressed={value === undefined}
        className={`inline-flex cursor-pointer items-center gap-2 rounded-full px-3 py-1.5 text-sm transition-colors ${value === undefined ? "bg-primary text-white" : "bg-gray-100 text-gray-700"}`}
      >
        {value === undefined ? <IconCheck size={14} /> : null}
        <span className="line-clamp-1">All</span>
      </button>
      {categories.map((c) => {
        const selected = value === c;
        return (
          <button
            key={c}
            type="button"
            onClick={() => onChange(selected ? undefined : c)}
            aria-pressed={selected}
            className={`inline-flex cursor-pointer items-center gap-2 rounded-full px-3 py-1.5 text-sm capitalize transition-colors ${selected ? "bg-primary text-white" : "bg-gray-100 text-gray-700"}`}
          >
            {selected ? <IconCheck size={14} /> : null}
            <span className="line-clamp-1">{c}</span>
          </button>
        );
      })}
    </div>
  );
}
