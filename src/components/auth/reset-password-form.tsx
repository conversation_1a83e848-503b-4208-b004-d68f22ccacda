"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { AlertError } from "@/components/ui/alert-error";
import { AlertSuccess } from "@/components/ui/alert-success";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Logo } from "@/components/ui/logo";
import { authClient } from "@/libs/auth-client";
import { z } from "@/libs/zod";
import { cn } from "@/utils/tailwind-helpers";
import { IconArrowLeft } from "@tabler/icons-react";

const schema = z.object({
  password: z
    .string()
    .min(8, "Password must be at least 8 characters.")
    .regex(/[0-9]/, "Password must contain at least one number")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(
      /[!@#$%^&*()_\-+=[\]{};:,.<>?/~|]/,
      "Password must contain at least one special character",
    ),
});

type FormData = z.infer<typeof schema>;

type Props = {
  searchParams: { [key: string]: string | string[] | undefined };
} & React.ComponentPropsWithoutRef<"div">;

export function ResetPasswordForm({
  className,
  searchParams,
  ...props
}: Props) {
  const [errorMessage, setErrorMessage] = useState("");
  const [showSuccessMessage, setShowSuccessMessage] = useState("");

  const token = (searchParams?.token as string) ?? "";

  console.log("token: ", token);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      password: "",
    },
  });

  async function onSubmit(data: FormData) {
    setErrorMessage("");
    await authClient.resetPassword({
      newPassword: data.password,
      token,
      fetchOptions: {
        onSuccess: (ctx) => {
          reset({ password: "" });
          setShowSuccessMessage(
            "Password reset successfully. You may now login or close this tab.",
          );
        },
        onError: (ctx) => {
          setErrorMessage(ctx.error.message ?? "Something went wrong");
        },
      },
    });
  }

  return (
    <div
      className={cn(
        "flex h-full flex-col justify-center px-4 py-12 sm:px-6 lg:px-8",
        className,
      )}
      {...props}
    >
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex items-center justify-center">
          <Logo className="w-40" href="https://formbox.app" />
        </div>
      </div>
      <Card className="mt-6 px-10 pt-4 pb-12 sm:mx-auto sm:w-full sm:max-w-[500px]">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Reset your password</CardTitle>
          <CardDescription className="text-base">
            Enter your new password below to complete the reset process. Ensure
            its strong and secure.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AlertError message={errorMessage} className="mb-6" />
          <AlertSuccess message={showSuccessMessage} />

          {!showSuccessMessage && (
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid gap-6">
                <div className="grid gap-6">
                  <div className="grid gap-6">
                    <Input
                      id="password"
                      type="password"
                      label="New password"
                      {...register("password")}
                      error={errors.password !== undefined}
                      errorMessage={errors?.password?.message}
                    />
                  </div>
                  <Button
                    type="submit"
                    className="w-full"
                    loading={isSubmitting}
                  >
                    Reset password
                  </Button>
                </div>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
      <div className="mx-auto mt-2 max-w-sm">
        <Button
          href="/auth/login"
          variant="ghost"
          leftIcon={<IconArrowLeft size={16} />}
        >
          Back to login page
        </Button>
      </div>
    </div>
  );
}
