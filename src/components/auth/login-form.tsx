"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { AlertError } from "@/components/ui/alert-error";
import { AlertSuccess } from "@/components/ui/alert-success";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Logo } from "@/components/ui/logo";
// import GithubLogo from "@/images/github-logo.svg";
import GoogleLogo from "@/images/google-logo.svg";
import MicrosoftLogo from "@/images/microsoft-logo.svg";
import { authClient, signInWithProvider } from "@/libs/auth-client";
import { LoginSchema } from "@/libs/schemas/auth.schemas";
import { type LoginFormData } from "@/types/auth.types";
import { COMPANY_NAME, DEFAULT_LOGIN_REDIRECT } from "@/utils/constants";
import { cn } from "@/utils/tailwind-helpers";
import Image from "next/image";

interface LoginFormProps {
  searchParams: { [key: string]: string | string[] | undefined };
  className?: string;
}

export function LoginForm({ className, searchParams }: LoginFormProps) {
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState<string | undefined>();

  const callbackUrl = (searchParams.callbackUrl as string) ?? "";

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(data: LoginFormData) {
    setErrorMessage("");
    await authClient.signIn.email({
      email: data.email,
      password: data.password,
      callbackURL: callbackUrl || DEFAULT_LOGIN_REDIRECT,
      fetchOptions: {
        onSuccess: () => {
          if (callbackUrl) {
            return router.push(callbackUrl);
          }
          return router.push(DEFAULT_LOGIN_REDIRECT);
        },
        onError: (ctx) => {
          setErrorMessage(ctx.error.message);
        },
      },
    });
  }

  async function handleGoogleLogin() {
    await signInWithProvider("google", callbackUrl);
  }

  async function handleMicrosoftLogin() {
    await signInWithProvider("microsoft", callbackUrl);
  }

  // async function handleGithubLogin() {
  //   await signInWithProvider("github");
  // }

  async function handlePasskeyLogin() {
    setErrorMessage("");
    await authClient.signIn.passkey({
      fetchOptions: {
        onSuccess: () => {
          if (callbackUrl) {
            return router.push(callbackUrl);
          }
          return router.push(DEFAULT_LOGIN_REDIRECT);
        },
        onError: (ctx) => {
          setErrorMessage(ctx.error.message);
        },
      },
    });
  }

  return (
    <div
      className={cn(
        "flex h-full flex-col justify-center px-4 py-12 sm:px-6 lg:px-8",
        className,
      )}
    >
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex items-center justify-center">
          <Logo className="w-40" href="https://formbox.app" />
        </div>
      </div>
      <Card className="mt-6 px-10 pt-6 pb-10 sm:mx-auto sm:w-full sm:max-w-[500px]">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Welcome back</CardTitle>
          <CardDescription className="text-base">
            Enter your email below to login to your account
          </CardDescription>
        </CardHeader>

        <CardContent>
          <AlertError message={errorMessage} className="mb-6" />

          <AlertSuccess
            message={
              callbackUrl &&
              `You have been invited to join an organization on Formbox. Login to your account to accept the invitation.`
            }
            className="mb-6"
          />

          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid gap-6">
              <div className="grid gap-6">
                <div className="grid gap-2">
                  <Input
                    id="email"
                    type="email"
                    label="Email"
                    {...register("email")}
                    error={errors.email !== undefined}
                    errorMessage={errors?.email?.message}
                  />
                </div>
                <div className="grid gap-2">
                  <div className="flex items-center">
                    <Label htmlFor="password">Password</Label>
                    <Link
                      href="/auth/forgot-password"
                      className="ml-auto text-sm underline-offset-4 hover:underline"
                    >
                      Forgot password?
                    </Link>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    {...register("password")}
                    error={errors.password !== undefined}
                    errorMessage={errors?.password?.message}
                  />
                </div>
                <Button type="submit" className="w-full" loading={isSubmitting}>
                  Login
                </Button>
              </div>

              <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
                <span className="relative z-10 bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>

              <div className="justify-between space-y-2 md:flex md:space-y-0 md:space-x-2">
                {/* <Button
                  variant="outline"
                  className="w-full"
                  type="button"
                  onClick={handleGithubLogin}
                  leftIcon={
                    <Image
                      alt="Sign in with Github"
                      src={GithubLogo}
                      className="size-4"
                    />
                  }
                >
                  GitHub
                </Button> */}
                <Button
                  variant="outline"
                  className="w-full"
                  type="button"
                  onClick={handleGoogleLogin}
                  leftIcon={
                    <Image
                      alt="Sign in with Google"
                      src={GoogleLogo}
                      className="size-4"
                    />
                  }
                >
                  Google
                </Button>
                <Button
                  variant="outline"
                  className="w-full"
                  type="button"
                  onClick={handleMicrosoftLogin}
                  leftIcon={
                    <Image
                      alt="Sign in with Microsoft"
                      src={MicrosoftLogo}
                      className="size-4"
                    />
                  }
                >
                  Microsoft
                </Button>
                {/* <Button
                  variant="outline"
                  className="w-full"
                  type="button"
                  onClick={handlePasskeyLogin}
                  leftIcon={<IconKey className="size-4" />}
                >
                  Passkey
                </Button> */}
              </div>

              <div className="text-center text-sm">
                Don&apos;t have an account?{" "}
                <Link
                  href="/auth/signup"
                  className="underline underline-offset-4"
                >
                  Sign up
                </Link>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
      <div className="mx-auto mt-6 max-w-sm text-center text-sm text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary">
        <p>
          By signing up to create a new account, I accept {COMPANY_NAME}
          &apos;s{" "}
          <Link
            href="https://formbox.app/terms"
            target="_blank"
            rel="noopener noreferrer"
            className="font-semibold underline decoration-gray-400 underline-offset-4"
          >
            Terms of Service
          </Link>{" "}
          and{" "}
          <Link
            href="https://formbox.app/privacy"
            target="_blank"
            rel="noopener noreferrer"
            className="font-semibold underline decoration-gray-400 underline-offset-4"
          >
            Privacy Policy
          </Link>
          .
        </p>
      </div>
    </div>
  );
}
