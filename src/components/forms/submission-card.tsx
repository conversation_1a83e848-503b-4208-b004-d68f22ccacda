"use client";

import { SubmissionCardActionsMenu } from "@/components/forms/submission-card-actions-menu";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ToolTip } from "@/components/ui/tooltip";
import { type SubmissionOutput } from "@/types/submission.types";
import { Roles } from "@/types/utility.types";
import {
  type DocumentMimeType,
  type ImageMimeType,
  type SpreadsheetMimeType,
  DOCUMENT_MIME_TYPE,
  IMAGE_MIME_TYPE,
  SPREADSHEET_MIME_TYPE,
} from "@/utils/constants";
import { formatDate } from "@/utils/format-date";
import { formatFileSize } from "@/utils/format-file-size";
import { getCountryName } from "@/utils/get-country";
import {
  IconBrowser,
  IconClock,
  IconDevices,
  IconDownload,
  IconFileSpreadsheet,
  IconFileText,
  IconInbox,
  IconPhoto,
  IconServer,
  IconWorld,
} from "@tabler/icons-react";
import Link from "next/link";

function getEmail(submission: SubmissionOutput) {
  return (
    submission.answers?.find((answer) =>
      answer.label.toLowerCase().includes("email"),
    )?.value || "Anonymous"
  );
}

interface Props {
  submission: SubmissionOutput;
  userRole: string | undefined;
}

export function SubmissionCard({ submission, userRole }: Props) {
  async function downloadFile(fileUrl: string, fileName: string) {
    const image = await fetch(fileUrl);
    const imageBlog = await image.blob();
    const imageURL = URL.createObjectURL(imageBlog);
    const link = document.createElement("a");
    link.href = imageURL;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  return (
    <Card key={submission.id} className="overflow-hidden">
      <Collapsible defaultOpen={true}>
        <CollapsibleTrigger
          asChild
          className="w-full cursor-pointer hover:bg-gray-50"
        >
          <div className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="flex h-7 w-7 items-center justify-center rounded-full bg-gray-100">
                    <IconInbox size={16} className="text-gray-500" />
                  </div>
                  <h3 className="text-base font-semibold text-gray-900">
                    {getEmail(submission)}
                  </h3>
                </div>

                <div className="flex flex-wrap items-center gap-x-6 gap-y-2">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <IconClock size={16} className="text-gray-500" />
                    <span className="font-medium">
                      {formatDate(submission.createdAt, "MMM DD, YYYY h:mm A")}
                    </span>
                  </div>
                  {submission.ipAddress && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <ToolTip message="IP Address">
                        <IconServer size={16} className="text-gray-500" />
                      </ToolTip>
                      <span className="font-mono">{submission.ipAddress}</span>
                    </div>
                  )}
                  {submission.countryCode && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <ToolTip message="Country">
                        <IconWorld size={16} className="text-gray-500" />
                      </ToolTip>
                      <span>{getCountryName(submission.countryCode)}</span>
                    </div>
                  )}
                  {submission.browser && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <ToolTip message="Browser">
                        <IconBrowser size={16} className="text-gray-500" />
                      </ToolTip>
                      <span>{submission.browser}</span>
                    </div>
                  )}
                  {submission.os && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <ToolTip message="Operating System">
                        <IconDevices size={16} className="text-gray-500" />
                      </ToolTip>
                      <span>{submission.os}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="ml-4 flex items-start space-x-3">
                {submission.isSpam && (
                  <Badge variant="yellow" className="font-medium">
                    Spam
                  </Badge>
                )}
                <SubmissionCardActionsMenu
                  submission={submission}
                  disabled={userRole === Roles.VIEWER}
                />
              </div>
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <div className="border-t border-gray-200">
            <div className="bg-gray-50/30 p-4">
              <div className="space-y-4">
                {submission.answers && submission.answers.length > 0 && (
                  <div>
                    <h4 className="mb-3 text-sm font-semibold tracking-wide text-gray-700">
                      Form Responses
                    </h4>
                    <div className="space-y-3">
                      {submission.answers.map((answer) => (
                        <div
                          key={answer.id}
                          className="rounded-md border border-gray-200 bg-white p-3"
                        >
                          <h5 className="mb-1 text-sm font-medium text-gray-600">
                            {answer.label}
                          </h5>
                          <p className="text-[15px] leading-relaxed text-gray-900">
                            {answer.value}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {submission.files && submission.files.length > 0 && (
                  <div>
                    <h4 className="mb-3 text-sm font-semibold tracking-wide text-gray-700">
                      Attachments
                    </h4>
                    <div className="space-y-2">
                      {submission.files.map((file) => (
                        <Card
                          key={file.id}
                          className="border border-gray-200 bg-white"
                        >
                          <div className="p-3">
                            <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-100">
                                  {DOCUMENT_MIME_TYPE.includes(
                                    file.type as DocumentMimeType,
                                  ) && (
                                    <IconFileText
                                      size={18}
                                      className="text-gray-600"
                                    />
                                  )}
                                  {IMAGE_MIME_TYPE.includes(
                                    file.type as ImageMimeType,
                                  ) && (
                                    <IconPhoto
                                      size={18}
                                      className="text-gray-600"
                                    />
                                  )}
                                  {SPREADSHEET_MIME_TYPE.includes(
                                    file.type as SpreadsheetMimeType,
                                  ) && (
                                    <IconFileSpreadsheet
                                      size={18}
                                      className="text-gray-600"
                                    />
                                  )}
                                </div>
                                <div className="min-w-0 flex-1 space-y-0.5">
                                  <div className="text-sm font-medium text-gray-500">
                                    {file.formFieldName}
                                  </div>
                                  <Link
                                    href={file.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="block"
                                  >
                                    <p className="truncate text-sm font-semibold text-gray-900">
                                      {file.name}
                                    </p>
                                  </Link>
                                  <p className="text-xs text-gray-500">
                                    {formatFileSize(file.size)}
                                  </p>
                                </div>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                leftIcon={<IconDownload size={14} />}
                                onClick={() =>
                                  downloadFile(file.url, file.name)
                                }
                                className="w-full shrink-0 text-xs sm:w-auto"
                              >
                                Download
                              </Button>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
