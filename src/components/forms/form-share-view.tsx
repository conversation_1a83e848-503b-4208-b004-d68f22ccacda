"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { CodeBlock } from "@/components/ui/code-block";
import { env } from "@/env";
import { useClipboard } from "@/hooks/use-clipboard";
import { useFormQrCode } from "@/queries/form.queries";
import {
  legacyFullPageEmbedCode,
  legacyInlineEmbedCode,
} from "@/utils/code-examples";
import { IconCheck, IconCopy, IconDownload } from "@tabler/icons-react";
import Image from "next/image";
import { useState } from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

interface Props {
  formId: string;
}

export function FormShareView({ formId }: Props) {
  const { copy, copied } = useClipboard();
  const [embedOption, setEmbedOption] = useState("inline");

  const formEndpointUrl = `${env.NEXT_PUBLIC_APP_URL}/forms/${formId}`;
  // const formEmbedUrl = `${env.NEXT_PUBLIC_APP_URL}/embed/${formId}?transparentBackground=1&dynamicHeight=1&isEmbedded=1&hideTitle=1&alignLeft=1`;
  // const scriptUrl = `${env.NEXT_PUBLIC_APP_URL}/scripts/embed.js`;

  const copyFormEndpointUrl = () => {
    copy(formEndpointUrl);
  };

  return (
    <div>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold">Share Link</h3>
            <p className="mt-2 max-w-xl text-gray-600">
              Your form is now ready to be shared. Copy this link to share your
              form on messaging apps, social media, or via email.
            </p>
          </div>
        </div>

        <div className="mt-6">
          <div className="mt-2 flex max-w-[605px] flex-col gap-2 sm:flex-row sm:items-center">
            <Card className="flex h-10 flex-1 items-center py-2 pr-2 pl-3">
              <p className="truncate text-sm sm:text-base">{formEndpointUrl}</p>
            </Card>
            <Button
              className="shrink-0"
              onClick={copyFormEndpointUrl}
              leftIcon={
                copied ? <IconCheck size={16} /> : <IconCopy size={16} />
              }
            >
              Copy link
            </Button>
          </div>
          {/* QR Code Section */}
          <div className="mt-6">
            <h4 className="text-xl font-semibold">Share via QR Code</h4>
            <p className="mt-2 max-w-lg text-gray-600">
              Scan or download this QR code to quickly share your form in print
              or digital media.
            </p>
            <FormQrCodeDisplay formId={formId} />
          </div>
        </div>
      </div>

      <div className="mt-12">
        <div>
          <h3 className="text-xl font-semibold">Embed Form</h3>
          <p className="mt-2 max-w-lg text-gray-600">
            Use these options to embed your form into your own website.
          </p>
        </div>
        <div className="mt-4">
          <Select defaultValue={embedOption} onValueChange={setEmbedOption}>
            <SelectTrigger className="w-[210px]">
              <SelectValue placeholder="Select an embed option" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="inline">Inline embed</SelectItem>
                <SelectItem value="full">Full page</SelectItem>
                <SelectItem value="popup" disabled>
                  Popup (Comming soon)
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        {embedOption === "inline" && (
          <div className="mt-6">
            <div>
              <p className="text-gray-600">
                Paste the below code snippet in your page where you want to show
                it:
              </p>
            </div>
            <div className="mt-4">
              <CodeBlock
                code={legacyInlineEmbedCode(formEndpointUrl)}
                language="html"
                filename="Inline embed"
              />
            </div>
          </div>
        )}
        {embedOption === "full" && (
          <div className="mt-6">
            <div>
              <p className="text-gray-600">
                Paste the below code snippet in a new page:
              </p>
            </div>
            <div className="mt-4">
              <CodeBlock
                code={legacyFullPageEmbedCode(formEndpointUrl)}
                language="html"
                filename="Full page embed"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// QR Code display component
function FormQrCodeDisplay({ formId }: { formId: string }) {
  const { data: qrCode, isLoading, isError } = useFormQrCode(formId);

  if (isLoading) {
    return <div className="mt-4 text-gray-500">Loading QR code...</div>;
  }
  if (isError || !qrCode) {
    return <div className="mt-4 text-red-500">Failed to load QR code.</div>;
  }
  return (
    <div className="mt-4 flex flex-col items-start gap-2">
      <div className="relative h-40 w-40">
        <Image
          src={qrCode}
          alt="Form QR Code"
          width={160}
          height={160}
          className="rounded border bg-white"
          style={{ imageRendering: "crisp-edges" }}
          unoptimized
          priority
        />
      </div>
      <Button
        href={qrCode}
        download={`formbox-form-${formId}-qrcode.png`}
        variant="outline"
        size="sm"
        leftIcon={<IconDownload size={16} />}
        className="mt-1"
      >
        Download QR code
      </Button>
    </div>
  );
}
