"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  ChartContainer,
  ChartLegend,
  ChartTooltip,
  type ChartConfig,
} from "@/components/ui/chart";
import { Skeleton } from "@/components/ui/skeleton";
import { StatCard } from "@/components/ui/stat-card";
import { useOrgById } from "@/queries/org.queries";
import { api } from "@/trpc/react";
import { hasFeatureAccess, type Feature } from "@/utils/has-feature-access";
import {
  IconCalendar,
  IconChartBar,
  IconCheck,
  IconQuestionMark,
  IconTrendingUp,
  IconUsers,
} from "@tabler/icons-react";
import { useMemo } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Pie,
  PieChart,
  XAxis,
  YAxis,
} from "recharts";

interface Props {
  formId: string;
  orgId: string;
}

const COLORS = [
  "#3B82F6",
  "#10B981",
  "#F59E0B",
  "#EF4444",
  "#8B5CF6",
  "#06B6D4",
];

export function FormAnalyticsView({ formId, orgId }: Props) {
  const org = useOrgById(orgId);

  const hasAccess = (feature: Feature) => {
    return hasFeatureAccess(org.data?.stripePlan, feature);
  };

  const { data: overview, isLoading: overviewLoading } =
    api.analytics.getFormOverview.useQuery(
      { formId, orgId },
      {
        enabled: hasAccess("Form analytics"),
      },
    );

  const { data: questionAnalytics, isLoading: questionsLoading } =
    api.analytics.getQuestionAnalytics.useQuery(
      { formId, orgId },
      {
        enabled: hasAccess("Form analytics"),
      },
    );

  const { data: responseTimes, isLoading: timeLoading } =
    api.analytics.getResponseTimes.useQuery(
      { formId, orgId },
      {
        enabled: hasAccess("Form analytics"),
      },
    );

  // Process time data for charts
  const weeklyData = useMemo(() => {
    if (!responseTimes?.weeklyData) return [];

    // ISO week format: Monday=0, Tuesday=1, ..., Sunday=6
    const days = [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ];
    const dayAbbr = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

    const startOfWeek = responseTimes.weekStartDate
      ? new Date(responseTimes.weekStartDate)
      : new Date();

    return days.map((day, index) => {
      const currentDate = new Date(startOfWeek);
      currentDate.setDate(startOfWeek.getDate() + index);

      const dateStr = currentDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });

      return {
        day: dayAbbr[index],
        fullDay: day,
        submissions: responseTimes.weeklyData[index] || 0,
        date: dateStr,
        isToday: currentDate.toDateString() === new Date().toDateString(),
      };
    });
  }, [responseTimes?.weeklyData, responseTimes?.weekStartDate]);

  const weeklyChartConfig: ChartConfig = {
    submissions: {
      label: "Submissions",
      color: "#8B5CF6",
    },
  };

  const isPageLoading =
    overviewLoading || questionsLoading || timeLoading || org.isLoading;

  if (isPageLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (!isPageLoading && !hasAccess("Form analytics")) {
    return (
      <div className="space-y-6">
        {/* Upgrade Banner */}
        <div className="rounded-lg border border-blue-200 bg-linear-to-r from-blue-50 to-indigo-50 p-4 sm:p-6">
          <div className="flex flex-col items-start space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div className="flex items-center space-x-3 sm:space-x-4">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 sm:h-12 sm:w-12">
                <IconChartBar className="h-5 w-5 text-blue-600 sm:h-6 sm:w-6" />
              </div>
              <div>
                <h3 className="text-base font-semibold text-gray-900 sm:text-lg">
                  Form Analytics Available with Pro
                </h3>
                <p className="text-sm text-gray-600">
                  Get detailed insights into your form performance, response
                  patterns, and user demographics.
                </p>
              </div>
            </div>
            <Button
              className="w-full bg-blue-600 hover:bg-blue-700 sm:w-auto"
              href={`/dashboard/${orgId}/settings/subscription`}
            >
              Upgrade Plan
            </Button>
          </div>
        </div>

        {/* Preview of Analytics with Blur/Lock Overlay */}
        <div className="relative">
          {/* Blur overlay */}
          <div className="absolute inset-0 z-10 rounded-lg bg-white/60 backdrop-blur-xs" />

          {/* What's included content */}
          <div className="absolute inset-0 z-20 flex items-center justify-center p-4">
            <Card className="max-h-[80vh] w-full max-w-3xl overflow-y-auto">
              <div className="p-6">
                <div className="mb-6 text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                    <IconChartBar className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="mb-2 text-xl font-semibold text-gray-900">
                    What&apos;s included in Form Analytics
                  </h3>
                  <p className="mb-4 text-sm text-gray-600">
                    Upgrade to Professional or Business plan to unlock detailed
                    form analytics and insights
                  </p>
                </div>

                <div className="mb-6 grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
                  {[
                    "Submission overview and totals",
                    "Response patterns by day of week",
                    "Geographic distribution by country",
                    "Device platform analytics",
                    "Browser usage statistics",
                    "Question-by-question response breakdown",
                    "Choice field visualization with charts",
                    "Response frequency and percentages",
                    "Spam filtering insights",
                    "30-day activity tracking",
                  ].map((feature) => (
                    <div key={feature} className="flex items-center space-x-3">
                      <div className="flex h-5 w-5 shrink-0 items-center justify-center rounded-full bg-blue-100">
                        <IconCheck className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-sm text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <div className="flex flex-col gap-3 sm:flex-row sm:gap-4">
                    <Button
                      className="flex-1"
                      href={`/dashboard/${orgId}/settings/subscription`}
                    >
                      Upgrade to a paid plan
                    </Button>
                    <Button
                      variant="outline"
                      className="flex-1"
                      href={`https://formbox.app/pricing`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      View All Plans
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Preview content (blurred) */}
          <div className="space-y-6">
            {/* Overview Stats */}
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <StatCard
                title="Total Responses"
                value={245}
                icon={IconUsers}
                iconColor="text-blue-600"
                iconBgColor="bg-blue-50"
                description="All time submissions"
              />
              <StatCard
                title="Spam Filtered"
                value={18}
                icon={IconTrendingUp}
                iconColor="text-red-600"
                iconBgColor="bg-red-50"
                description="Blocked submissions"
              />
              <StatCard
                title="Recent Activity"
                value={34}
                icon={IconCalendar}
                iconColor="text-purple-600"
                iconBgColor="bg-purple-50"
                description="Submissions in the last 30 days"
              />
            </div>

            {/* Sample Charts */}
            <Card className="overflow-hidden">
              <div className="border-b border-gray-100 bg-gray-50 px-4 py-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-200">
                      <IconCalendar className="h-3 w-3 text-gray-600" />
                    </div>
                    <h3 className="text-base font-semibold text-gray-900">
                      Responses by Day of Week
                    </h3>
                  </div>
                  <span className="text-xs text-gray-500">245 total</span>
                </div>
              </div>
              <div className="flex h-[250px] items-center justify-center bg-gray-50 p-4">
                <div className="text-center">
                  <IconChartBar className="mx-auto mb-2 h-12 w-12 text-gray-400" />
                  <p className="text-sm text-gray-500">Chart preview</p>
                </div>
              </div>
            </Card>

            {/* Geographic, Device, and Browser Data */}
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
              {[
                "Geographic Distribution",
                "Device Distribution",
                "Browser Usage",
              ].map((title) => (
                <Card key={title} className="overflow-hidden">
                  <div className="border-b border-gray-100 bg-gray-50 px-4 py-3">
                    <div className="flex items-center space-x-2">
                      <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-200">
                        <IconChartBar className="h-3 w-3 text-gray-600" />
                      </div>
                      <h3 className="text-base font-semibold text-gray-900">
                        {title}
                      </h3>
                    </div>
                  </div>
                  <div className="flex h-40 items-center justify-center bg-gray-50 p-4">
                    <div className="text-center">
                      <IconChartBar className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                      <p className="text-xs text-gray-500">Data preview</p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Question Analytics */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2 border-b border-gray-100 pb-3">
                <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-200">
                  <IconQuestionMark className="h-3 w-3 text-gray-600" />
                </div>
                <h2 className="text-lg font-bold text-gray-900">
                  Response Breakdown
                </h2>
              </div>

              <Card className="overflow-hidden">
                <div className="border-b border-gray-100 bg-gray-50 px-4 py-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-200">
                        <span className="text-xs font-semibold text-gray-700">
                          1
                        </span>
                      </div>
                      <h3 className="text-base font-semibold text-gray-900">
                        What&apos;s your favorite color?
                      </h3>
                    </div>
                    <span className="text-xs text-gray-500">128 responses</span>
                  </div>
                </div>
                <div className="flex h-48 items-center justify-center bg-gray-50 p-4">
                  <div className="text-center">
                    <IconChartBar className="mx-auto mb-2 h-10 w-10 text-gray-400" />
                    <p className="text-sm text-gray-500">Response analysis</p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <StatCard
          title="Total Responses"
          value={overview?.overview.totalSubmissions || 0}
          icon={IconUsers}
          iconColor="text-blue-600"
          iconBgColor="bg-blue-50"
          description="All time submissions"
        />

        <StatCard
          title="Spam Filtered"
          value={overview?.overview.spamSubmissions || 0}
          icon={IconTrendingUp}
          iconColor="text-red-600"
          iconBgColor="bg-red-50"
          description="Blocked submissions"
        />

        <StatCard
          title="Recent Activity"
          value={overview?.overview.submissionsLast30Days || 0}
          icon={IconCalendar}
          iconColor="text-purple-600"
          iconBgColor="bg-purple-50"
          description="Submissions in the last 30 days"
        />
      </div>

      {/* Response Patterns */}
      <Card className="overflow-hidden">
        <div className="border-b border-gray-100 bg-gray-50 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-200">
                <IconCalendar className="h-3 w-3 text-gray-600" />
              </div>
              <h3 className="text-base font-semibold text-gray-900">
                Responses this week
              </h3>
            </div>
            {weeklyData.length > 0 && (
              <span className="text-xs text-gray-500">
                {weeklyData.reduce((sum, item) => sum + item.submissions, 0)}{" "}
                total
              </span>
            )}
          </div>
        </div>
        <div className="p-4">
          {weeklyData.length > 0 ? (
            <ChartContainer
              className="h-[250px] w-full"
              config={weeklyChartConfig}
            >
              <BarChart
                data={weeklyData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="day"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: "#6B7280", fontSize: 12 }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: "#6B7280", fontSize: 12 }}
                />
                <ChartTooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className="rounded-lg border bg-background p-3 shadow-lg">
                          <div className="space-y-2">
                            <div className="font-medium text-gray-900">
                              {data.fullDay}
                            </div>
                            <div className="text-sm text-gray-600">
                              {payload[0].value} submissions
                            </div>
                            <div className="border-t border-gray-100 pt-2">
                              <div className="text-xs text-gray-600">
                                {data.date}
                                {data.isToday && (
                                  <span className="ml-1 text-blue-600">
                                    (Today)
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Bar
                  dataKey="submissions"
                  fill="#8B5CF6"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ChartContainer>
          ) : (
            <div className="flex h-[250px] items-center justify-center">
              <div className="text-center">
                <div className="mb-2 flex justify-center">
                  <IconCalendar className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-sm font-medium text-gray-900">
                  No data available
                </h3>
                <p className="text-sm text-gray-500">
                  Weekly submissions will appear here once you have responses.
                </p>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Geographic, Device, and Browser Data in Grid */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        {/* Geographic Data */}
        <Card className="overflow-hidden">
          <div className="border-b border-gray-100 bg-gray-50 px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-200">
                  <svg
                    className="h-3 w-3 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-base font-semibold text-gray-900">
                  Geographic Distribution
                </h3>
              </div>
              {overview?.geoData && overview.geoData.length > 0 && (
                <span className="text-xs text-gray-500">
                  {overview.geoData.reduce((sum, item) => sum + item.count, 0)}{" "}
                  total
                </span>
              )}
            </div>
          </div>
          <div className="p-4">
            {overview?.geoData && overview.geoData.length > 0 ? (
              <div className="space-y-2">
                {overview.geoData.slice(0, 6).map((item, index) => {
                  const total = overview.geoData.reduce(
                    (sum, geo) => sum + geo.count,
                    0,
                  );
                  const percentage = Math.round((item.count / total) * 100);

                  return (
                    <div
                      key={item.country}
                      className="group flex items-center justify-between rounded-lg border border-gray-100 bg-gray-50 p-2 transition-all hover:border-gray-200 hover:bg-gray-100"
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{
                            backgroundColor: COLORS[index % COLORS.length],
                          }}
                        />
                        <span className="font-medium text-gray-900">
                          {item.country}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="text-sm text-gray-500">
                          {percentage}%
                        </span>
                        <span className="font-semibold text-gray-900">
                          {item.count}
                        </span>
                      </div>
                    </div>
                  );
                })}

                {overview.geoData.length > 6 && (
                  <div className="border-t border-gray-100 pt-2">
                    <p className="text-center text-xs text-gray-500">
                      +{overview.geoData.length - 6} more countries
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex h-40 items-center justify-center">
                <div className="text-center">
                  <div className="mb-4 flex justify-center">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                      <svg
                        className="h-6 w-6 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                  </div>
                  <h4 className="mb-2 text-sm font-semibold text-gray-900">
                    No geographic data
                  </h4>
                  <p className="text-sm text-gray-500">
                    Location data will appear once you have submissions
                  </p>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Device Data */}
        <Card className="overflow-hidden">
          <div className="border-b border-gray-100 bg-gray-50 px-4 py-3">
            <div className="flex items-center space-x-2">
              <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-200">
                <svg
                  className="h-3 w-3 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="text-base font-semibold text-gray-900">
                Device Distribution
              </h3>
            </div>
          </div>
          <div className="p-4">
            {overview?.deviceData && overview.deviceData.length > 0 ? (
              <div className="space-y-2">
                {overview.deviceData.map((item, index) => {
                  const total = overview.deviceData.reduce(
                    (sum, device) => sum + device.count,
                    0,
                  );
                  const percentage = Math.round((item.count / total) * 100);

                  return (
                    <div
                      key={item.platform}
                      className="group flex items-center justify-between rounded-lg border border-gray-100 bg-gray-50 p-2 transition-all hover:border-gray-200 hover:bg-gray-100"
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{
                            backgroundColor: COLORS[index % COLORS.length],
                          }}
                        />
                        <span className="font-medium text-gray-900">
                          {item.platform}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="text-sm text-gray-500">
                          {percentage}%
                        </span>
                        <span className="font-semibold text-gray-900">
                          {item.count}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="flex h-40 items-center justify-center">
                <div className="text-center">
                  <div className="mb-4 flex justify-center">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                      <svg
                        className="h-6 w-6 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                  </div>
                  <h4 className="mb-2 text-sm font-semibold text-gray-900">
                    No device data
                  </h4>
                  <p className="text-sm text-gray-500">
                    Device information will appear once you have submissions
                  </p>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Browser Data */}
        <Card className="overflow-hidden">
          <div className="border-b border-gray-100 bg-gray-50 px-4 py-3">
            <div className="flex items-center space-x-2">
              <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-200">
                <svg
                  className="h-3 w-3 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"
                  />
                </svg>
              </div>
              <h3 className="text-base font-semibold text-gray-900">
                Browser Usage
              </h3>
            </div>
          </div>
          <div className="p-4">
            {overview?.browserData && overview.browserData.length > 0 ? (
              <div className="space-y-2">
                {overview.browserData.slice(0, 6).map((item, index) => {
                  const total = overview.browserData.reduce(
                    (sum, browser) => sum + browser.count,
                    0,
                  );
                  const percentage = Math.round((item.count / total) * 100);

                  return (
                    <div
                      key={item.browser}
                      className="group flex items-center justify-between rounded-lg border border-gray-100 bg-gray-50 p-2 transition-all hover:border-gray-200 hover:bg-gray-100"
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{
                            backgroundColor: COLORS[index % COLORS.length],
                          }}
                        />
                        <span className="font-medium text-gray-900">
                          {item.browser}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="text-sm text-gray-500">
                          {percentage}%
                        </span>
                        <span className="font-semibold text-gray-900">
                          {item.count}
                        </span>
                      </div>
                    </div>
                  );
                })}

                {overview.browserData.length > 6 && (
                  <div className="border-t border-gray-100 pt-2">
                    <p className="text-center text-xs text-gray-500">
                      +{overview.browserData.length - 6} more browsers
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex h-40 items-center justify-center">
                <div className="text-center">
                  <div className="mb-4 flex justify-center">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                      <svg
                        className="h-6 w-6 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"
                        />
                      </svg>
                    </div>
                  </div>
                  <h4 className="mb-2 text-sm font-semibold text-gray-900">
                    No browser data
                  </h4>
                  <p className="text-sm text-gray-500">
                    Browser usage will appear once you have submissions
                  </p>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Question Analytics */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2 border-b border-gray-100 pb-3">
          <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-200">
            <IconQuestionMark className="h-3 w-3 text-gray-600" />
          </div>
          <h2 className="text-lg font-bold text-gray-900">
            Response Breakdown
          </h2>
        </div>

        {questionAnalytics && questionAnalytics.length > 0 ? (
          questionAnalytics.map((question, questionIndex) => {
            // Check if this is a choice field that should show a pie chart
            const isChoiceField = question.isChoiceField;
            const hasShortResponses = question.responses.every(
              (response) => response.value.length <= 50,
            );
            const hasReasonableResponseCount = question.responses.length <= 8;

            const shouldUsePieChart =
              isChoiceField && hasShortResponses && hasReasonableResponseCount;

            const shouldUseBarChart =
              !isChoiceField &&
              question.responses.length <= 6 &&
              hasShortResponses;

            return (
              <Card key={question.questionLabel} className="overflow-hidden">
                <div className="border-b border-gray-100 bg-gray-50 px-4 py-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-200">
                        <span className="text-xs font-semibold text-gray-700">
                          {questionIndex + 1}
                        </span>
                      </div>
                      <h3 className="text-base font-semibold text-gray-900">
                        {question.questionLabel}
                      </h3>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500">
                        {question.totalResponses} responses
                      </span>
                      {isChoiceField && (
                        <span className="inline-flex items-center rounded-full bg-indigo-100 px-2 py-0.5 text-xs font-medium text-indigo-800">
                          {question.fieldType === "single_choice" &&
                            "Single Choice"}
                          {question.fieldType === "multiple_choice" &&
                            "Multiple Choice"}
                          {question.fieldType === "dropdown" && "Dropdown"}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="p-4">
                  {shouldUsePieChart ? (
                    // Show pie chart for choice fields
                    <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                      <div className="space-y-1.5">
                        {question.responses.map((response, index) => {
                          const percentage = Math.round(
                            (response.count / question.totalResponses) * 100,
                          );

                          return (
                            <div
                              key={index}
                              className="flex items-center justify-between rounded-lg border border-gray-100 bg-gray-50 p-2 transition-all hover:border-gray-200 hover:bg-gray-100"
                            >
                              <div className="flex items-center space-x-2">
                                <div
                                  className="h-2.5 w-2.5 rounded-full"
                                  style={{
                                    backgroundColor:
                                      COLORS[index % COLORS.length],
                                  }}
                                />
                                <span className="truncate text-sm font-medium text-gray-900">
                                  {response.value}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2 text-right">
                                <span className="text-xs text-gray-500">
                                  {percentage}%
                                </span>
                                <span className="text-sm font-semibold text-gray-900">
                                  {response.count}
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>

                      <div className="flex items-center justify-center">
                        <ChartContainer
                          className="h-[220px] w-full"
                          config={question.responses.reduce(
                            (config, item, index) => {
                              config[item.value] = {
                                label: item.value,
                                color: COLORS[index % COLORS.length],
                              };
                              return config;
                            },
                            {} as ChartConfig,
                          )}
                        >
                          <PieChart>
                            <Pie
                              data={question.responses}
                              cx="50%"
                              cy="50%"
                              outerRadius={80}
                              innerRadius={30}
                              dataKey="count"
                              nameKey="value"
                              paddingAngle={2}
                            >
                              {question.responses.map((_, index) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={COLORS[index % COLORS.length]}
                                  stroke="white"
                                  strokeWidth={2}
                                />
                              ))}
                            </Pie>
                            <ChartTooltip
                              content={({ active, payload }) => {
                                if (active && payload && payload.length) {
                                  const data = payload[0].payload;
                                  const percentage = Math.round(
                                    (data.count / question.totalResponses) *
                                      100,
                                  );
                                  return (
                                    <div className="rounded-lg border bg-background p-2 shadow-xs">
                                      <div className="grid grid-cols-2 gap-2">
                                        <span className="font-medium">
                                          {data.value}:
                                        </span>
                                        <span className="font-medium">
                                          {data.count} ({percentage}%)
                                        </span>
                                      </div>
                                    </div>
                                  );
                                }
                                return null;
                              }}
                            />
                            <ChartLegend />
                          </PieChart>
                        </ChartContainer>
                      </div>
                    </div>
                  ) : shouldUseBarChart ? (
                    // Show bar chart for short text responses
                    <div className="px-1">
                      <ChartContainer
                        className="h-[250px] w-full"
                        config={{
                          count: {
                            label: "Responses",
                            color: COLORS[questionIndex % COLORS.length],
                          },
                        }}
                      >
                        <BarChart data={question.responses} barGap={8}>
                          <CartesianGrid
                            strokeDasharray="3 3"
                            vertical={false}
                          />
                          <XAxis
                            dataKey="value"
                            axisLine={false}
                            tickLine={false}
                            tick={{ fill: "#6B7280", fontSize: 12 }}
                          />
                          <YAxis
                            axisLine={false}
                            tickLine={false}
                            tick={{ fill: "#6B7280", fontSize: 12 }}
                          />
                          <ChartTooltip
                            content={({ active, payload, label }) => {
                              if (active && payload && payload.length) {
                                return (
                                  <div className="rounded-lg border bg-background p-2 shadow-xs">
                                    <div className="grid grid-cols-2 gap-2">
                                      <span className="font-medium">
                                        Response:
                                      </span>
                                      <span>{label}</span>
                                      <span className="font-medium">
                                        Count:
                                      </span>
                                      <span>{payload[0].value}</span>
                                    </div>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                          <Bar
                            dataKey="count"
                            fill={COLORS[questionIndex % COLORS.length]}
                            radius={[4, 4, 0, 0]}
                          />
                        </BarChart>
                      </ChartContainer>
                    </div>
                  ) : (
                    // Show list for long text or many responses
                    <div className="space-y-1.5">
                      {question.responses
                        .slice(0, 10)
                        .map((response, index) => {
                          const total = question.totalResponses;
                          const percentage = Math.round(
                            (response.count / total) * 100,
                          );
                          const isLongText = response.value.length > 50;

                          return (
                            <div
                              key={index}
                              className="group flex items-start justify-between rounded-lg border border-gray-100 bg-gray-50 p-2.5 transition-all hover:border-gray-200 hover:bg-gray-100"
                            >
                              <div className="flex-1">
                                <div className="flex items-center space-x-2">
                                  <span
                                    className={`text-[15px] font-medium text-gray-900 ${
                                      isLongText ? "line-clamp-2" : "truncate"
                                    }`}
                                  >
                                    {isLongText && response.value.length > 100
                                      ? `${response.value.substring(0, 100)}...`
                                      : response.value}
                                  </span>
                                </div>
                                <div className="mt-1 flex items-center space-x-2">
                                  <span className="text-xs text-gray-500">
                                    {percentage}% of responses
                                  </span>
                                  {isLongText && (
                                    <span className="text-xs text-gray-400">
                                      ({response.value.length} characters)
                                    </span>
                                  )}
                                </div>
                              </div>
                              <span className="ml-3 font-semibold text-gray-900">
                                {response.count}
                              </span>
                            </div>
                          );
                        })}
                      {question.responses.length > 10 && (
                        <div className="border-t border-gray-200 pt-1.5">
                          <p className="text-center text-xs text-gray-500">
                            +{question.responses.length - 10} more responses
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </Card>
            );
          })
        ) : (
          <Card className="overflow-hidden">
            <div className="p-4">
              <div className="flex h-40 items-center justify-center">
                <div className="text-center">
                  <div className="mb-4 flex justify-center">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                      <IconQuestionMark className="h-6 w-6 text-gray-400" />
                    </div>
                  </div>
                  <h4 className="mb-2 text-sm font-semibold text-gray-900">
                    No response data
                  </h4>
                  <p className="text-sm text-gray-500">
                    Response analytics will appear once you have form
                    submissions
                  </p>
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
