import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { type FormByIdOutput, type FormUpdateData } from "@/types/form.types";
import { formUpdateSchema } from "@/utils/schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { IconEye, IconEyeOff } from "@tabler/icons-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { type z } from "zod";

const schema = formUpdateSchema.pick({
  smtpHost: true,
  smtpPort: true,
  smtpUsername: true,
  smtpPassword: true,
  smtpSenderEmail: true,
});

interface Props extends DialogProps {
  form: FormByIdOutput;
  onClose: () => void;
  submit: (data: FormUpdateData) => Promise<void>;
}

export function FormSmtpDialog({ open, onClose, form, submit }: Props) {
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      smtpHost: form?.smtpHost,
      smtpPort: form?.smtpPort,
      smtpUsername: form?.smtpUsername,
      smtpPassword: form?.smtpPassword,
      smtpSenderEmail: form?.smtpSenderEmail,
    },
  });

  useEffect(() => {
    reset({
      smtpHost: form?.smtpHost,
      smtpPort: form?.smtpPort,
      smtpUsername: form?.smtpUsername,
      smtpPassword: form?.smtpPassword,
      smtpSenderEmail: form?.smtpSenderEmail,
    });
  }, [form, reset]);

  function handleShowPassword() {
    setShowPassword(!showPassword);
  }

  function closeModal() {
    reset();
    onClose();
  }

  async function onSubmit(data: z.infer<typeof schema>) {
    await submit({ ...(data as FormUpdateData) });
    closeModal();
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent
        className="sm:max-w-[500px]"
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>SMTP</DialogTitle>
        </DialogHeader>

        <DialogDescription>
          Set up your SMTP server to send emails from your own custom domain.
        </DialogDescription>

        <form onSubmit={handleSubmit(onSubmit)} className="mt-2">
          <div className="space-y-3">
            <Input
              label="Host"
              defaultValue={form?.smtpHost || ""}
              placeholder="smtp.example.com"
              {...register("smtpHost")}
              error={errors.smtpHost !== undefined}
              errorMessage={errors?.smtpHost?.message}
            />
            <Input
              label="Port"
              defaultValue={form?.smtpPort || ""}
              placeholder="587"
              {...register("smtpPort", { valueAsNumber: true })}
              type="number"
              error={errors.smtpPort !== undefined}
              errorMessage={errors?.smtpPort?.message}
            />
            <Input
              label="User"
              defaultValue={form?.smtpUsername || ""}
              {...register("smtpUsername")}
              placeholder="<EMAIL>"
              error={errors.smtpUsername !== undefined}
              errorMessage={errors?.smtpUsername?.message}
            />
            <Input
              label="Password"
              defaultValue={form?.smtpPassword || ""}
              {...register("smtpPassword")}
              placeholder="Your smtp password"
              type={showPassword ? "text" : "password"}
              rightAdornment={
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7"
                  onClick={handleShowPassword}
                >
                  {showPassword ? (
                    <IconEye size={16} />
                  ) : (
                    <IconEyeOff size={16} />
                  )}
                </Button>
              }
              allowAutoComplete={false}
              error={errors.smtpPassword !== undefined}
              errorMessage={errors?.smtpPassword?.message}
            />
            <Input
              label="Sender email"
              defaultValue={form?.smtpSenderEmail || ""}
              {...register("smtpSenderEmail")}
              placeholder="<EMAIL>"
              error={errors.smtpSenderEmail !== undefined}
              errorMessage={errors?.smtpSenderEmail?.message}
            />
          </div>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={closeModal}>
              Close
            </Button>
            <Button type="submit" loading={isSubmitting}>
              Save changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
