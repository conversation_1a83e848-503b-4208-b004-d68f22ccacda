"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { env } from "@/env";
import { useFormById, useFormUpdateMutation } from "@/queries/form.queries";
import { useFileDeleteMutation } from "@/queries/storage.queries";
import { useFormStore } from "@/stores/form.store";
import type {
  FormBorderStyle,
  FormByIdOutput,
  FormPageMode,
} from "@/types/form.types";
import { cn } from "@/utils/tailwind-helpers";
import {
  IconArrowLeft,
  IconCircleCheck,
  IconDeviceFloppy,
  IconExternalLink,
  IconHammer,
  IconPalette,
} from "@tabler/icons-react";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { isEmpty, isEqual, pick } from "radash";
import { useEffect, useState } from "react";

interface Props {
  formId: string;
  orgId: string;
  formName?: string;
}

export function FormBuilderNavbar({ formId, orgId, formName }: Props) {
  // Add this at the top level of your component to track renders
  console.log("FormBuilderNavbar rendering");

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { form, setForm } = useFormStore();

  const { data: formData } = useFormById(
    { id: formId, orgId },
    {
      refetchOnWindowFocus: false,
    },
  );

  const deleteFileMutation = useFileDeleteMutation({
    errorMessaege: "An error occured while trying to update this form.",
  });

  const handleUpdateMutation = useFormUpdateMutation(orgId, {
    showToast: true,
    toastMessage: "Form updated",
  });

  const deleteImage = async (image: string | null | undefined) => {
    if (!image) return;

    if (!image.startsWith(env.NEXT_PUBLIC_AWS_PUBLIC_BUCKET_URL)) return;

    const fileKey = image.replace(
      `${env.NEXT_PUBLIC_AWS_PUBLIC_BUCKET_URL}/`,
      "",
    );

    try {
      return await deleteFileMutation.mutateAsync({ fileKey });
    } catch (error) {
      console.log(error);
    }
  };

  const [saveStatus, setSaveStatus] = useState<
    "idle" | "saving" | "saved" | "error"
  >("idle");
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  const handleSave = async () => {
    if (!form) return;

    // Don't attempt to save if there are no changes
    if (isEqual(formData, form)) return;

    setSaveStatus("saving");

    try {
      if (isEmpty(form?.headerImage)) {
        await deleteImage(formData?.headerImage);
      }
      if (isEmpty(form?.logo)) {
        await deleteImage(formData?.logo);
      }

      const updateData = pick(form, [
        "name",
        "fields",
        "submitButtonText",
        "headerTitle",
        "headerDescription",
        "pageMode",
        "inputBorderStyle",
        "buttonBorderStyle",
        "buttonBackgroundColor",
        "backgroundColor",
        "accentColor",
        "buttonTextColor",
        "textColor",
        "headerImage",
        "logo",
        "tpBackgroundColor",
        "tpButtonBackgroundColor",
        "tpTextColor",
        "tpButtonColor",
        "tpHeader",
        "tpMessage",
        "tpButtonText",
        "tpButtonUrl",
      ]);

      await handleUpdateMutation.mutateAsync({
        id: formId,
        ...updateData,
        pageMode: form.pageMode as FormPageMode,
        buttonBorderStyle: form.buttonBorderStyle as FormBorderStyle,
        inputBorderStyle: form.inputBorderStyle as FormBorderStyle,
      });

      setSaveStatus("saved");
      setLastSaved(new Date());

      // Only reset form if there was an error
    } catch (error) {
      console.error(error);
      setSaveStatus("error");
      // Only update form state if there was an error
      setForm(formData as FormByIdOutput);
    }
  };

  // Add this to track form changes
  useEffect(() => {
    console.log("Form changed");
  }, [form]);

  const currentMode = searchParams.get("mode") || "builder";

  const updateMode = (mode: "builder" | "preview" | "success") => {
    const newSearchParams = new URLSearchParams(searchParams.toString());
    newSearchParams.set("mode", mode);
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  return (
    <div className="fixed top-0 z-50 w-full border-b border-gray-200 bg-white">
      <div className="mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Left section - Back button and form name */}
          <div className="flex items-center space-x-4">
            <Link href={`/dashboard/${orgId}/forms/${formId}`}>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <IconArrowLeft size={16} />
              </Button>
            </Link>
            {formName && (
              <div className="flex items-center space-x-2">
                <h1 className="text-lg font-semibold text-gray-900">
                  {formName}
                </h1>
                <div className="hidden space-x-2 sm:inline-block">
                  {!formData?.isClosed && <Badge variant="green">Active</Badge>}
                  {formData?.isClosed && <Badge variant="red">Closed</Badge>}
                </div>
              </div>
            )}
          </div>

          {/* Center section - Mode navigation */}
          <div className="flex items-center rounded-lg bg-gray-100 p-1">
            <button
              onClick={() => updateMode("builder")}
              className={cn(
                "flex cursor-pointer items-center space-x-2 rounded-md px-4 py-2 text-sm font-medium transition-colors",
                currentMode === "builder"
                  ? "bg-white text-gray-900 shadow-xs"
                  : "text-gray-600 hover:text-gray-900",
              )}
            >
              <IconHammer size={16} />
              <span>Build</span>
            </button>
            <button
              onClick={() => updateMode("preview")}
              className={cn(
                "flex cursor-pointer items-center space-x-2 rounded-md px-4 py-2 text-sm font-medium transition-colors",
                currentMode === "preview"
                  ? "bg-white text-gray-900 shadow-xs"
                  : "text-gray-600 hover:text-gray-900",
              )}
            >
              <IconPalette size={16} />
              <span>Customize</span>
            </button>
            <button
              onClick={() => updateMode("success")}
              className={cn(
                "flex cursor-pointer items-center space-x-2 rounded-md px-4 py-2 text-sm font-medium transition-colors",
                currentMode === "success"
                  ? "bg-white text-gray-900 shadow-xs"
                  : "text-gray-600 hover:text-gray-900",
              )}
            >
              <IconCircleCheck size={16} />
              <span>Success Page</span>
            </button>
          </div>

          {/* Right section - Save button with status indicator */}
          <div className="flex items-center space-x-2">
            {/* {saveStatus === "saving" && (
              <span className="text-sm text-amber-600">Saving...</span>
            )}
            {saveStatus === "saved" && (
              <span className="text-sm text-green-600">
                Saved {lastSaved && `at ${lastSaved.toLocaleTimeString()}`}
              </span>
            )}
            {saveStatus === "error" && (
              <span className="text-sm text-red-600">Save failed</span>
            )} */}
            <Button
              onClick={handleSave}
              disabled={isEqual(formData, form) || saveStatus === "saving"}
              loading={handleUpdateMutation.isPending}
              leftIcon={<IconDeviceFloppy size={16} />}
              className="disabled:bg-gray-200 disabled:text-gray-500"
            >
              Save
            </Button>
            <Button
              variant="outline"
              leftIcon={<IconExternalLink size={16} />}
              href={`/forms/${formId}`}
              rel="noreferrer noopener"
              target="_blank"
            >
              Open form
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
