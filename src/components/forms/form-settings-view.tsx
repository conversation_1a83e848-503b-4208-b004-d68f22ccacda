"use client";

import { FormDeleteDialog } from "@/components/forms/form-delete-dialog";
import { FormRespondantEmailTemplateDialog } from "@/components/forms/form-respondant-email-template-dialog";
import { FormSmtpDialog } from "@/components/forms/form-smtp-dialog";
import { Autocomplete } from "@/components/ui/autocomplete";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ColorPicker } from "@/components/ui/color-picker";
import { DatePicker } from "@/components/ui/date-picker";
import { Divider } from "@/components/ui/divider";
import { Input } from "@/components/ui/input";
import { MaxWidthWrapper } from "@/components/ui/max-width-wrapper";
import { Paper } from "@/components/ui/paper";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useDialog } from "@/hooks/use-dialog";
import {
  useFormById,
  useFormDeleteMutation,
  useFormUpdateMutation,
} from "@/queries/form.queries";
import { useOrgById, useOrgMemberRole } from "@/queries/org.queries";
import { useAuthUser } from "@/queries/user.queries";
import { type FormByIdOutput, type FormUpdateData } from "@/types/form.types";
import { countries, timezones } from "@/utils/constants";
import { hasFeatureAccess, type Feature } from "@/utils/has-feature-access";
import { IconDeviceFloppy, IconTrash } from "@tabler/icons-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { isEmpty, isEqual, pick } from "radash";
import { useEffect, useState } from "react";

function getEmailsToNotify(emails: string[]) {
  return emails.map((email) => email.trim()).join(", ");
}

interface Props {
  orgId: string;
  formId: string;
}

export function FormSettingsView({ orgId, formId }: Props) {
  const router = useRouter();
  const [deleteModal, deleteModalHandler] = useDialog();
  const [respondantEmailModal, respondantEmailModalHandler] = useDialog();
  const [smtpModal, smtpModalHandler] = useDialog();
  const { data: formData } = useFormById(
    { id: formId, orgId },
    {
      refetchOnWindowFocus: false,
    },
  );

  const countryOptions = Object.keys(countries).map((key) => ({
    label: key,
    value: countries[key as keyof typeof countries],
  }));

  // Timezone options showing IANA names as labels
  const timezoneOptions = Object.entries(timezones).map(([label, tz]) => ({
    label: `${label} - (${tz})`,
    value: tz,
  }));

  const [form, setForm] = useState<FormByIdOutput | null | undefined>(formData);

  useEffect(() => {
    setForm(formData);
  }, [formData]);

  const defaultCountries = () => {
    if (isEmpty(form?.allowedCountries)) return [];
    return form?.allowedCountries?.split(",").map((code) => ({
      label:
        Object.keys(countries).find(
          (key) => countries[key as keyof typeof countries] === code,
        ) ?? "",
      value: code,
    }));
  };

  const defaultCountriesOptions = defaultCountries();

  const org = useOrgById(orgId);
  const user = useAuthUser();
  const { data: userRole } = useOrgMemberRole(user?.id as string, orgId);

  const updateMutation = useFormUpdateMutation(orgId);
  const deleteMutation = useFormDeleteMutation(orgId);

  const handleDeleteForm = async () => {
    try {
      await deleteMutation.mutateAsync({ id: formId });
      router.push(`/dashboard/${orgId}/forms`);
    } catch (error) {
      console.log(error);
    }
  };

  const handleSettingsSave = async (data?: FormUpdateData) => {
    try {
      if (!form) return;
      const updateData = pick(form, [
        "name",
        "isClosed",
        "autoCloseEnabled",
        "autoCloseDate",
        "autoCloseTime",
        "autoCloseTimezone",
        "sendEmailNotifications",
        "sendRespondantEmailNotifications",
        "emailsToNotify",
        "limitResponses",
        "maxResponses",
        "submissionStorageDuration",
        "webhookEnabled",
        "removeFormboxBranding",
        "respondantEmailFromName",
        "respondantEmailSubject",
        "respondantEmailMessageHTML",
        "useCustomRedirect",
        "customSuccessUrl",
        "googleRecaptchaEnabled",
        "googleRecaptchaSecretKey",
        "allowedDomains",
        "allowedCountries",
        "ipBlacklist",
        "customHoneypot",
        "showCustomClosedMessage",
        "closeMessageTitle",
        "closeMessageDescription",
        "useCustomThankYouPage",
        "tpBackgroundColor",
        "tpButtonBackgroundColor",
        "tpTextColor",
        "tpButtonColor",
        "tpHeader",
        "tpMessage",
        "tpButtonText",
        "tpButtonUrl",
        "smtpEnabled",
        "smtpHost",
        "smtpPort",
        "smtpUsername",
        "smtpPassword",
        "smtpSenderEmail",
      ]);

      // Combine date and time if both are present
      let combinedDateTime = null;
      if (form.autoCloseEnabled && form.autoCloseDate) {
        combinedDateTime = form.autoCloseDate;

        // Always include time - use default if not specified (23:59)
        const timeToUse = form.autoCloseTime || "23:59";
        const [hoursRaw, minutesRaw] = timeToUse.split(":");
        const hours = Number(hoursRaw) || 0;
        const minutes = Number(minutesRaw) || 0;
        const dateObj = new Date(form.autoCloseDate);
        dateObj.setHours(hours, minutes, 0, 0);
        combinedDateTime = dateObj;
      }

      if (!form?.smtpEnabled) {
        updateData.smtpEnabled = false;
        updateData.smtpHost = null;
        updateData.smtpPort = null;
        updateData.smtpUsername = null;
        updateData.smtpPassword = null;
        updateData.smtpSenderEmail = null;
      }

      await updateMutation.mutateAsync({
        id: formId,
        ...updateData,
        ...data,
        autoCloseDate: form.autoCloseEnabled ? combinedDateTime : null,
        autoCloseTimezone: form.autoCloseEnabled
          ? form.autoCloseTimezone
          : null,
        autoCloseTime: form.autoCloseEnabled
          ? form.autoCloseTime || "23:59"
          : null,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleNameChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          name: inputValue,
        },
    );
  };

  const handleRemoveFormboxBrandingChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          removeFormboxBranding: !prevForm.removeFormboxBranding,
        },
    );
  };

  const handleEmailNotificationChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          sendEmailNotifications: !prevForm.sendEmailNotifications,
        },
    );
  };

  const handleCloseFormChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          isClosed: !prevForm.isClosed,
        },
    );
  };

  const handleRespondantEmailNotificationChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          sendRespondantEmailNotifications:
            !prevForm.sendRespondantEmailNotifications,
        },
    );
  };

  const handleSmtpEnabledChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          smtpEnabled: !prevForm.smtpEnabled,
        },
    );
  };

  const handleLimitResponsesChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          limitResponses: !prevForm.limitResponses,
        },
    );
  };

  const handleMaxResponsesChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          maxResponses: Number(inputValue),
        },
    );
  };

  const handleCloseMessageChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          showCustomClosedMessage: !prevForm.showCustomClosedMessage,
        },
    );
  };

  const handleCloseMessageTitleChange = (
    e: React.FormEvent<HTMLInputElement>,
  ) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          closeMessageTitle: inputValue,
        },
    );
  };

  const handleCloseMessageDescriptionChange = (
    e: React.FormEvent<HTMLTextAreaElement>,
  ) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          closeMessageDescription: inputValue,
        },
    );
  };

  const handleUseCustomRedirectChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          useCustomRedirect: !prevForm.useCustomRedirect,
        },
    );
  };

  const handleSuccessUrlChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          customSuccessUrl: inputValue,
        },
    );
  };

  const handleUseCustomThankYouPageChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          useCustomThankYouPage: !prevForm.useCustomThankYouPage,
        },
    );
  };

  const handleIpBlacklistChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          ipBlacklist: inputValue,
        },
    );
  };

  const handleAllowedDomainsChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          allowedDomains: inputValue,
        },
    );
  };

  const handleAllowedCountriesChange = (newValue: unknown) => {
    const newValues = newValue as { value: string; label: string }[];
    const inputValue = newValues.map((item) => item.value).join(",");
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          allowedCountries: inputValue,
        },
    );
  };

  const handleGoogleRecaptchaChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          googleRecaptchaEnabled: !prevForm.googleRecaptchaEnabled,
        },
    );
  };

  const handleGoogleRecaptchaKeyChange = (
    e: React.FormEvent<HTMLInputElement>,
  ) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          googleRecaptchaSecretKey: inputValue,
        },
    );
  };

  const handleCustomHoneypotChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          customHoneypot: inputValue,
        },
    );
  };

  const handleEmailsToNotifyChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          emailsToNotify: inputValue.split(",").map((email) => email.trim()),
        },
    );
  };
  const handleStorageDurationChange = (value: string) => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          submissionStorageDuration: value,
        },
    );
  };

  const handleTPHeaderChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          tpHeader: inputValue,
        },
    );
  };

  const handleTPButtonTextChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          tpButtonText: inputValue,
        },
    );
  };

  const handleTPMessageChange = (e: React.FormEvent<HTMLTextAreaElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          tpMessage: inputValue,
        },
    );
  };

  const handleTPButtonUrlChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          tpButtonUrl: inputValue,
        },
    );
  };

  const handleTPButtonBackgroundColorChange = (
    e: React.FormEvent<HTMLInputElement>,
  ) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          tpButtonBackgroundColor: inputValue,
        },
    );
  };

  const handleTPButtonTextColorChange = (
    e: React.FormEvent<HTMLInputElement>,
  ) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          tpButtonColor: inputValue,
        },
    );
  };

  const handleTPTextColorChange = (e: React.FormEvent<HTMLInputElement>) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          tpTextColor: inputValue,
        },
    );
  };

  const handleTPBackgroundColorChange = (
    e: React.FormEvent<HTMLInputElement>,
  ) => {
    const inputValue = e.currentTarget.value;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          tpBackgroundColor: inputValue,
        },
    );
  };

  const handleAutoCloseEnabledChange = () => {
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          autoCloseEnabled: !prevForm.autoCloseEnabled,
        },
    );
  };

  const handleAutoCloseDateChange = (date: Date | undefined) => {
    if (!date) return;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          autoCloseDate: date,
          // If no time is set yet, default to end of day (23:59)
          autoCloseTime: prevForm.autoCloseTime || "23:59",
        },
    );
  };

  const handleAutoCloseTimeChange = (time: string) => {
    if (!time) return;

    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          autoCloseTime: time,
        },
    );
  };

  const handleAutoCloseTimezoneChange = (newValue: unknown) => {
    const selected = newValue as { value: string; label: string } | null;
    setForm(
      (prevForm) =>
        prevForm && {
          ...prevForm,
          autoCloseTimezone: selected?.value || "",
        },
    );
  };

  const hasAccess = (feature: Feature) => {
    return hasFeatureAccess(org.data?.stripePlan, feature);
  };

  if (isEmpty(form)) {
    return (
      <div className="pb-[80px]">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold">Settings</h3>
            <p className="mt-2 text-gray-600">Manage your form settings.</p>
          </div>
        </div>
        <div className="mt-6 space-y-5">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} className="h-[400px] w-full rounded-xl" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="pb-[80px]">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold">Settings</h3>
          <p className="mt-2 text-gray-600">Manage your form settings.</p>
        </div>
      </div>

      <div className="mt-6">
        <Paper>
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold">General</h3>
            </div>
          </div>
          <Divider />
          <div className="px-4 py-5 sm:px-6">
            <div className="space-y-3 sm:flex sm:items-center sm:justify-between sm:space-y-0 sm:space-x-16">
              <div className="space-y-1">
                <h4 className="font-semibold">Form name</h4>
                <p className="text-sm text-gray-600">
                  Only visible to you and your teammates.
                </p>
              </div>
              <div>
                <Input
                  className="sm:w-[400px]"
                  defaultValue={form?.name}
                  onChange={handleNameChange}
                  disabled={userRole?.role === "viewer"}
                />
              </div>
            </div>
          </div>
          <Divider />
          <div className="px-4 py-5 sm:px-6">
            <div className="space-y-3 sm:flex sm:items-center sm:justify-between sm:space-y-0 sm:space-x-16">
              <div className="space-y-1">
                <div className="flex items-center space-x-4">
                  <h4 className="font-semibold">Submission storage duration</h4>
                  {!hasAccess("Submission storage duration") && (
                    <UpgradeBadge />
                  )}
                </div>
                <p className="text-sm text-gray-600">
                  Choose how long Formbox stores your form submissions.
                </p>
              </div>
              <div>
                <Select
                  value={form?.submissionStorageDuration}
                  onValueChange={handleStorageDurationChange}
                  disabled={
                    userRole?.role === "viewer" ||
                    !hasAccess("Submission storage duration")
                  }
                >
                  <SelectGroup>
                    <SelectTrigger className="sm:w-[400px]">
                      <SelectValue placeholder="Choose a duration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectLabel>Choose a duration</SelectLabel>
                      <SelectItem value="never">Never</SelectItem>
                      <SelectItem value="30">30 Days</SelectItem>
                      <SelectItem value="60">60 Days</SelectItem>
                      <SelectItem value="90">90 Days</SelectItem>
                      <SelectItem value="365">365 Days</SelectItem>
                      <SelectItem value="forever">Forever</SelectItem>
                    </SelectContent>
                  </SelectGroup>
                </Select>
              </div>
            </div>
          </div>
          <Divider />
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <div className="flex items-center space-x-4">
                  <h4 className="font-semibold">Remove Formbox branding</h4>
                  {!hasAccess("Remove Formbox branding") && <UpgradeBadge />}
                </div>
                <p className="text-sm text-gray-600">
                  Remove &quot;Powered by Formbox&quot; branding on your form.
                </p>
              </div>
              <div>
                <Switch
                  checked={form?.removeFormboxBranding}
                  onCheckedChange={handleRemoveFormboxBrandingChange}
                  disabled={
                    userRole?.role === "viewer" ||
                    !hasAccess("Remove Formbox branding")
                  }
                />
              </div>
            </div>
          </div>
          <Divider />
          <div className="px-5 py-5 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h4 className="font-semibold">Delete form</h4>
                <p className="text-sm text-gray-600">
                  Terminate your form with all of its submissions and data.
                </p>
              </div>
              <div>
                <Button
                  variant="outline"
                  leftIcon={<IconTrash size={16} className="text-red-600" />}
                  onClick={deleteModalHandler.open}
                  disabled={userRole?.role === "viewer"}
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </Paper>
      </div>

      <div className="mt-5">
        <Paper>
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold">Email configuration</h3>
            </div>
          </div>

          <Divider />

          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <h4 className="font-semibold">Self email notifications</h4>
                <p className="text-sm text-gray-600">
                  Get an email for you and your team on new submissions.
                </p>
              </div>
              <div>
                <Switch
                  checked={form?.sendEmailNotifications}
                  onCheckedChange={handleEmailNotificationChange}
                  disabled={userRole?.role === "viewer"}
                />
              </div>
            </div>

            {form?.sendEmailNotifications && (
              <div className="mt-4">
                <Input
                  label="Emails to notify"
                  description='Separate emails with a comma ","'
                  placeholder="e.g. <EMAIL>, <EMAIL>"
                  defaultValue={getEmailsToNotify(form?.emailsToNotify || [""])}
                  onChange={handleEmailsToNotifyChange}
                  disabled={userRole?.role === "viewer"}
                />
              </div>
            )}
          </div>

          <Divider />

          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center space-x-4">
                  <h4 className="font-semibold">
                    Respondent email notifications
                  </h4>
                  {!hasAccess("Auto responses") && <UpgradeBadge />}
                </div>

                <p className="text-sm text-gray-600">
                  Send a customized email to respondents after a successful form
                  submission.
                </p>
              </div>
              <div>
                <Switch
                  checked={form?.sendRespondantEmailNotifications}
                  onCheckedChange={handleRespondantEmailNotificationChange}
                  disabled={
                    !hasAccess("Auto responses") || userRole?.role === "viewer"
                  }
                />
              </div>
            </div>

            {form?.sendRespondantEmailNotifications && (
              <>
                <Divider className="mt-4" />

                <div className="mt-4 flex items-center justify-between space-x-16">
                  <div className="space-y-1">
                    <h4 className="font-semibold">
                      Auto response email template
                    </h4>
                    <p className="text-sm text-gray-600">
                      Customize your respondent email notification template.
                    </p>
                  </div>
                  <div>
                    <Button
                      variant="outline"
                      onClick={respondantEmailModalHandler.open}
                      disabled={
                        !hasAccess("Auto responses") ||
                        userRole?.role === "viewer"
                      }
                    >
                      Edit template
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>

          <Divider />

          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center space-x-4">
                  <h4 className="font-semibold">Custom email domain</h4>
                  {!hasAccess("Auto responses") && <UpgradeBadge />}
                </div>

                <p className="text-sm text-gray-600">
                  Send emails from your own custom domain.
                </p>
              </div>
              <div>
                <Switch
                  checked={form?.smtpEnabled}
                  onCheckedChange={handleSmtpEnabledChange}
                  disabled={
                    !hasAccess("Custom email sending with SMTP") ||
                    userRole?.role === "viewer"
                  }
                />
              </div>
            </div>

            {form?.smtpEnabled && (
              <>
                <Divider className="mt-4" />

                <div className="mt-4 flex items-center justify-between space-x-16">
                  <div className="space-y-1">
                    <h4 className="font-semibold">
                      Configure your SMTP server.
                    </h4>
                    <p className="text-sm text-gray-600">
                      Set up your SMTP server making it easy to send emails from
                      your own custom domain.
                    </p>
                  </div>
                  <div>
                    <Button
                      variant="outline"
                      onClick={smtpModalHandler.open}
                      disabled={
                        !hasAccess("Auto responses") ||
                        userRole?.role === "viewer"
                      }
                    >
                      Edit configuration
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>
        </Paper>
      </div>

      <div className="mt-5">
        <Paper>
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h3 className="text-xl font-semibold">Access</h3>
              </div>
            </div>
          </div>
          <Divider />
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <h4 className="font-semibold">Close form</h4>
                <p className="text-sm text-gray-600">
                  People won&apos;t be able to respond to this form anymore.
                </p>
              </div>
              <div>
                <Switch
                  checked={form?.isClosed}
                  onCheckedChange={handleCloseFormChange}
                  disabled={userRole?.role === "viewer"}
                />
              </div>
            </div>
          </div>

          <Divider />

          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <div className="flex items-center space-x-4">
                  <h4 className="font-semibold">
                    Close form on a specific date
                  </h4>
                </div>
                <p className="text-sm text-gray-600">
                  Automatically close your form on a specific date and time.
                </p>
              </div>
              <div>
                <Switch
                  checked={form?.autoCloseEnabled}
                  onCheckedChange={handleAutoCloseEnabledChange}
                />
              </div>
            </div>

            {form?.autoCloseEnabled && (
              <div className="mt-4">
                <Autocomplete
                  label="Timezone"
                  options={timezoneOptions}
                  isMulti={false}
                  placeholder="Select a timezone"
                  onChange={handleAutoCloseTimezoneChange}
                  defaultValue={
                    timezoneOptions.find(
                      (option) => option.value === form?.autoCloseTimezone,
                    ) ?? timezoneOptions[0]?.value
                  }
                  isDisabled={userRole?.role === "viewer"}
                />
                <div className="mt-4 space-y-4 md:flex md:space-y-0 md:space-x-4">
                  <div className="md:flex-1">
                    <DatePicker
                      label="Close date"
                      value={
                        form?.autoCloseDate
                          ? new Date(form.autoCloseDate)
                          : undefined
                      }
                      onSelect={handleAutoCloseDateChange}
                      description="Select the date when the form should close"
                    />
                  </div>
                  <div className="md:flex-1">
                    <Input
                      type="time"
                      label="Close time"
                      value={form?.autoCloseTime || "23:59"}
                      onChange={(e) =>
                        handleAutoCloseTimeChange(e.currentTarget.value)
                      }
                      description="Time defaults to 23:59 (end of day) if not specified"
                      disabled={userRole?.role === "viewer"}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          <Divider />

          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <h4 className="font-semibold">
                  Limit the number of submissions
                </h4>
                <p className="text-sm text-gray-600">
                  Set how many submissions this form can receive in total.
                </p>
              </div>
              <div>
                <Switch
                  checked={form?.limitResponses}
                  onCheckedChange={handleLimitResponsesChange}
                  disabled={userRole?.role === "viewer"}
                />
              </div>
            </div>

            {form?.limitResponses && (
              <div className="mt-4">
                <Input
                  placeholder="Max submissions"
                  type="number"
                  defaultValue={form?.maxResponses || Infinity}
                  onChange={handleMaxResponsesChange}
                  disabled={userRole?.role === "viewer"}
                />
              </div>
            )}
          </div>

          {form?.type === "hosted" && (
            <>
              <Divider />

              <div className="px-4 py-5 sm:px-6">
                <div className="flex items-center justify-between space-x-16">
                  <div className="space-y-1">
                    <h4 className="font-semibold">Closed form message</h4>
                    <p className="text-sm text-gray-600">
                      This is what your recipients will see if you close the
                      form with one of the options above.
                    </p>
                  </div>
                  <div>
                    <Switch
                      checked={form?.showCustomClosedMessage}
                      onCheckedChange={handleCloseMessageChange}
                    />
                  </div>
                </div>

                {form?.showCustomClosedMessage && (
                  <div className="mt-4 space-y-3">
                    <Input
                      placeholder="Title"
                      defaultValue={form?.closeMessageTitle}
                      onChange={handleCloseMessageTitleChange}
                    />
                    <Textarea
                      placeholder="Description"
                      rows={3}
                      defaultValue={form?.closeMessageDescription}
                      onChange={handleCloseMessageDescriptionChange}
                    />
                  </div>
                )}
              </div>
            </>
          )}
        </Paper>
      </div>

      <div className="mt-5">
        <Paper>
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h3 className="text-xl font-semibold">Custom redirect</h3>
                {!hasAccess("Custom redirect") && <UpgradeBadge />}
              </div>
            </div>
          </div>
          <Divider />
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <h4 className="font-semibold">Use custom redirect</h4>
                <p className="text-sm text-gray-600">
                  Users will be redirected to your custom success or fail
                  URL&apos;s.
                </p>
              </div>
              <div>
                <Switch
                  checked={form?.useCustomRedirect}
                  onCheckedChange={handleUseCustomRedirectChange}
                  disabled={
                    !hasAccess("Custom redirect") || userRole?.role === "viewer"
                  }
                />
              </div>
            </div>

            {form?.useCustomRedirect && (
              <>
                <Divider className="mt-4" />

                <div className="mt-4">
                  <div className="space-y-1">
                    <h4 className="font-semibold">Custom redirect URL</h4>
                    <p className="text-sm text-gray-600">
                      Users will be redirected to your custom URL on successful
                      submission.
                    </p>
                  </div>
                  <div>
                    <Input
                      className="mt-4"
                      placeholder="https://example.com/thanks"
                      defaultValue={form?.customSuccessUrl}
                      onChange={handleSuccessUrlChange}
                      disabled={
                        !hasAccess("Custom redirect") ||
                        userRole?.role === "viewer"
                      }
                    />
                  </div>
                </div>
              </>
            )}
          </div>
        </Paper>
      </div>

      {form?.type === "endpoint" && (
        <div className="mt-5">
          <Paper>
            <div className="px-4 py-5 sm:px-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <h3 className="text-xl font-semibold">
                    Custom thank you page
                  </h3>
                  {!hasAccess("Custom redirect") && <UpgradeBadge />}
                </div>
              </div>
            </div>
            <Divider />
            <div className="px-4 py-5 sm:px-6">
              <div className="flex items-center justify-between space-x-16">
                <div className="space-y-1">
                  <h4 className="font-semibold">Use custom thank you page</h4>
                  <p className="text-sm text-gray-600">
                    Customize the thank you page, or use the default thank you
                    page.
                  </p>
                </div>
                <div>
                  <Switch
                    checked={form?.useCustomThankYouPage}
                    onCheckedChange={handleUseCustomThankYouPageChange}
                    disabled={
                      !hasAccess("Custom redirect") ||
                      userRole?.role === "viewer"
                    }
                  />
                </div>
              </div>

              {form?.useCustomThankYouPage && (
                <>
                  <Divider className="mt-4" />

                  <div className="mt-4 flex items-center justify-between space-x-16">
                    <div className="space-y-1">
                      <h4 className="font-semibold">Thank you page</h4>
                      <p className="text-sm text-gray-600">
                        Customize the default Formbox thank you page.
                      </p>
                    </div>
                    <div>
                      <Link
                        href={`/success/${formId}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Button
                          variant="outline"
                          disabled={
                            !hasAccess("Customize thank you page") ||
                            userRole?.role === "viewer"
                          }
                        >
                          View preview
                        </Button>
                      </Link>
                    </div>
                  </div>

                  <div className="mt-4">
                    <div className="space-y-4">
                      <Input
                        label="Heading"
                        value={form?.tpHeader}
                        onChange={handleTPHeaderChange}
                        placeholder=""
                      />
                      <Textarea
                        label="Message"
                        value={form?.tpMessage}
                        onChange={handleTPMessageChange}
                      />
                      <div className="w-full items-center justify-between space-y-4 md:flex md:space-y-0 md:space-x-4">
                        <div className="w-full">
                          <Input
                            label="Button text"
                            value={form?.tpButtonText}
                            onChange={handleTPButtonTextChange}
                          />
                        </div>
                        <div className="w-full">
                          <Input
                            label="Button URL"
                            value={form?.tpButtonUrl}
                            onChange={handleTPButtonUrlChange}
                          />
                        </div>
                      </div>
                      <div className="w-full items-center justify-between space-y-4 md:flex md:space-y-0 md:space-x-4">
                        <div className="flex w-full place-items-end space-x-4">
                          <div className="w-full grow">
                            <Input
                              label="Button text color"
                              className="w-full"
                              value={form?.tpButtonColor}
                              onChange={handleTPButtonTextColorChange}
                            />
                          </div>
                          <div className="w-full flex-1">
                            <ColorPicker
                              color={form?.tpButtonColor || "#030712"}
                              className="h-[36px] w-[80px]"
                              colorClassName="w-full h-6"
                              onColorChange={(color) =>
                                setForm({
                                  ...form,
                                  tpButtonColor: color.hex,
                                })
                              }
                            />
                          </div>
                        </div>
                        <div className="flex w-full place-items-end space-x-4">
                          <div className="w-full grow">
                            <Input
                              label="Button background color"
                              className="w-full"
                              value={form?.tpButtonBackgroundColor}
                              onChange={handleTPButtonBackgroundColorChange}
                            />
                          </div>
                          <div className="w-full flex-1">
                            <ColorPicker
                              color={form?.tpButtonBackgroundColor || "#f3f4f6"}
                              className="h-[36px] w-[80px]"
                              colorClassName="w-full h-6"
                              onColorChange={(color) =>
                                setForm({
                                  ...form,
                                  tpButtonBackgroundColor: color.hex,
                                })
                              }
                            />
                          </div>
                        </div>
                      </div>

                      <div className="w-full items-center justify-between space-y-4 md:flex md:space-y-0 md:space-x-4">
                        <div className="flex w-full place-items-end space-x-4">
                          <div className="w-full grow">
                            <Input
                              label="Page text color"
                              className="w-full"
                              value={form?.tpTextColor}
                              onChange={handleTPTextColorChange}
                            />
                          </div>
                          <div className="w-full flex-1">
                            <ColorPicker
                              color={form?.tpTextColor || "#030712"}
                              className="h-[36px] w-[80px]"
                              colorClassName="w-full h-6"
                              onColorChange={(color) =>
                                setForm({
                                  ...form,
                                  tpTextColor: color.hex,
                                })
                              }
                            />
                          </div>
                        </div>
                        <div className="flex w-full place-items-end space-x-4">
                          <div className="w-full grow">
                            <Input
                              label="Page background color"
                              className="w-full"
                              value={form?.tpBackgroundColor}
                              onChange={handleTPBackgroundColorChange}
                            />
                          </div>
                          <div className="w-full flex-1">
                            <ColorPicker
                              color={form?.tpBackgroundColor || "#ffffff"}
                              className="h-[36px] w-[80px]"
                              colorClassName="w-full h-6"
                              onColorChange={(color) =>
                                setForm({
                                  ...form,
                                  tpBackgroundColor: color.hex,
                                })
                              }
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </Paper>
        </div>
      )}

      <div className="mt-5">
        <Paper>
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h3 className="text-xl font-semibold">Security</h3>
              </div>
            </div>
          </div>

          <Divider />
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <div className="flex items-center space-x-4">
                  <h4 className="font-semibold">IP address blacklist</h4>
                  {!hasAccess("IP address blacklist") && <UpgradeBadge />}
                </div>
                <p className="text-sm text-gray-600">
                  Block form submissions from specific IP addresses.
                </p>
              </div>
            </div>

            <div className="mt-4 space-y-3">
              <Input
                placeholder="e.g. *******, *******"
                defaultValue={form?.ipBlacklist}
                onChange={handleIpBlacklistChange}
                disabled={
                  !hasAccess("IP address blacklist") ||
                  userRole?.role === "viewer"
                }
              />
            </div>
          </div>

          <Divider />
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <div className="flex items-center space-x-4">
                  <h4 className="font-semibold">Allowed domains</h4>
                  {!hasAccess("Domain restrictions") && <UpgradeBadge />}
                </div>
                <p className="text-sm text-gray-600">
                  Restrict form submissions to specific domains.
                </p>
              </div>
            </div>

            <div className="mt-4 space-y-3">
              <Input
                placeholder="e.g. example.com, blog.example.com"
                defaultValue={form?.allowedDomains}
                onChange={handleAllowedDomainsChange}
                disabled={
                  !hasAccess("Domain restrictions") ||
                  userRole?.role === "viewer"
                }
              />
            </div>
          </div>

          <Divider />
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <div className="flex items-center space-x-4">
                  <h4 className="font-semibold">Allowed countries</h4>
                  {!hasAccess("Country restrictions") && <UpgradeBadge />}
                </div>
                <p className="text-sm text-gray-600">
                  Restrict form submissions to specific countries.
                </p>
              </div>
            </div>

            <div className="mt-4 space-y-3">
              <Autocomplete
                defaultValue={defaultCountriesOptions}
                options={countryOptions}
                isMulti
                placeholder="Select countries"
                onChange={handleAllowedCountriesChange}
                isDisabled={
                  !hasAccess("Country restrictions") ||
                  userRole?.role === "viewer"
                }
              />
            </div>
          </div>

          <Divider />
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <div className="flex items-center space-x-4">
                  <h4 className="font-semibold">Custom honeypot</h4>
                  {!hasAccess("Custom honeypot") && <UpgradeBadge />}
                </div>
                <p className="text-sm text-gray-600">
                  The hidden honeypot field (&quot;_gotcha&quot;) can be used in
                  conjunction with our supported spam filtering methods, and
                  using a custom name makes it work even better.
                </p>
              </div>
            </div>

            <div className="mt-4 space-y-3">
              <Input
                placeholder="_botvortex"
                defaultValue={form?.customHoneypot}
                onChange={handleCustomHoneypotChange}
                disabled={
                  !hasAccess("Custom honeypot") || userRole?.role === "viewer"
                }
              />
            </div>
          </div>

          <Divider />
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between space-x-16">
              <div className="space-y-1">
                <h4 className="font-semibold">Google reCAPTCHA enabled</h4>
                <p className="text-sm text-gray-600">
                  Protect your form with google reCAPTCHA.
                </p>
              </div>
              <div>
                <Switch
                  checked={form?.googleRecaptchaEnabled}
                  onCheckedChange={handleGoogleRecaptchaChange}
                  disabled={userRole?.role === "viewer"}
                />
              </div>
            </div>

            {form?.googleRecaptchaEnabled && (
              <>
                <Divider className="mt-4" />

                <div className="mt-4">
                  <div className="space-y-1">
                    <h4 className="font-semibold">
                      Google reCAPTCHA secret key
                    </h4>
                    <p className="text-sm text-gray-600">
                      Paste your Google reCAPTCHA Secret Key to protect your
                      form.
                    </p>
                  </div>
                  <div>
                    <Input
                      className="mt-4"
                      defaultValue={form?.googleRecaptchaSecretKey}
                      onChange={handleGoogleRecaptchaKeyChange}
                      disabled={userRole?.role === "viewer"}
                    />
                  </div>
                </div>
              </>
            )}
          </div>
        </Paper>
      </div>

      <div className="fixed bottom-0 left-0 w-full border-t border-gray-200 bg-white py-5">
        <MaxWidthWrapper>
          <Button
            leftIcon={<IconDeviceFloppy size={16} />}
            loading={updateMutation.isPending}
            disabled={isEqual(formData, form) || userRole?.role === "viewer"}
            onClick={() => handleSettingsSave()}
            className="disabled:bg-gray-200 disabled:text-gray-500"
          >
            Save changes
          </Button>
        </MaxWidthWrapper>
      </div>

      <FormSmtpDialog
        open={smtpModal}
        onClose={smtpModalHandler.close}
        submit={handleSettingsSave}
        form={form as FormByIdOutput}
      />

      <FormRespondantEmailTemplateDialog
        open={respondantEmailModal}
        onClose={respondantEmailModalHandler.close}
        submit={handleSettingsSave}
        form={form as FormByIdOutput}
      />

      <FormDeleteDialog
        title={form?.name}
        open={deleteModal}
        onClose={deleteModalHandler.close}
        onDelete={handleDeleteForm}
        loading={deleteMutation.isPending}
      />
    </div>
  );
}

function UpgradeBadge() {
  return <Badge variant="blue">Upgrade</Badge>;
}
