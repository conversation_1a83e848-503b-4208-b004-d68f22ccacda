"use client";
import { Rating } from "@/components/ui/Rating";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { FileUploader } from "@/components/ui/file-uploader";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Logo } from "@/components/ui/logo";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { env } from "@/env";
import type { FormByIdOutput, FormField, FormFile } from "@/types/form.types";
import {
  EMAIL_REGEX,
  NUMBER_REGEX,
  PHONE_NUMBER_REGEX,
} from "@/utils/constants";
import { cn } from "@/utils/tailwind-helpers";
import {
  IconArrowLeft,
  IconCircleCheck,
  IconExclamationCircle,
} from "@tabler/icons-react";
import { useMutation } from "@tanstack/react-query";
import { formatDate } from "date-fns";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { isEmpty } from "radash";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";

// Accept multiple files and submit as fileUploads array
async function createSubmission(
  answers: Record<string, string | string[]>,
  formId: string,
  files?: FormFile[],
) {
  // Remove file field answers from answersToSubmit (by label)
  const fileFieldNames = files?.map((file) => file.formFieldName) || [];
  const answersToSubmit = Object.entries(answers).reduce(
    (acc, [key, value]) => {
      if (fileFieldNames.includes(key)) {
        return acc;
      }
      return { ...acc, [key]: value };
    },
    {},
  );
  return await fetch(`${env.NEXT_PUBLIC_SUBMISSIONS_API_URL}/s/${formId}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
    body: JSON.stringify({
      ...answersToSubmit,
      ...(files && files.length > 0 ? { fileUploads: files } : {}),
    }),
  });
}

function processAnswers(
  data: Record<string, string | string[]>,
  form: FormByIdOutput,
) {
  const answers = Object.entries(data)
    .map(([key, value]) => {
      const label = form?.fields.find((field) => field.id === key)
        ?.label as string;
      return {
        [label]: value,
      };
    })
    .reduce((acc, cur) => ({ ...acc, ...cur }), {});
  return answers;
}

interface Props {
  form: FormByIdOutput;
  redirect?: () => void;
}

export function FormPreview({ form, redirect }: Props) {
  const router = useRouter();
  const [submissionError, setSubmissionError] = useState<string | null>(null);
  const [currentFieldGroupIndex, setCurrentFieldGroupIndex] = useState(0);
  const [currentFields, setCurrentFields] = useState<FormField[]>();
  // Store multiple files as an array of FormFile
  const [files, setFiles] = useState<FormFile[]>([]);
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
  } = useForm();

  console.log("Form: ", form);

  const submissionCreateMutation = useMutation({
    mutationFn: async (data: Record<string, string | string[]>) =>
      await createSubmission(processAnswers(data, form), form.id, files),
    onError: (error) => {
      console.log(error);
    },
  });

  useEffect(() => {
    const fields = fieldGroups(form)[currentFieldGroupIndex];
    setCurrentFields(fields);
  }, [form, currentFieldGroupIndex]);

  /**
   * Create field groups (or Page) using page breaks if any
   */
  function fieldGroups(form: FormByIdOutput) {
    if (!form?.fields?.length) return [];
    const groups = [];
    let currentGroup: FormField[] = [];
    form.fields.forEach((field) => {
      currentGroup.push(field);
      if (field.type === "fb-page-break") {
        groups.push(currentGroup);
        currentGroup = [];
      }
    });
    groups.push(currentGroup);
    return groups;
  }

  function getPreviousPage() {
    setCurrentFieldGroupIndex((prevIndex) => prevIndex - 1);
  }

  function getNextPage() {
    setCurrentFieldGroupIndex((prevIndex) => prevIndex + 1);
  }

  /**
   * Returns true if we're on the last page
   */
  function isLastPage() {
    const fields = fieldGroups(form);
    return currentFieldGroupIndex === fields.length - 1 || fields.length === 0;
  }

  async function onSubmit(data: Record<string, string | string[]>) {
    try {
      const result = await submissionCreateMutation.mutateAsync(data);

      const resultData = await result.json();

      if (resultData.error) {
        setSubmissionError(resultData.error);
      }

      // If custom redirect is enabled, redirect to custom success url
      if (
        form.useCustomRedirect &&
        !isEmpty(form.customSuccessUrl) &&
        redirect &&
        result.ok
      ) {
        redirect();
      }
    } catch (error) {
      console.log(error);
    }
  }

  function resetForm() {
    setCurrentFieldGroupIndex(0);
    setFiles([]);
    setSubmissionError(null);
    submissionCreateMutation.reset();
    router.refresh();
  }

  const buttonBorderStyles = {
    "rounded-full": form?.buttonBorderStyle === "full",
    "rounded-lg": form?.buttonBorderStyle === "rounded",
    "rounded-none": form?.buttonBorderStyle === "flat",
  };

  const inputBorderStyles = {
    "rounded-full": form?.inputBorderStyle === "full",
    "rounded-lg": form?.inputBorderStyle === "rounded",
    "rounded-none": form?.inputBorderStyle === "flat",
  };

  const selectContentInputBorderStyles = {
    "rounded-lg":
      form?.inputBorderStyle === "rounded" || form?.inputBorderStyle === "full",
    "rounded-none": form?.inputBorderStyle === "flat",
  };

  const inputBorderStylesTextarea = {
    "rounded-[16px]": form?.inputBorderStyle === "full",
    "rounded-lg": form?.inputBorderStyle === "rounded",
    "rounded-none": form?.inputBorderStyle === "flat",
  };

  const inputBorderStylesCheckbox = {
    rounded:
      form?.inputBorderStyle === "full" || form?.inputBorderStyle === "rounded",
    "rounded-none": form?.inputBorderStyle === "flat",
  };

  const accentColorStyle = {
    "--accent-color": form?.accentColor,
  } as React.CSSProperties;

  const twAccentColorStyle = {
    "--tw-ring-color": form?.accentColor,
  } as React.CSSProperties;

  const hasHeaderImage = !isEmpty(form?.headerImage);
  const hasLogoImage = !isEmpty(form?.logo);

  const isFreePlan =
    form?.organization?.stripePlan?.toLocaleLowerCase() === "free" ||
    isEmpty(form.organization?.stripePlan);

  const shouldRedirect =
    !isFreePlan && form.useCustomRedirect && !isEmpty(form.customSuccessUrl);

  const shouldRemoveFormboxBranding = !isFreePlan && form.removeFormboxBranding;

  return (
    <div className={cn("h-full w-full")}>
      <div className="fixed top-0 left-0 -z-10 h-full w-full">
        <div
          className="absolute inset-0 -z-10 h-screen w-full"
          style={{
            backgroundColor: form?.backgroundColor,
          }}
        />
      </div>
      {form?.pageMode === "full" && hasHeaderImage && (
        <div
          className="h-[350px] w-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url(${form?.headerImage})`,
          }}
        ></div>
      )}
      <div className="relative mx-auto px-5">
        <div
          className={cn(
            "mx-auto max-w-3xl px-5 py-10 md:px-[70px] md:py-20",
            form?.pageMode === "full" && "py-10 md:py-10",
          )}
        >
          {/* Render submission error if present */}
          {submissionError && (
            <div
              className={cn(
                "flex h-full w-full flex-col items-center justify-center text-center",
              )}
            >
              <IconExclamationCircle
                size={60}
                className="mx-auto text-red-600"
              />
              <h2 className="mt-4 text-2xl font-semibold lg:text-3xl">
                Sorry!
              </h2>
              <p className="mt-4 max-w-lg font-light lg:text-xl">
                {submissionError}
              </p>
            </div>
          )}

          {form?.pageMode === "compact" && hasHeaderImage && (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={form?.headerImage as string}
              alt=""
              className="mb-12 h-[270px] w-full"
            />
          )}
          {hasLogoImage && (
            <div
              className={cn(
                form?.pageMode === "full" &&
                  hasHeaderImage &&
                  "-mt-[90px] md:-mt-[95px] lg:-mt-[95px]",
                form?.pageMode === "compact" && hasHeaderImage && "-mt-24 ml-5",
                !hasHeaderImage && !submissionError && "hidden",
              )}
            >
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={form?.logo}
                alt={"Logo"}
                className={cn(
                  "h-28 w-28 rounded-full object-cover object-center",
                )}
              />
            </div>
          )}

          {submissionCreateMutation.isSuccess &&
            !submissionError &&
            !shouldRedirect && (
              <div
                className={cn(
                  "text-center",
                  hasLogoImage && "mt-8",
                  !hasHeaderImage &&
                    "mt-24 flex h-full w-full flex-col items-center justify-center",
                )}
                style={{ color: form?.textColor }}
              >
                <IconCircleCheck size={60} className="mx-auto" />
                {!isFreePlan && (
                  <>
                    <h2 className="mt-6 text-2xl font-semibold lg:text-3xl">
                      {form?.tpHeader || "Thanks for completing this form!"}
                    </h2>
                    <p className="mt-6 font-light lg:text-xl">
                      {!form?.tpMessage && !form.removeFormboxBranding
                        ? "Made with Formbox, the easiest way to create forms for free."
                        : form?.tpMessage || null}
                    </p>

                    <div className="mx-auto mt-6 flex w-full flex-col items-center justify-center gap-4 sm:flex-row sm:justify-center">
                      {form?.tpButtonUrl && form.removeFormboxBranding && (
                        <div className="w-full sm:w-auto">
                          <Button
                            variant="secondary"
                            className="w-full sm:w-auto"
                            href={form?.tpButtonUrl}
                            target="_blank"
                          >
                            {form?.tpButtonText}
                          </Button>
                        </div>
                      )}
                      {form?.tpButtonUrl && !form.removeFormboxBranding && (
                        <div className="w-full sm:w-auto">
                          <Button
                            variant="secondary"
                            className="w-full sm:w-auto"
                            href={form?.tpButtonUrl}
                            target="_blank"
                          >
                            {form?.tpButtonText}
                          </Button>
                        </div>
                      )}
                      {!form?.tpButtonUrl && !form.removeFormboxBranding && (
                        <div className="w-full sm:w-auto">
                          <Button
                            variant="secondary"
                            className="w-full sm:w-auto"
                            href="https://formbox.app"
                            target="_blank"
                          >
                            Create your own form
                          </Button>
                        </div>
                      )}

                      <div className="w-full sm:w-auto">
                        <Button
                          variant="outline"
                          className="w-full sm:w-auto"
                          onClick={resetForm}
                        >
                          Submit another response
                        </Button>
                      </div>
                    </div>
                  </>
                )}
                {isFreePlan && (
                  <>
                    <h2 className="mt-6 text-2xl font-semibold lg:text-3xl">
                      Thanks for completing this form!
                    </h2>
                    <p className="mt-6 font-light lg:text-xl">
                      Made with Formbox, the easiest way to create forms for
                      free.
                    </p>

                    <div className="mx-auto mt-8 flex w-full flex-col items-center justify-center gap-4 sm:flex-row sm:justify-center">
                      <div className="w-full sm:w-auto">
                        <Button
                          variant="secondary"
                          className="w-full sm:w-auto"
                          href="https://formbox.app"
                          target="_blank"
                        >
                          Create your own form
                        </Button>
                      </div>
                      <div className="w-full sm:w-auto">
                        <Button
                          variant="outline"
                          className="w-full sm:w-auto"
                          onClick={resetForm}
                        >
                          Submit another response
                        </Button>
                      </div>
                    </div>
                  </>
                )}

                {!shouldRemoveFormboxBranding && (
                  <Link
                    href="https://formbox.app"
                    target="_blank"
                    rel="noreferrer"
                  >
                    <div className="mt-10">
                      <Button variant="outline" size="xs">
                        <div className="flex items-center space-x-2 text-gray-900">
                          <span>Powered by</span>{" "}
                          <span>
                            <Logo className="w-20" noLink />
                          </span>
                        </div>
                      </Button>
                    </div>
                  </Link>
                )}
              </div>
            )}

          {!submissionError && !submissionCreateMutation.isSuccess && (
            <>
              {currentFields && (
                <div className={cn(hasLogoImage && "mt-8")}>
                  {form?.headerTitle && (
                    <h2
                      className="text-3xl font-semibold"
                      style={{ color: form?.textColor }}
                    >
                      {form?.headerTitle}
                    </h2>
                  )}
                  {form?.headerDescription && (
                    <div
                      className={cn(
                        "prose mt-4 max-w-none sm:prose hover:prose-a:underline prose-strong:text-current prose-ul:marker:text-gray-900 prose-li:marker:text-gray-900",
                        form?.textColor === "#000000" &&
                          "prose-a:text-blue-600",
                        form?.textColor !== "#000000" && "prose-a:text-current",
                      )}
                      style={{ color: form?.textColor }}
                      dangerouslySetInnerHTML={{
                        __html: form.headerDescription,
                      }}
                    ></div>
                  )}
                </div>
              )}

              {currentFieldGroupIndex > 0 && (
                <div className="mt-8">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={getPreviousPage}
                    leftIcon={<IconArrowLeft size={16} />}
                    className={cn(buttonBorderStyles)}
                  >
                    Previous
                  </Button>
                </div>
              )}

              <form
                className="mt-8 space-y-10"
                onSubmit={handleSubmit(onSubmit)}
                noValidate
              >
                <div className="space-y-6">
                  {currentFields?.map((field) => {
                    if (field.subtype === "short_answer") {
                      return (
                        <Input
                          key={field.id}
                          label={field.label}
                          description={field.description}
                          className={cn(inputBorderStyles, "accent-color")}
                          style={accentColorStyle}
                          styles={{
                            label: { color: form?.textColor },
                            description: {
                              color:
                                form?.textColor !== "#000000"
                                  ? form?.textColor
                                  : "",
                            },
                          }}
                          required={field.required}
                          {...register(`${field.id}`, {
                            required: {
                              value: field.required,
                              message: `${field.label} is required`,
                            },
                          })}
                          error={errors[`${field.id}`]?.message !== undefined}
                          errorMessage={
                            errors[`${field.id}`]?.message as string
                          }
                        />
                      );
                    }
                    if (field.subtype === "email") {
                      return (
                        <Input
                          key={field.id}
                          label={field.label}
                          description={field.description}
                          type="email"
                          className={cn(inputBorderStyles, "accent-color")}
                          style={accentColorStyle}
                          styles={{
                            label: { color: form?.textColor },
                            description: {
                              color:
                                form?.textColor !== "#000000"
                                  ? form?.textColor
                                  : "",
                            },
                          }}
                          required={field.required}
                          {...register(`${field.id}`, {
                            required: {
                              value: field.required,
                              message: `${field.label} is required`,
                            },
                            pattern: {
                              value: EMAIL_REGEX,
                              message: "Please enter a valid email address",
                            },
                          })}
                          error={errors[`${field.id}`]?.message !== undefined}
                          errorMessage={
                            errors[`${field.id}`]?.message as string
                          }
                        />
                      );
                    }
                    if (field.subtype === "long_answer") {
                      return (
                        <Textarea
                          key={field.id}
                          label={field.label}
                          description={field.description}
                          className={cn(
                            inputBorderStylesTextarea,
                            "accent-color",
                          )}
                          style={accentColorStyle}
                          styles={{
                            label: { color: form?.textColor },
                            description: {
                              color:
                                form?.textColor !== "#000000"
                                  ? form?.textColor
                                  : "",
                            },
                          }}
                          classNames={{
                            description: cn(
                              form?.textColor !== "#000000" && "text-current",
                            ),
                          }}
                          required={field.required}
                          {...register(`${field.id}`, {
                            required: {
                              value: field.required,
                              message: `${field.label} is required`,
                            },
                          })}
                          error={errors[`${field.id}`]?.message !== undefined}
                          errorMessage={
                            errors[`${field.id}`]?.message as string
                          }
                        />
                      );
                    }
                    if (field.subtype === "number") {
                      return (
                        <Input
                          key={field.id}
                          label={field.label}
                          description={field.description}
                          type="number"
                          className={cn(inputBorderStyles, "accent-color")}
                          style={accentColorStyle}
                          styles={{
                            label: { color: form?.textColor },
                            description: {
                              color:
                                form?.textColor !== "#000000"
                                  ? form?.textColor
                                  : "",
                            },
                          }}
                          required={field.required}
                          {...register(`${field.id}`, {
                            required: {
                              value: field.required,
                              message: `${field.label} is required`,
                            },
                            pattern: {
                              value: NUMBER_REGEX,
                              message: "Please enter a valid number",
                            },
                          })}
                          error={errors[`${field.id}`]?.message !== undefined}
                          errorMessage={
                            errors[`${field.id}`]?.message as string
                          }
                        />
                      );
                    }
                    if (field.subtype === "phone") {
                      return (
                        <Input
                          key={field.id}
                          label={field.label}
                          description={field.description}
                          className={cn(inputBorderStyles, "accent-color")}
                          style={accentColorStyle}
                          styles={{
                            label: { color: form?.textColor },
                            description: {
                              color:
                                form?.textColor !== "#000000"
                                  ? form?.textColor
                                  : "",
                            },
                          }}
                          required={field.required}
                          {...register(`${field.id}`, {
                            required: {
                              value: field.required,
                              message: `${field.label} is required`,
                            },
                            pattern: {
                              value: PHONE_NUMBER_REGEX,
                              message: "Please enter a valid phone number",
                            },
                          })}
                          error={errors[`${field.id}`]?.message !== undefined}
                          errorMessage={
                            errors[`${field.id}`]?.message as string
                          }
                        />
                      );
                    }
                    if (field.subtype === "single_choice") {
                      return (
                        <Controller
                          key={field.id}
                          rules={{
                            required: {
                              value: field.required,
                              message: `${field.label} is required`,
                            },
                            // value: localStorage?.getItem(field.id) || "",
                          }}
                          control={control}
                          name={field.id}
                          render={({ field: { onChange } }) => (
                            <RadioGroup
                              key={field.id}
                              label={field?.label}
                              description={field?.description}
                              styles={{
                                label: { color: form?.textColor },
                                description: {
                                  color:
                                    form?.textColor !== "#000000"
                                      ? form?.textColor
                                      : "",
                                },
                              }}
                              className="space-y-2"
                              defaultValue={
                                localStorage?.getItem(field.id) || ""
                              }
                              onValueChange={(value) => {
                                onChange(value);
                              }}
                              required={field.required}
                              error={
                                errors[`${field.id}`]?.message !== undefined
                              }
                              errorMessage={
                                errors[`${field.id}`]?.message as string
                              }
                            >
                              {field?.options?.map((option) => (
                                <div
                                  className="flex items-center space-x-2"
                                  key={option.id}
                                >
                                  <RadioGroupItem
                                    value={option.value}
                                    id={option.id}
                                    className="border-gray-300 bg-white data-[state=checked]:border-(--accent-color)"
                                    style={{
                                      color: form?.accentColor,
                                      ...twAccentColorStyle,
                                    }}
                                  />
                                  <label
                                    className="text-sm"
                                    htmlFor={option.id}
                                    style={{ color: form?.textColor }}
                                  >
                                    {option.value}
                                  </label>
                                </div>
                              ))}
                            </RadioGroup>
                          )}
                        />
                      );
                    }

                    if (field.subtype === "dropdown") {
                      return (
                        <Controller
                          key={field.id}
                          rules={{
                            required: {
                              value: field.required,
                              message: `${field.label} is required`,
                            },
                          }}
                          control={control}
                          name={field.id}
                          render={({ field: { onChange } }) => (
                            <div key={field.id}>
                              <Label>{field?.label}</Label>
                              {field.showDescription && (
                                <p
                                  className={cn("text-sm text-gray-500")}
                                  style={{
                                    color:
                                      form?.textColor !== "#000000"
                                        ? form?.textColor
                                        : "",
                                  }}
                                >
                                  {field.description}
                                </p>
                              )}
                              <Select
                                onValueChange={onChange}
                                required={field.required}
                              >
                                <SelectTrigger
                                  className={cn(
                                    "mt-[5px]",
                                    errors[`${field.id}`]?.message &&
                                      "accent-color border-red-500 focus:border-red-500!",
                                    inputBorderStyles,
                                  )}
                                  style={{ ...twAccentColorStyle }}
                                >
                                  <SelectValue placeholder="Select an option" />
                                </SelectTrigger>
                                <SelectContent
                                  className={cn(selectContentInputBorderStyles)}
                                >
                                  {field?.options?.map((option) => (
                                    <SelectItem
                                      value={option.value}
                                      key={option.id}
                                      className={cn(
                                        selectContentInputBorderStyles,
                                      )}
                                    >
                                      {option.value}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              {errors[`${field.id}`]?.message && (
                                <p className="mt-1 text-sm text-red-500">
                                  {errors[`${field.id}`]?.message as string}
                                </p>
                              )}
                            </div>
                          )}
                        />
                      );
                    }

                    if (field.subtype === "date") {
                      return (
                        <Controller
                          key={field.id}
                          rules={{
                            required: {
                              value: field.required,
                              message: `${field.label} is required`,
                            },
                          }}
                          control={control}
                          name={field.id}
                          render={({ field: { onChange } }) => (
                            <div key={field.id}>
                              <DatePicker
                                label={field?.label}
                                description={field?.description}
                                onSelect={(date) => {
                                  if (!date) return;
                                  onChange(formatDate(date, "PPP"));
                                }}
                                classNames={{
                                  button: cn(inputBorderStyles),
                                  day_selected: cn(buttonBorderStyles),
                                }}
                                style={{ ...twAccentColorStyle }}
                                styles={{
                                  description: {
                                    color:
                                      form?.textColor !== "#000000"
                                        ? form?.textColor
                                        : "",
                                  },
                                }}
                              />
                              {errors[`${field.id}`]?.message && (
                                <p className="mt-1 text-sm text-red-500">
                                  {errors[`${field.id}`]?.message as string}
                                </p>
                              )}
                            </div>
                          )}
                        />
                      );
                    }

                    if (field.subtype === "rating") {
                      return (
                        <Controller
                          key={field.id}
                          rules={{
                            required: {
                              value: field.required,
                              message: `${field.label} is required`,
                            },
                          }}
                          control={control}
                          name={field.id}
                          render={({ field: { onChange } }) => (
                            <div key={field.id}>
                              <div className="space-y-2">
                                <div>
                                  <Label
                                    className="cursor-pointer"
                                    style={{ color: form?.textColor }}
                                  >
                                    {field?.label}
                                  </Label>
                                  {field?.description && (
                                    <p
                                      className="mb-1 block text-sm text-gray-500"
                                      style={{
                                        color:
                                          form?.textColor !== "#000000"
                                            ? form?.textColor
                                            : "",
                                      }}
                                    >
                                      {field?.description}
                                    </p>
                                  )}
                                </div>
                                <div className="mt-[5px] space-y-2">
                                  <Rating
                                    ratingCount={field.ratingCount}
                                    onChange={onChange}
                                  />
                                </div>
                              </div>
                              {errors[`${field.id}`]?.message && (
                                <p className="mt-1 text-sm text-red-500">
                                  {errors[`${field.id}`]?.message as string}
                                </p>
                              )}
                            </div>
                          )}
                        />
                      );
                    }

                    if (field.subtype === "file_upload") {
                      return (
                        <Controller
                          key={field.id}
                          rules={{
                            required: {
                              value: field.required,
                              message: `${field.label} is required`,
                            },
                          }}
                          control={control}
                          name={field.id}
                          render={({ field: { onChange } }) => (
                            <div key={field.id}>
                              <div className="space-y-2">
                                <div>
                                  <Label
                                    className="cursor-pointer"
                                    style={{ color: form?.textColor }}
                                  >
                                    {field?.label}
                                  </Label>
                                  {field?.description && (
                                    <p
                                      className="mb-1 block text-sm text-gray-500"
                                      style={{
                                        color:
                                          form?.textColor !== "#000000"
                                            ? form?.textColor
                                            : "",
                                      }}
                                    >
                                      {field?.description}
                                    </p>
                                  )}
                                </div>
                                <div className="mt-[5px] space-y-2">
                                  <FileUploader
                                    className={cn(inputBorderStylesTextarea)}
                                    style={{ ...twAccentColorStyle }}
                                    orgId={form?.organizationId}
                                    formId={form?.id}
                                    multiple
                                    onUploadComplete={(uploaded) => {
                                      // uploaded can be FormFile or FormFile[]
                                      const uploadedArr = Array.isArray(
                                        uploaded,
                                      )
                                        ? uploaded
                                        : [uploaded];
                                      // Remove any previous files for this field (by label)
                                      setFiles((prev) => [
                                        ...prev.filter(
                                          (file) =>
                                            file.formFieldName !== field.label,
                                        ),
                                        ...uploadedArr.map((file) => ({
                                          ...file,
                                          formFieldName: field.label,
                                        })),
                                      ]);
                                      // Set form value to array of file names
                                      onChange(uploadedArr.map((f) => f.name));
                                    }}
                                  />
                                </div>
                              </div>
                              {errors[`${field.id}`]?.message && (
                                <p className="mt-1 text-sm text-red-500">
                                  {errors[`${field.id}`]?.message as string}
                                </p>
                              )}
                            </div>
                          )}
                        />
                      );
                    }

                    if (field.subtype === "multiple_choice") {
                      return (
                        <div className="space-y-2" key={field.id}>
                          <div>
                            <label
                              className="block text-sm leading-6 font-medium"
                              style={{ color: form?.textColor }}
                            >
                              {field?.label}
                            </label>
                            {field.showDescription && (
                              <p
                                className="block text-sm text-gray-500"
                                style={{
                                  color:
                                    form?.textColor !== "#000000"
                                      ? form?.textColor
                                      : "",
                                }}
                              >
                                {field.description}
                              </p>
                            )}
                          </div>
                          <div className="mt-[4px] space-y-4">
                            {field?.options?.map((option) => (
                              <div
                                className="flex items-center space-x-2"
                                key={option.id}
                              >
                                <input
                                  type="checkbox"
                                  id={option.id}
                                  className={cn(
                                    "accent-color h-[18px] w-[18px] cursor-pointer rounded border-gray-300 text-primary outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2",
                                    inputBorderStylesCheckbox,
                                  )}
                                  style={{
                                    color: form?.accentColor,
                                    ...twAccentColorStyle,
                                  }}
                                  value={option.value}
                                  {...register(field.id, {
                                    required: {
                                      value: field.required,
                                      message: `${field.label} is required`,
                                    },
                                  })}
                                />
                                <label
                                  className="text-sm"
                                  htmlFor={option.id}
                                  style={{ color: form?.textColor }}
                                >
                                  {option.value}
                                </label>
                              </div>
                            ))}
                          </div>
                          {errors[`${field.id}`]?.message !== undefined && (
                            <p className="mt-1 text-sm text-red-500">
                              {errors[`${field.id}`]?.message as string}
                            </p>
                          )}
                        </div>
                      );
                    }

                    if (field.subtype === "heading") {
                      return (
                        <div
                          key={field.id}
                          className="prose space-y-2 prose-headings:m-0 prose-headings:font-semibold prose-p:m-0"
                        >
                          <h2 style={{ color: form?.textColor }}>
                            {field.label}
                          </h2>
                          {field.showDescription && <p>{field.description}</p>}
                        </div>
                      );
                    }
                  })}
                </div>

                {!isLastPage() && currentFields && (
                  <div className="flex items-center justify-center">
                    <Button
                      size="lg"
                      type="button"
                      onClick={handleSubmit(getNextPage)}
                      style={
                        {
                          backgroundColor: form?.buttonBackgroundColor,
                          ...twAccentColorStyle,
                        } as React.CSSProperties
                      }
                      className={cn(buttonBorderStyles)}
                    >
                      Next
                    </Button>
                  </div>
                )}

                {isLastPage() && currentFields && (
                  <div className="flex items-center justify-center space-x-3">
                    <Button
                      size="lg"
                      type="button"
                      style={
                        {
                          color: form?.buttonTextColor,
                          backgroundColor: form?.buttonBackgroundColor,
                          ...twAccentColorStyle,
                        } as React.CSSProperties
                      }
                      className={cn(buttonBorderStyles)}
                      onClick={handleSubmit(onSubmit)}
                      loading={isSubmitting}
                    >
                      {form?.submitButtonText}
                    </Button>
                  </div>
                )}
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
