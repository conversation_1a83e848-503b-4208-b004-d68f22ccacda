import { <PERSON><PERSON> } from "@/components/ui/button";
import { Divider } from "@/components/ui/divider";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from "@/components/ui/switch";
import { ToolTip } from "@/components/ui/tooltip";
import { nanoid } from "@/libs/nanoid";
import { type FormByIdOutput, type FormField } from "@/types/form.types";
import { debounce } from "@/utils/debounce";
import { move } from "@dnd-kit/helpers";
import { DragDropProvider } from "@dnd-kit/react";
import { useSortable } from "@dnd-kit/react/sortable";
import {
  IconCircleCheck,
  IconCopy,
  IconGripVertical,
  IconPlus,
  IconSquareCheck,
  IconTrash,
  IconX,
} from "@tabler/icons-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { FormFieldContainer } from "../form-field-container";

interface Props {
  form: FormByIdOutput;
  element: FormField;
  index?: number;
  selectedId?: string;
  setSelectedId: (fieldId: string) => void;
  setForm: (form: FormByIdOutput) => void;
}

export function SingleChoiceElement({
  element,
  index,
  selectedId,
  setSelectedId,
  form,
  setForm,
}: Props) {
  const [newId, setNewId] = useState(selectedId);
  const [localLabel, setLocalLabel] = useState(element.label);
  const [localDescription, setLocalDescription] = useState(element.description);
  const [lastAddedOptionId, setLastAddedOptionId] = useState<string | null>(
    null,
  );
  const optionRefs = useRef<Record<string, HTMLInputElement | null>>({});

  const isSelected = element.id === selectedId;

  const debouncedSetForm = useMemo(
    () =>
      debounce((updatedForm: FormByIdOutput) => {
        setForm(updatedForm);
      }, 300),
    [setForm],
  );

  const updateLabelOrDescription = (
    e: React.SyntheticEvent<HTMLInputElement>,
  ) => {
    const { value, name } = e.currentTarget;
    const fieldName = name as keyof FormField;

    // Update local state immediately for responsive UI
    if (fieldName === "label") {
      setLocalLabel(value);
    } else if (fieldName === "description") {
      setLocalDescription(value);
    }

    // Debounce the actual form update
    const updatedFields = form.fields.map((el) => {
      const newElement = { ...el };
      if (el.id === element.id) {
        if (fieldName === "label" || fieldName === "description") {
          newElement[fieldName] = value;
        }
      }
      return newElement;
    });
    debouncedSetForm({ ...form, fields: updatedFields });
  };

  const updateShowDescription = () => {
    const updatedFields = form.fields.map((el) => {
      const newElement = { ...el };
      if (el.id === element.id) {
        newElement.showDescription = !element.showDescription;
      }
      return newElement;
    });
    setForm({ ...form, fields: updatedFields });
  };

  const updateIsRequired = () => {
    const updatedFields = form.fields.map((el) => {
      const newElement = { ...el };
      if (el.id === element.id) {
        newElement.required = !element.required;
      }
      return newElement;
    });
    setForm({ ...form, fields: updatedFields });
  };

  const duplicateElement = () => {
    const elementId = nanoid(8);
    const newFields = [...form.fields];
    const newElement = { ...element, id: elementId };

    if (selectedId) {
      newFields.splice(
        newFields.findIndex((el) => el.id === selectedId) + 1,
        0,
        newElement,
      );
    } else {
      newFields.push(newElement);
    }

    setForm({ ...form, fields: newFields });

    setNewId(elementId);
  };

  useEffect(() => {
    if (newId === selectedId) return;
    setSelectedId(newId as string);
  }, [newId, setSelectedId]);

  const deleteElement = () => {
    const updatedFields = form.fields.filter((el) => el.id !== element.id);
    setForm({ ...form, fields: updatedFields });
  };

  useEffect(() => {
    if (lastAddedOptionId && optionRefs.current[lastAddedOptionId]) {
      optionRefs.current[lastAddedOptionId]?.focus();
      setLastAddedOptionId(null);
    }
  }, [lastAddedOptionId, element.options]);

  const addOption = () => {
    const updatedElement = { ...element };
    const newOptionId = nanoid();
    if (element.options) {
      updatedElement.options = [
        ...element.options,
        { id: newOptionId, value: `Option ${element.options.length + 1}` },
      ];
    }
    setForm({
      ...form,
      fields: form.fields.map((element) => {
        if (element.id === updatedElement.id) {
          return updatedElement;
        }
        return element;
      }),
    });
    setLastAddedOptionId(newOptionId);
  };

  const updateOption = (e: React.SyntheticEvent<HTMLInputElement>) => {
    const { id, value } = e.currentTarget;
    const updatedElement = { ...element };

    const updatedOptions = updatedElement.options?.map((option) => {
      if (id === option.id) {
        return { ...option, value: value.trim() };
      }
      return option;
    });

    updatedElement.options = updatedOptions;

    setForm({
      ...form,
      fields: form.fields.map((element) => {
        if (element.id === updatedElement.id) {
          return { ...updatedElement };
        }
        return { ...element };
      }),
    });
  };

  const deleteOption = (id: string) => {
    const updatedElement = { ...element };

    updatedElement.options = element.options?.filter(
      (option) => option.id !== id,
    );

    setForm({
      ...form,
      fields: form.fields.map((element) => {
        if (element.id === updatedElement.id) {
          return updatedElement;
        }
        return element;
      }),
    });
  };

  const convertToMultipleChoice = () => {
    const updatedFields = form.fields.map((field) => {
      if (field.id === element.id) {
        return { ...field, subtype: "multiple_choice" };
      }
      return field;
    });

    setForm({ ...form, fields: updatedFields });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addOption();
    } else if (
      (e.key === "Delete" || e.key === "Backspace") &&
      e.currentTarget.value === ""
    ) {
      e.preventDefault();
      deleteOption(e.currentTarget.id);
      // Focus the previous input if available
      const currentIndex =
        element.options?.findIndex((opt) => opt.id === e.currentTarget.id) || 0;
      if (currentIndex > 0 && element.options && element.options.length > 1) {
        const prevOptionId = element?.options?.[currentIndex - 1]?.id ?? "";
        setTimeout(() => {
          optionRefs.current[prevOptionId]?.focus();
        }, 0);
      }
    }
  };

  const reorderOptions = (updatedOptions: typeof element.options) => {
    const updatedElement = { ...element, options: updatedOptions };
    setForm({
      ...form,
      fields: form.fields.map((field) => {
        if (field.id === element.id) {
          return updatedElement;
        }
        return field;
      }),
    });
  };

  return (
    <FormFieldContainer
      fieldId={element.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={setSelectedId}
    >
      {isSelected && (
        <div className="space-y-4">
          <div className="space-y-3">
            <input
              className="w-full border-none p-0 font-semibold focus:ring-0"
              autoFocus
              placeholder="Enter a question"
              name="label"
              value={localLabel || ""}
              onChange={updateLabelOrDescription}
            />

            {element?.showDescription && (
              <input
                className="w-full border-none p-0 text-gray-600 focus:ring-0"
                autoFocus
                placeholder="Enter a description"
                name="description"
                value={localDescription || ""}
                onChange={updateLabelOrDescription}
              />
            )}

            <div className="space-y-2">
              <DragDropProvider
                onDragEnd={(event) => {
                  if (element.options) {
                    const updatedOptions = move(element.options, event);
                    reorderOptions(updatedOptions);
                  }
                }}
              >
                {element.options?.map((option, index) => (
                  <SingleOptionItem
                    key={option.id}
                    option={option}
                    index={index}
                    updateOption={updateOption}
                    deleteOption={deleteOption}
                    handleKeyDown={handleKeyDown}
                    optionRefs={optionRefs}
                  />
                ))}
              </DragDropProvider>
            </div>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                leftIcon={<IconPlus size={14} />}
                onClick={addOption}
                size="sm"
              >
                Add option
              </Button>
              <Button
                variant="secondary"
                leftIcon={<IconSquareCheck size={14} />}
                onClick={convertToMultipleChoice}
                size="sm"
              >
                Convert to multiple choice
              </Button>
            </div>
          </div>
          <Divider />
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="">{<IconCircleCheck size={18} />}</div>
              <p className="text-sm font-medium">Single choice selection</p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="mr-4 flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <label className="block text-sm leading-6 font-medium">
                    Required
                  </label>
                  <Switch
                    checked={element.required}
                    onCheckedChange={updateIsRequired}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <label className="block text-sm leading-6 font-medium">
                    Show description
                  </label>
                  <Switch
                    checked={element.showDescription}
                    onCheckedChange={updateShowDescription}
                  />
                </div>
              </div>
              <ToolTip message="Duplicate">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={duplicateElement}
                >
                  <IconCopy size={16} />
                </Button>
              </ToolTip>
              <ToolTip message="Delete">
                <Button variant="outline" size="icon" onClick={deleteElement}>
                  <IconTrash size={16} className="text-red-500" />
                </Button>
              </ToolTip>
            </div>
          </div>
        </div>
      )}
      {!isSelected && (
        <div className="space-y-2">
          <RadioGroup label={element?.label} description={element?.description}>
            {element.options?.map((option) => (
              <div className="flex items-center space-x-2" key={option.id}>
                <RadioGroupItem value="default" id={option.id} disabled />
                <label className="text-sm" htmlFor={option.id}>
                  {option.value}
                </label>
              </div>
            ))}
          </RadioGroup>
        </div>
      )}
    </FormFieldContainer>
  );
}

// New component for sortable single choice option items
function SingleOptionItem({
  option,
  index,
  updateOption,
  deleteOption,
  handleKeyDown,
  optionRefs,
}: {
  option: { id: string; value: string };
  index: number;
  updateOption: (e: React.SyntheticEvent<HTMLInputElement>) => void;
  deleteOption: (id: string) => void;
  handleKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  optionRefs: React.RefObject<Record<string, HTMLInputElement | null>>;
}) {
  const { ref, isDragging } = useSortable({ id: option.id, index });
  return (
    <div
      ref={ref}
      className={`flex w-full items-center ${isDragging ? "opacity-50" : ""}`}
    >
      <div className="flex min-w-[38px] items-center gap-1">
        <span className="flex h-[20px] w-[20px] cursor-grab items-center justify-center text-gray-700">
          <IconGripVertical size={16} />
        </span>
        <input
          type="radio"
          disabled
          className="accent-color size-[18px] cursor-pointer rounded-full border-gray-300 text-primary outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2"
          style={{ marginLeft: 0 }}
        />
      </div>
      <div className="ml-3 w-full">
        <Input
          id={option.id}
          placeholder="Enter an option"
          defaultValue={option.value}
          onChange={updateOption}
          onKeyDown={handleKeyDown}
          ref={(el) => (optionRefs.current[option.id] = el) as any}
        />
      </div>
      <Button
        className="ml-3 h-9 w-10"
        variant="outline"
        onClick={() => deleteOption(option.id)}
        size="icon"
      >
        <IconX size={16} />
      </Button>
    </div>
  );
}
