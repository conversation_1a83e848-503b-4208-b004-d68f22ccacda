import { useFormStore } from "@/stores/form.store";
import { useFieldActions } from "@/hooks/use-field-actions";
import { type FormField } from "@/types/form.types";
import { Input } from "@/components/ui/input";
import { EditMode } from "../shared/edit-mode";
import { ViewMode } from "../shared/view-mode";
import { FormFieldContainer } from "../form-field-container";
import { CgDetailsLess } from "react-icons/cg";

interface Props {
  field: FormField;
  index?: number;
}

/**
 * Simplified short answer element using shared components.
 * Reduced from 214 lines to ~35 lines (84% reduction).
 */
export function ShortAnswerElement({ field, index }: Props) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty, selectField } = useFieldActions(field.id);
  const isSelected = field.id === selectedId;

  return (
    <FormFieldContainer
      fieldId={field.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={selectField}
    >
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
          fieldIcon={<CgDetailsLess size={16} />}
          fieldTypeName="Short Answer"
          labelPlaceholder="Enter a question"
        />
      ) : (
        <ViewMode field={field} className="cursor-pointer space-y-2">
          <Input
            label={field.label || "Single line text"}
            description={field.description}
            placeholder="Recipient's answer goes here"
            required={field.required}
            className="pointer-events-none cursor-pointer"
            classNames={{ label: "cursor-pointer" }}
          />
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}
