import { Textarea } from "@/components/ui/textarea";
import { useFieldActions } from "@/hooks/use-field-actions";
import { useFormStore } from "@/stores/form.store";
import { type FormField } from "@/types/form.types";
import { CgDetailsMore } from "react-icons/cg";
import { FormFieldContainer } from "../form-field-container";
import { EditMode } from "../shared/edit-mode";
import { ViewMode } from "../shared/view-mode";

interface Props {
  field: FormField;
  index?: number;
}

/**
 * Simplified long answer element using shared components.
 * Reduced from 214 lines to ~35 lines (84% reduction).
 */
export function LongAnswerElement({ field, index }: Props) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty, selectField } = useFieldActions(
    field.id,
  );
  const isSelected = field.id === selectedId;

  return (
    <FormFieldContainer
      fieldId={field.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={selectField}
    >
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
          fieldIcon={<CgDetailsMore size={16} />}
          fieldTypeName="Long Answer"
          labelPlaceholder="Enter a question"
        />
      ) : (
        <ViewMode field={field}>
          <Textarea
            label={field.label || "Long answer"}
            description={field.description}
            placeholder="Recipient's answer goes here"
            required={field.required}
            className="pointer-events-none cursor-pointer"
            classNames={{ label: "cursor-pointer" }}
          />
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}
