import { nanoid } from "@/libs/nanoid";
import type { FormField } from "@/types/form.types";
import { HeadingElement } from "./heading-element";
import { ShortAnswerElement } from "./short-answer-element";
import { EmailElement } from "./email-element";
import { LongAnswerElement } from "./long-answer-element";
import { MultipleChoiceElement } from "./multiple-choice-element";
import { SingleChoiceElement } from "./single-choice-element";
import { DropdownElement } from "./dropdown-element";
import { NumberElement } from "./number-element";
import { DateElement } from "./date-element";
import { RatingElement } from "./rating-element";
import { UploadElement } from "./upload-element";
import { PhoneNumberElement } from "./phone-number-element";
import { PageElement } from "./page-element";

/**
 * Simple element mapping system.
 * Replaces the massive renderElement function with explicit component mapping.
 */

export const ELEMENT_COMPONENTS = {
  heading: HeadingElement,
  short_answer: ShortAnswerElement,
  email: EmailElement,
  long_answer: LongAnswerElement,
  multiple_choice: MultipleChoiceElement,
  single_choice: SingleChoiceElement,
  dropdown: DropdownElement,
  number: NumberElement,
  date: DateElement,
  rating: RatingElement,
  file_upload: UploadElement,
  phone: PhoneNumberElement,
  page: PageElement,
} as const;

export const ELEMENT_DEFAULTS = {
  heading: (): FormField => ({
    id: nanoid(),
    type: "text",
    subtype: "heading",
    label: "Section heading",
    description: "",
    required: false,
    showDescription: false,
    options: [],
  }),
  short_answer: (): FormField => ({
    id: nanoid(),
    type: "text",
    subtype: "short_answer",
    label: "Untitled Question",
    description: "",
    required: false,
    showDescription: false,
    options: [],
  }),
  email: (): FormField => ({
    id: nanoid(),
    type: "email",
    subtype: "email",
    label: "Email",
    description: "",
    required: true,
    showDescription: false,
    options: [],
  }),
  long_answer: (): FormField => ({
    id: nanoid(),
    type: "text",
    subtype: "long_answer",
    label: "Untitled Question",
    description: "",
    required: false,
    showDescription: false,
    options: [],
  }),
  multiple_choice: (): FormField => ({
    id: nanoid(),
    type: "text",
    subtype: "multiple_choice",
    label: "Untitled Question",
    description: "",
    required: false,
    showDescription: false,
    options: [
      { id: nanoid(), value: "Option 1" },
      { id: nanoid(), value: "Option 2" },
      { id: nanoid(), value: "Option 3" },
    ],
  }),
  single_choice: (): FormField => ({
    id: nanoid(),
    type: "text",
    subtype: "single_choice",
    label: "Untitled Question",
    description: "",
    required: false,
    showDescription: false,
    options: [
      { id: nanoid(), value: "Option 1" },
      { id: nanoid(), value: "Option 2" },
      { id: nanoid(), value: "Option 3" },
    ],
  }),
  dropdown: (): FormField => ({
    id: nanoid(),
    type: "text",
    subtype: "dropdown",
    label: "Untitled Question",
    description: "",
    required: false,
    showDescription: false,
    options: [
      { id: nanoid(), value: "Option 1" },
      { id: nanoid(), value: "Option 2" },
      { id: nanoid(), value: "Option 3" },
    ],
  }),
  number: (): FormField => ({
    id: nanoid(),
    type: "number",
    subtype: "number",
    label: "Untitled Question",
    description: "",
    required: false,
    showDescription: false,
    options: [],
  }),
  date: (): FormField => ({
    id: nanoid(),
    type: "date",
    subtype: "date",
    label: "Untitled Question",
    description: "",
    required: false,
    showDescription: false,
    options: [],
  }),
  rating: (): FormField => ({
    id: nanoid(),
    type: "text",
    subtype: "rating",
    label: "Untitled Question",
    description: "",
    required: false,
    showDescription: false,
    ratingCount: 5,
    options: [],
  }),
  file_upload: (): FormField => ({
    id: nanoid(),
    type: "file",
    subtype: "file_upload",
    label: "Untitled Question",
    description: "",
    required: false,
    showDescription: false,
    options: [],
  }),
  phone: (): FormField => ({
    id: nanoid(),
    type: "tel",
    subtype: "phone",
    label: "Phone number",
    description: "",
    required: false,
    showDescription: false,
    options: [],
  }),
  page: (): FormField => ({
    id: nanoid(),
    type: "fb-page-break",
    subtype: "page",
    label: "Page 2",
    description: "",
    required: false,
    showDescription: false,
    options: [],
  }),
} as const;

export type ElementType = keyof typeof ELEMENT_COMPONENTS;
