import { Button } from "@/components/ui/button";
import { Divider } from "@/components/ui/divider";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ToolTip } from "@/components/ui/tooltip";
import { nanoid } from "@/libs/nanoid";
import { type FormByIdOutput, type FormField } from "@/types/form.types";
import { debounce } from "@/utils/debounce";
import { cn } from "@/utils/tailwind-helpers";
import { IconCopy, IconTrash, IconUpload } from "@tabler/icons-react";
import { useEffect, useMemo, useState } from "react";
import { FormFieldContainer } from "../form-field-container";

interface Props {
  form: FormByIdOutput;
  element: FormField;
  index?: number;
  selectedId?: string;
  setSelectedId: (fieldId: string) => void;
  setForm: (form: FormByIdOutput) => void;
}

export function UploadElement({
  element,
  index,
  selectedId,
  setSelectedId,
  form,
  setForm,
}: Props) {
  const [newId, setNewId] = useState(selectedId);
  const [localLabel, setLocalLabel] = useState(element.label);
  const [localDescription, setLocalDescription] = useState(element.description);

  const isSelected = element.id === selectedId;

  const debouncedSetForm = useMemo(
    () =>
      debounce((updatedForm: FormByIdOutput) => {
        setForm(updatedForm);
      }, 300),
    [setForm],
  );

  const updateLabelOrDescription = (
    e: React.SyntheticEvent<HTMLInputElement>,
  ) => {
    const { value, name } = e.currentTarget;
    const fieldName = name as keyof FormField;

    // Update local state immediately for responsive UI
    if (fieldName === "label") {
      setLocalLabel(value);
    } else if (fieldName === "description") {
      setLocalDescription(value);
    }

    // Debounce the actual form update
    const updatedFields = form.fields.map((el) => {
      const newElement = { ...el };
      if (el.id === element.id) {
        if (fieldName === "label" || fieldName === "description") {
          newElement[fieldName] = value;
        }
      }
      return newElement;
    });
    debouncedSetForm({ ...form, fields: updatedFields });
  };

  const updateShowDescription = () => {
    const updatedFields = form.fields.map((el) => {
      const newElement = { ...el };
      if (el.id === element.id) {
        newElement.showDescription = !element.showDescription;
      }
      return newElement;
    });
    setForm({ ...form, fields: updatedFields });
  };

  const updateIsRequired = () => {
    const updatedFields = form.fields.map((el) => {
      const newElement = { ...el };
      if (el.id === element.id) {
        newElement.required = !element.required;
      }
      return newElement;
    });
    setForm({ ...form, fields: updatedFields });
  };

  const duplicateElement = () => {
    const elementId = nanoid(8);
    const newFields = [...form.fields];
    const newElement = { ...element, id: elementId };

    if (selectedId) {
      newFields.splice(
        newFields.findIndex((el) => el.id === selectedId) + 1,
        0,
        newElement,
      );
    } else {
      newFields.push(newElement);
    }

    setForm({ ...form, fields: newFields });

    setNewId(elementId);
  };

  useEffect(() => {
    setSelectedId(newId as string);
  }, [newId, setSelectedId]);

  const deleteElement = () => {
    const updatedFields = form.fields.filter((el) => el.id !== element.id);
    setForm({ ...form, fields: updatedFields });
  };

  return (
    <FormFieldContainer
      fieldId={element.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={setSelectedId}
    >
      {isSelected && (
        <div className="space-y-4">
          <div className="space-y-3">
            <input
              className="w-full border-none p-0 font-semibold focus:ring-0"
              autoFocus
              placeholder="Enter a question"
              name="label"
              value={localLabel || ""}
              onChange={updateLabelOrDescription}
            />

            {element?.showDescription && (
              <input
                className="w-full border-none p-0 text-gray-600 focus:ring-0"
                autoFocus
                placeholder="Enter a description"
                name="description"
                value={localDescription || ""}
                onChange={updateLabelOrDescription}
              />
            )}
            <div
              className={cn(
                "flex h-[150px] w-full cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-gray-900/25 py-10",
              )}
            >
              <div>
                <IconUpload className="h-6 w-6 text-gray-700" />
              </div>
              <p className="mt-4 text-base">
                Click to choose a file or drag image here
              </p>
              <p className="mt-4 text-sm text-gray-600">Size limit: 10MB</p>
            </div>
          </div>
          <Divider />
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="">{<IconUpload size="16px" />}</div>
              <p className="text-sm font-medium">File upload</p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="mr-4 flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <label className="block text-sm font-medium leading-6">
                    Required
                  </label>
                  <Switch
                    checked={element.required}
                    onCheckedChange={updateIsRequired}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <label className="block text-sm font-medium leading-6">
                    Show description
                  </label>
                  <Switch
                    checked={element.showDescription}
                    onCheckedChange={updateShowDescription}
                  />
                </div>
              </div>
              <ToolTip message="Duplicate">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={duplicateElement}
                >
                  <IconCopy size={16} />
                </Button>
              </ToolTip>
              <ToolTip message="Delete">
                <Button variant="outline" size="icon" onClick={deleteElement}>
                  <IconTrash size={16} className="text-red-500" />
                </Button>
              </ToolTip>
            </div>
          </div>
        </div>
      )}
      {!isSelected && (
        <div className="cursor-pointer space-y-2">
          <Label className="cursor-pointer">{element?.label}</Label>
          {element?.description && (
            <p className="mb-1 block text-sm text-gray-600">
              {element?.description}
            </p>
          )}
          <div
            className={cn(
              "flex h-[150px] w-full cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-gray-900/25 py-10",
            )}
          >
            <div>
              <IconUpload className="h-6 w-6 text-gray-700" />
            </div>
            <p className="mt-4 text-base">
              Click to choose a file or drag image here
            </p>
            <p className="mt-4 text-sm text-gray-600">Size limit: 10MB</p>
          </div>
        </div>
      )}
    </FormFieldContainer>
  );
}
