import { Rating } from "@/components/ui/Rating";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Divider } from "@/components/ui/divider";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Switch } from "@/components/ui/switch";
import { ToolTip } from "@/components/ui/tooltip";
import { nanoid } from "@/libs/nanoid";
import { type FormByIdOutput, type FormField } from "@/types/form.types";
import { debounce } from "@/utils/debounce";
import {
  IconCopy,
  IconSettings,
  IconStar,
  IconTrash,
} from "@tabler/icons-react";
import { useEffect, useMemo, useState } from "react";
import { FormFieldContainer } from "../form-field-container";

interface Props {
  form: FormByIdOutput;
  element: FormField;
  index?: number;
  selectedId?: string;
  setSelectedId: (fieldId: string) => void;
  setForm: (form: FormByIdOutput) => void;
}

export function RatingElement({
  element,
  index,
  selectedId,
  setSelectedId,
  form,
  setForm,
}: Props) {
  const [newId, setNewId] = useState(selectedId);
  const [localLabel, setLocalLabel] = useState(element.label);
  const [localDescription, setLocalDescription] = useState(element.description);

  const isSelected = element.id === selectedId;

  const debouncedSetForm = useMemo(
    () =>
      debounce((updatedForm: FormByIdOutput) => {
        setForm(updatedForm);
      }, 300),
    [setForm],
  );

  const updateLabelOrDescription = (
    e: React.SyntheticEvent<HTMLInputElement>,
  ) => {
    const { value, name } = e.currentTarget;
    const fieldName = name as keyof FormField;

    // Update local state immediately for responsive UI
    if (fieldName === "label") {
      setLocalLabel(value);
    } else if (fieldName === "description") {
      setLocalDescription(value);
    }

    // Debounce the actual form update
    const updatedFields = form.fields.map((el) => {
      const newElement = { ...el };
      if (el.id === element.id) {
        if (fieldName === "label" || fieldName === "description") {
          newElement[fieldName] = value;
        }
      }
      return newElement;
    });
    debouncedSetForm({ ...form, fields: updatedFields });
  };

  const updateShowDescription = () => {
    const updatedFields = form.fields.map((el) => {
      const newElement = { ...el };
      if (el.id === element.id) {
        newElement.showDescription = !element.showDescription;
      }
      return newElement;
    });
    setForm({ ...form, fields: updatedFields });
  };

  const updateIsRequired = () => {
    const updatedFields = form.fields.map((el) => {
      const newElement = { ...el };
      if (el.id === element.id) {
        newElement.required = !element.required;
      }
      return newElement;
    });
    setForm({ ...form, fields: updatedFields });
  };

  const updateRating = (e: React.SyntheticEvent<HTMLInputElement>) => {
    const { value } = e.currentTarget;
    const updatedFields = form.fields.map((el) => {
      const newElement = { ...el };
      if (el.id === element.id) {
        newElement.ratingCount = Number(value);
      }
      return newElement;
    });
    setForm({ ...form, fields: updatedFields });
  };

  const duplicateElement = () => {
    const elementId = nanoid(8);
    const newFields = [...form.fields];
    const newElement = { ...element, id: elementId };

    if (selectedId) {
      newFields.splice(
        newFields.findIndex((el) => el.id === selectedId) + 1,
        0,
        newElement,
      );
    } else {
      newFields.push(newElement);
    }

    setForm({ ...form, fields: newFields });

    setNewId(elementId);
  };

  useEffect(() => {
    if (newId === selectedId) return;
    setSelectedId(newId as string);
  }, [newId]);

  const deleteElement = () => {
    const updatedFields = form.fields.filter((el) => el.id !== element.id);
    setForm({ ...form, fields: updatedFields });
  };

  return (
    <FormFieldContainer
      fieldId={element.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={setSelectedId}
    >
      {isSelected && (
        <div className="space-y-4">
          <div className="space-y-3">
            <input
              className="w-full border-none p-0 font-semibold focus:ring-0"
              autoFocus
              placeholder="Enter a question"
              name="label"
              value={localLabel || ""}
              onChange={updateLabelOrDescription}
            />

            {element?.showDescription && (
              <input
                className="w-full border-none p-0 text-gray-600 focus:ring-0"
                autoFocus
                placeholder="Enter a description"
                name="description"
                value={localDescription || ""}
                onChange={updateLabelOrDescription}
              />
            )}
            <div>
              <Rating ratingCount={element.ratingCount} />
            </div>
          </div>
          <Divider />
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="">{<IconStar size="16px" />}</div>
              <p className="text-sm font-medium">Rating</p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="mr-4 flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <label className="block text-sm leading-6 font-medium">
                    Required
                  </label>
                  <Switch
                    checked={element.required}
                    onCheckedChange={updateIsRequired}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <label className="block text-sm leading-6 font-medium">
                    Show description
                  </label>
                  <Switch
                    checked={element.showDescription}
                    onCheckedChange={updateShowDescription}
                  />
                </div>
              </div>
              <ToolTip message="Configuration">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="icon">
                      <IconSettings size={16} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent>
                    <Input
                      label="Stars"
                      type="number"
                      max={10}
                      defaultValue={element.ratingCount}
                      onChange={updateRating}
                    />
                  </PopoverContent>
                </Popover>
              </ToolTip>
              <ToolTip message="Duplicate">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={duplicateElement}
                >
                  <IconCopy size={16} />
                </Button>
              </ToolTip>
              <ToolTip message="Delete">
                <Button variant="outline" size="icon" onClick={deleteElement}>
                  <IconTrash size={16} className="text-red-500" />
                </Button>
              </ToolTip>
            </div>
          </div>
        </div>
      )}
      {!isSelected && (
        <div className="cursor-pointer space-y-2">
          <Label className="cursor-pointer">{element?.label}</Label>
          {element?.description && (
            <p className="mb-1 block text-sm text-gray-600">
              {element?.description}
            </p>
          )}
          <Rating ratingCount={element.ratingCount} />
        </div>
      )}
    </FormFieldContainer>
  );
}
