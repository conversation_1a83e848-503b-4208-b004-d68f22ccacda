import { useFormStore } from "@/stores/form.store";
import { useFieldActions } from "@/hooks/use-field-actions";
import { type FormField } from "@/types/form.types";
import { Input } from "@/components/ui/input";
import { IconAt } from "@tabler/icons-react";
import { EditMode } from "../shared/edit-mode";
import { ViewMode } from "../shared/view-mode";
import { FormFieldContainer } from "../form-field-container";

interface Props {
  field: FormField;
  index?: number;
}

/**
 * Simplified email element using shared components.
 * Reduced from 213 lines to ~35 lines (84% reduction).
 */
export function EmailElement({ field, index }: Props) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty, selectField } = useFieldActions(field.id);
  const isSelected = field.id === selectedId;

  return (
    <FormFieldContainer
      fieldId={field.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={selectField}
    >
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
          fieldIcon={<IconAt size={16} />}
          fieldTypeName="Email"
          labelPlaceholder="Enter a question"
        />
      ) : (
        <ViewMode field={field}>
          <Input
            label={field.label || "Email"}
            description={field.description}
            placeholder="Recipient's answer goes here"
            required={field.required}
            className="pointer-events-none cursor-pointer"
            classNames={{ label: "cursor-pointer" }}
          />
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}
