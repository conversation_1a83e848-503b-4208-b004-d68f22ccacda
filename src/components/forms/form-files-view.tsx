"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { EmptyState } from "@/components/ui/empty-state";
import { Loader } from "@/components/ui/loader";
import { Skeleton } from "@/components/ui/skeleton";
import { useInfiniteFormFiles } from "@/queries/form.queries";
import { type File, type InfiniteFilesData } from "@/types/form.types";
import {
  type DocumentMimeType,
  type ImageMimeType,
  type SpreadsheetMimeType,
  DOCUMENT_MIME_TYPE,
  IMAGE_MIME_TYPE,
  SPREADSHEET_MIME_TYPE,
} from "@/utils/constants";
import { formatDate } from "@/utils/format-date";
import { formatFileSize } from "@/utils/format-file-size";
import {
  IconClock,
  IconDownload,
  IconFile,
  IconFileSpreadsheet,
  IconFileText,
  IconPhoto,
} from "@tabler/icons-react";
import Link from "next/link";
import { isEmpty } from "radash";
import { useEffect, useMemo } from "react";
import { useInView } from "react-intersection-observer";

const loadingItems = Array.from({ length: 6 });

export const formatFiles = (files: InfiniteFilesData) => {
  let data: File[] = [];
  if (files) {
    for (const page of files.pages) {
      data = [...data, ...page.data];
    }
    return data.map((file) => ({
      ...file,
    }));
  }
  return data;
};

interface Props {
  formId: string;
}

export function FormFilesView({ formId }: Props) {
  const { ref, inView } = useInView();

  const files = useInfiniteFormFiles(formId);

  useEffect(() => {
    if (files.hasNextPage && inView) {
      files.fetchNextPage();
    }
  }, [inView, files]);

  const data = useMemo(() => formatFiles(files.data), [files.data]);

  async function downloadFile(fileUrl: string, fileName: string) {
    const response = await fetch(fileUrl);
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  function getFileIcon(fileType: string) {
    if (DOCUMENT_MIME_TYPE.includes(fileType as DocumentMimeType)) {
      return <IconFileText size={24} />;
    }
    if (IMAGE_MIME_TYPE.includes(fileType as ImageMimeType)) {
      return <IconPhoto size={24} />;
    }
    if (SPREADSHEET_MIME_TYPE.includes(fileType as SpreadsheetMimeType)) {
      return <IconFileSpreadsheet size={24} />;
    }
    return <IconFile size={24} />;
  }

  return (
    <div>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold">Files</h3>
          <p className="mt-2 max-w-lg text-gray-600">
            Manage the files associated with this form. You can view and
            download all your form files here.
          </p>
        </div>
      </div>

      <div className="mt-6">
        {files.isLoading && (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {loadingItems.map((_, index) => (
              <Skeleton key={index} className="h-[200px] w-full rounded-xl" />
            ))}
          </div>
        )}

        {!files.isLoading && !isEmpty(data) && (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {data.map((file) => (
              <Card key={file.id} className="group p-6 ">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="rounded-xl bg-white p-4 ring-1 ring-gray-200">
                      {getFileIcon(file.type)}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Link
                      href={file.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block"
                    >
                      <h4 className="line-clamp-2 truncate font-semibold text-gray-900">
                        {file.name}
                      </h4>
                    </Link>

                    <div className="space-y-1">
                      <p className="text-sm font-medium text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <IconClock size={16} />
                    <span>{formatDate(file.createdAt, "MMM DD, YYYY")}</span>
                  </div>

                  <Button
                    variant="outline"
                    leftIcon={<IconDownload size={16} />}
                    onClick={() => downloadFile(file.url, file.name)}
                    className="w-full"
                  >
                    Download
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        )}

        {!files.isLoading && isEmpty(data) && (
          <div className="mt-4 rounded-xl border border-gray-300 px-12 py-24 text-center md:p-44">
            <EmptyState
              title="No files found"
              subtitle="There are no files associated with this form yet."
              icon={<IconFile size={40} />}
            />
          </div>
        )}
      </div>

      <div ref={ref} className="flex items-center justify-center">
        {files.isFetchingNextPage && <Loader className="mt-5" />}
      </div>
    </div>
  );
}
