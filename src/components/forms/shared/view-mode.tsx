import { type FormField } from "@/types/form.types";

interface ViewModeProps {
  field: FormField;
  children?: React.ReactNode;
  showLabel?: boolean;
  showDescription?: boolean;
  className?: string;
}

/**
 * Shared viewing mode component for form field elements.
 * Handles the common display pattern when fields are not selected.
 */
export function ViewMode({
  field,
  children,
  showLabel = true,
  showDescription = true,
  className = "space-y-2",
}: ViewModeProps) {
  return (
    <div className={className}>
      {/* Standard label and description display */}
      {(showLabel || showDescription) && (
        <div className="space-y-2">
          {showLabel && field.label && (
            <label className="block text-sm font-medium leading-6">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          {showDescription && field.showDescription && field.description && (
            <p className="block text-sm text-gray-500">
              {field.description}
            </p>
          )}
        </div>
      )}

      {/* Field-specific content */}
      {children}
    </div>
  );
}
