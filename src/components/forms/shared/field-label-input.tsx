import { cn } from "@/utils/tailwind-helpers";
import { forwardRef } from "react";

interface FieldLabelInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  className?: string;
}

/**
 * Shared input component for field labels across all form elements.
 * Provides consistent styling and behavior for label editing.
 */
export const FieldLabelInput = forwardRef<HTMLInputElement, FieldLabelInputProps>(
  ({ value, onChange, placeholder = "Enter a question", autoFocus = true, className }, ref) => {
    return (
      <input
        ref={ref}
        className={cn(
          "w-full border-none p-0 font-semibold focus:ring-0",
          className
        )}
        autoFocus={autoFocus}
        placeholder={placeholder}
        value={value || ""}
        onChange={(e) => onChange(e.target.value)}
      />
    );
  }
);

FieldLabelInput.displayName = "FieldLabelInput";
