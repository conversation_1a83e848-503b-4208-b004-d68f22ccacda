import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { ToolTip } from "@/components/ui/tooltip";
import { IconCopy, IconTrash } from "@tabler/icons-react";

interface FieldActionsProps {
  onDuplicate: () => void;
  onDelete: () => void;
  showDescription?: boolean;
  onToggleDescription?: () => void;
  required?: boolean;
  onToggleRequired?: () => void;
  fieldIcon?: React.ReactNode;
  fieldTypeName?: string;
  children?: React.ReactNode;
}

/**
 * Shared action buttons and toggles for form field elements.
 * Provides consistent duplicate/delete actions and common toggles.
 */
export function FieldActions({
  onDuplicate,
  onDelete,
  showDescription,
  onToggleDescription,
  required,
  onToggleRequired,
  fieldIcon,
  fieldTypeName,
  children,
}: FieldActionsProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        {fieldIcon && (
          <>
            <div>{fieldIcon}</div>
            {fieldTypeName && <p className="text-sm font-medium">{fieldTypeName}</p>}
          </>
        )}
      </div>
      
      <div className="flex items-center space-x-2">
        <div className="mr-4 flex items-center space-x-4">
          {/* Required toggle */}
          {onToggleRequired !== undefined && (
            <div className="flex items-center space-x-2">
              <label className="block text-sm font-medium leading-6">
                Required
              </label>
              <Switch
                checked={required}
                onCheckedChange={onToggleRequired}
              />
            </div>
          )}
          
          {/* Show description toggle */}
          {onToggleDescription !== undefined && (
            <div className="flex items-center space-x-2">
              <label className="block text-sm font-medium leading-6">
                Show description
              </label>
              <Switch
                checked={showDescription}
                onCheckedChange={onToggleDescription}
              />
            </div>
          )}
          
          {/* Custom children for field-specific controls */}
          {children}
        </div>
        
        {/* Action buttons */}
        <ToolTip message="Duplicate">
          <Button
            variant="outline"
            size="icon"
            onClick={onDuplicate}
          >
            <IconCopy size={16} />
          </Button>
        </ToolTip>
        
        <ToolTip message="Delete">
          <Button 
            variant="outline" 
            size="icon" 
            onClick={onDelete}
          >
            <IconTrash size={16} className="text-red-500" />
          </Button>
        </ToolTip>
      </div>
    </div>
  );
}
