"use client";

import { Card } from "@/components/ui/card";
import { cn } from "@/utils/tailwind-helpers";

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ElementType;
  iconColor?: string;
  iconBgColor?: string;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    description?: string;
  };
  className?: string;
}

export function StatCard({
  title,
  value,
  icon: Icon,
  iconColor = "text-blue-600",
  iconBgColor = "bg-blue-50",
  description,
  trend,
  className,
}: StatCardProps) {
  return (
    <Card className={cn("relative overflow-hidden bg-white p-6", className)}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="mb-2 text-sm font-semibold uppercase tracking-wide text-gray-600">
            {title}
          </p>
          <p className="text-3xl font-bold tracking-tight text-gray-900">
            {value}
          </p>
          {description && (
            <p className="mt-1 text-sm text-gray-500">{description}</p>
          )}
          {trend && (
            <div className="mt-3 flex items-center">
              <span
                className={cn(
                  "inline-flex items-center rounded-full px-2.5 py-1 text-xs font-medium",
                  trend.isPositive
                    ? "bg-green-100 text-green-700"
                    : "bg-red-100 text-red-700",
                )}
              >
                {trend.isPositive ? "+" : ""}
                {trend.value}%
              </span>
              <span className="ml-2 text-xs text-gray-500">
                {trend.description ?? "vs last period"}
              </span>
            </div>
          )}
        </div>
        <div
          className={cn(
            "flex h-12 w-12 shrink-0 items-center justify-center rounded-xl",
            iconBgColor,
          )}
        >
          <Icon size={24} className={iconColor} />
        </div>
      </div>

      {/* Subtle gradient overlay */}
      <div className="pointer-events-none absolute inset-0 bg-linear-to-br from-transparent via-transparent to-gray-50/30" />
    </Card>
  );
}
