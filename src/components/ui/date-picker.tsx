"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { formatDate } from "@/utils/format-date";
import { cn } from "@/utils/tailwind-helpers";
import { CalendarIcon } from "lucide-react";
import * as React from "react";

export interface DatePickerProps {
  id?: string;
  label?: string;
  description?: string;
  error?: boolean;
  errorMessage?: string;
  placeholder?: string;
  value?: Date | null | undefined;
  onSelect?: (date: Date | undefined) => void;
  disabled?: boolean;
  className?: string;
  classNames?: {
    label?: string;
    button?: string;
    popover?: string;
    day_selected?: string;
    description?: string;
  };
  style?: React.CSSProperties;
  styles?: {
    label?: React.CSSProperties;
    day?: React.CSSProperties;
    description?: React.CSSProperties;
  };
}

export function DatePicker({
  id,
  label,
  description,
  error,
  errorMessage,
  placeholder = "Select date",
  value,
  onSelect,
  disabled,
  className,
  classNames,
  styles,
  style,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false);
  const [selectedDate, setSelectedDate] = React.useState<
    Date | null | undefined
  >(value);

  const handleSelect = (date: Date | undefined) => {
    if (onSelect) {
      onSelect(date);
    }
    setSelectedDate(date);
  };

  return (
    <div className={className}>
      {label && (
        <Label
          htmlFor={id}
          className={cn(classNames?.label)}
          style={styles?.label}
        >
          {label}
        </Label>
      )}
      {description && (
        <p className="block text-sm text-gray-500" style={styles?.description}>
          {description}
        </p>
      )}
      <div className={cn("relative", label && "mt-[5px]")}>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              id={id}
              disabled={disabled}
              className={cn(
                "w-full justify-between font-normal text-primary/70",
                error && "ring-red-500 focus:ring-red-500",
                classNames?.button,
                selectedDate && "text-primary",
              )}
              style={style}
              rightIcon={<CalendarIcon size={16} />}
            >
              {selectedDate ? formatDate(selectedDate) : placeholder}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className={cn("w-auto overflow-hidden p-0", classNames?.popover)}
            align="start"
          >
            <Calendar
              mode="single"
              selected={selectedDate ?? undefined}
              captionLayout="dropdown"
              onSelect={handleSelect}
              disabled={disabled}
              classNames={{
                day_button: cn(classNames?.day_selected),
                selected: cn(
                  "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground rounded-md",
                  classNames?.day_selected,
                ),
                today: cn(
                  "bg-accent text-accent-foreground rounded-md",
                  classNames?.day_selected,
                ),
              }}
              styles={{
                day: styles?.day,
              }}
            />
          </PopoverContent>
        </Popover>

        {error && errorMessage && (
          <p className="mt-1 text-sm text-red-500">{errorMessage}</p>
        )}
      </div>
    </div>
  );
}
