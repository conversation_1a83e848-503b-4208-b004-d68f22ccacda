import { cn } from "@/utils/tailwind-helpers";

interface AlertProps {
  message?: string;
  className?: string;
}

export function AlertError({ message, className }: AlertProps) {
  if (!message) return null;

  return (
    <div
      className={cn(
        "flex items-center space-x-3 rounded-lg bg-destructive/15 p-3 text-sm text-destructive",
        className,
      )}
    >
      <p>{message}</p>
    </div>
  );
}
