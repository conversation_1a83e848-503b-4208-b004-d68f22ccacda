import { cn } from "@/utils/tailwind-helpers";

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  animatePulse?: boolean;
}

export function Skeleton({ className, animatePulse = true, ...props }: Props) {
  // If a custom border radius is provided, don't apply the default rounded-xl
  const hasCustomRadius = className?.includes("rounded-");
  return (
    <div
      className={cn(
        hasCustomRadius ? "bg-primary/10" : "rounded-xl bg-primary/10",
        animatePulse && "animate-pulse",
        className,
      )}
      {...props}
    />
  );
}
