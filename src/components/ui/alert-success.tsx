import { cn } from "@/utils/tailwind-helpers";

interface AlertProps {
  message?: string;
  className?: string;
}

export function AlertSuccess({ message, className }: AlertProps) {
  if (!message) return null;

  return (
    <div
      className={cn(
        "flex items-center space-x-3 rounded-lg bg-emerald-500/15 p-3 text-sm text-emerald-500",
        className,
      )}
    >
      <p>{message}</p>
    </div>
  );
}
