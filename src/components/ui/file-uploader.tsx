import { env } from "@/env";
import { nanoid } from "@/libs/nanoid";
import { useFileUploadUrlMutation } from "@/queries/storage.queries";
import { type FormFile } from "@/types/form.types";
import { IMAGE_MIME_TYPE } from "@/types/utility.types";
import { compressImage } from "@/utils/compress-image";
import {
  type ImageMimeType,
  MAX_FILE_COUNT,
  MAX_FILE_SIZE,
  MIME_TYPES,
} from "@/utils/constants";
import { formatFileSize } from "@/utils/format-file-size";
import { cn } from "@/utils/tailwind-helpers";
import {
  IconCheck,
  IconExclamationCircle,
  IconUpload,
  IconX,
} from "@tabler/icons-react";
import { useState } from "react";
import { type FileRejection, useDropzone } from "react-dropzone";
import { Button } from "./button";
import { Loader } from "./loader";

interface Props {
  className?: string;
  style?: React.CSSProperties;
  onUploadComplete?: (file: FormFile | FormFile[]) => void;
  orgId?: string;
  formId?: string;
  multiple?: boolean;
}

export function FileUploader({
  className,
  style,
  onUploadComplete,
  orgId,
  formId,
  multiple = false,
}: Props) {
  const [uploadError, setUploadError] = useState("");
  const [isUploadLoading, setUploadLoading] = useState(false);
  const [_fileNames, setFileNames] = useState<string[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<FormFile[]>([]);

  const uploadUrlMutation = useFileUploadUrlMutation();
  // const deleteFileMutation = useFileDeleteMutation();

  const { getRootProps, getInputProps } = useDropzone({
    disabled: isUploadLoading,
    maxSize: MAX_FILE_SIZE,
    maxFiles: multiple ? MAX_FILE_COUNT : 1,
    multiple,
    accept: Object.values(MIME_TYPES).reduce(
      (r, key) => ({ ...r, [key]: [] }),
      {},
    ),
    onDropAccepted: handleFileUpload,
    onDropRejected: handleFileRejectedErrors,
  });

  async function onFileUpload(file: File) {
    let compressedFile = file;

    if (IMAGE_MIME_TYPE.includes(compressedFile.type as ImageMimeType)) {
      compressedFile = await compressImage(file);
    }

    const fileKey = `uploads/${orgId}/forms/${formId}/${nanoid()}-${compressedFile.name}`;
    const { uploadUrl } = await uploadUrlMutation.mutateAsync({
      fileKey,
    });
    if (uploadUrl) {
      await fetch(uploadUrl, {
        method: "PUT",
        headers: { "Content-Type": compressedFile.type },
        body: compressedFile,
      });
      return {
        name: compressedFile.name,
        type: compressedFile.type,
        url: `${env.NEXT_PUBLIC_AWS_PUBLIC_BUCKET_URL}/${fileKey}`,
        size: compressedFile.size,
      } as FormFile;
    }
  }

  function handleFileRejectedErrors(files: FileRejection[]) {
    const error = files[0]?.errors[0];
    switch (error?.code) {
      case "too-many-files":
        setUploadError("You can only upload one file at a time");
        break;
      case "file-too-large":
        setUploadError(
          `File size should not exceed ${formatFileSize(MAX_FILE_SIZE)}`,
        );
        break;
      case "file-invalid-type":
        setUploadError(error.message);
        break;
    }
  }

  async function handleFileUpload(files: File[]) {
    try {
      setUploadLoading(true);
      const uploaded: FormFile[] = [];
      for (const file of files) {
        const fileObj = await onFileUpload(file);
        if (fileObj) {
          uploaded.push(fileObj);
        }
      }
      setFileNames((prev) => [...prev, ...uploaded.map((file) => file.name)]);
      setUploadedFiles((prev) => [...prev, ...uploaded]);
      if (uploaded.length > 0) {
        if (multiple) {
          onUploadComplete?.(uploaded);
        } else {
          onUploadComplete?.(uploaded[0] as FormFile);
        }
      }
      setUploadLoading(false);
    } catch (error) {
      console.log(error);
      setUploadLoading(false);
    }
  }

  return (
    <>
      {uploadError && (
        <div className="relative mb-4 rounded-md bg-red-50 p-4">
          <div className="flex items-start justify-between">
            <div className="flex">
              <div className="shrink-0">
                <IconExclamationCircle
                  className="h-5 w-5 text-red-400"
                  aria-hidden="true"
                />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{uploadError}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="absolute top-1 right-1">
            <Button
              size="icon"
              variant="ghost"
              className="h-7 w-7 text-red-500 hover:bg-red-100 hover:text-red-500"
              onClick={() => setUploadError("")}
            >
              <IconX size={16} />
            </Button>
          </div>
        </div>
      )}
      <div
        {...getRootProps({
          className: cn(
            "w-full min-h-[150px] flex flex-col justify-center items-center rounded-lg border border-dashed border-gray-900/25 py-10 hover:bg-gray-50 cursor-pointer bg-white shadow-xs",
            isUploadLoading && "bg-gray-50 cursor-default",
            className,
          ),
        })}
        style={style}
      >
        {isUploadLoading && <Loader />}
        {!isUploadLoading && uploadedFiles.length === 0 && (
          <>
            <input {...getInputProps()} />
            <div>
              <IconUpload className="h-6 w-6 text-gray-700" />
            </div>
            <p className="mt-4">
              Click to choose {multiple ? "files" : "a file"} or drag{" "}
              {multiple ? "files" : "an image"} here
            </p>
            <p className="mt-4 text-sm text-gray-600">
              Size limit: {formatFileSize(MAX_FILE_SIZE)}
            </p>
          </>
        )}
        {!isUploadLoading && uploadedFiles.length > 0 && (
          <>
            <input {...getInputProps()} />
            <div className="text-center">
              <p className="text-sm font-medium text-gray-900">
                {uploadedFiles.length === 1
                  ? "File uploaded successfully"
                  : `${uploadedFiles.length} files uploaded successfully`}
              </p>
              {uploadedFiles.length <= 3 && (
                <div className="mt-1 space-y-0.5">
                  {uploadedFiles.map((file) => (
                    <div
                      key={file.name}
                      className="flex items-center justify-center space-x-1 text-xs text-gray-600"
                    >
                      <span className="max-w-32 truncate">{file.name}</span>
                      <IconCheck className="size-4 text-green-600" />
                    </div>
                  ))}
                </div>
              )}
              {/* <p className="mt-1 text-xs text-gray-500">
                Click to upload {multiple ? "more files" : "a different file"}
              </p> */}
            </div>
          </>
        )}
      </div>
    </>
  );
}
