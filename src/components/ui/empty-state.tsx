interface Props {
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  actionButton?: React.ReactNode;
}

export function EmptyState({ title, subtitle, icon, actionButton }: Props) {
  return (
    <div className="mx-auto flex max-w-md flex-col items-center justify-center px-4 py-12 text-center">
      {icon && (
        <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
          <div className="text-xl text-gray-500">{icon}</div>
        </div>
      )}
      {title && (
        <h3 className="mb-3 text-lg font-semibold text-gray-900">{title}</h3>
      )}
      {subtitle && (
        <p className="mb-8 max-w-sm text-sm leading-relaxed text-gray-500">
          {subtitle}
        </p>
      )}
      {actionButton && <div className="mt-2">{actionButton}</div>}
    </div>
  );
}
