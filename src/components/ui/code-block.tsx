import { But<PERSON> } from "@/components/ui/button";
import { useClipboard } from "@/hooks/use-clipboard";
import { IconCheck, IconCopy } from "@tabler/icons-react";
import { clsx } from "clsx";
import { Terminal } from "lucide-react";
import { Highlight, themes } from "prism-react-renderer";

interface CodeBlockProps {
  code: string;
  language?: string;
  filename?: string;
  showLineNumbers?: boolean;
  showHeader?: boolean;
}

export function CodeBlock({
  code,
  language = "typescript",
  filename,
  showLineNumbers = true,
  showHeader = true,
}: CodeBlockProps) {
  const { copy, copied } = useClipboard();

  function handleCopy() {
    copy(code);
  }

  return (
    <div className="relative overflow-hidden rounded-xl border">
      {showHeader && (
        <div className="flex items-center justify-between border-b px-4 py-2">
          <div className="flex items-center gap-2">
            <Terminal className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-600">
              {filename || language}
            </span>
          </div>
          <Button
            className="h-8 w-8 text-gray-500 hover:text-gray-900"
            variant="ghost"
            onClick={handleCopy}
          >
            {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
          </Button>
        </div>
      )}
      <Highlight theme={themes.oneLight} code={code} language={language}>
        {({ className, style, tokens, getLineProps, getTokenProps }) => (
          <pre
            className={clsx(
              className,
              "overflow-x-auto p-4",
              "font-mono text-sm",
            )}
            style={style}
          >
            {tokens.map((line, i) => (
              <div key={i} {...getLineProps({ line })} className="table-row">
                {showLineNumbers && (
                  <span className="table-cell select-none pr-4 text-right text-gray-600 ">
                    {i + 1}
                  </span>
                )}
                <span className="table-cell">
                  {line.map((token, key) => (
                    <span key={key} {...getTokenProps({ token })} />
                  ))}
                </span>
              </div>
            ))}
          </pre>
        )}
      </Highlight>
    </div>
  );
}
