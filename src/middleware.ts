import {
  apiAuthPrefix,
  apiIntegrationsPrefix,
  apiJobsPrefix,
  authRoutes,
  externalApiPrefix,
  invitePrefix,
  publicRoutes,
} from "@/routes";
import { DEFAULT_LOGIN_REDIRECT } from "@/utils/constants";
import { getSessionCookie } from "better-auth/cookies";
import { type NextRequest, NextResponse } from "next/server";

// Authentication and route protection middleware
export default async function authMiddleware(request: NextRequest) {
  try {
    const { pathname } = request.nextUrl;

    // Skip middleware for API/auth/integration/job/invite/external routes
    if (
      pathname.startsWith(apiAuthPrefix) ||
      pathname.startsWith(apiJobsPrefix) ||
      pathname.startsWith(apiIntegrationsPrefix) ||
      pathname.startsWith(externalApiPrefix) ||
      pathname.startsWith(invitePrefix)
    ) {
      return;
    }

    // Check for session cookie to determine login state
    const sessionCookie = getSessionCookie(request);
    const isLoggedIn = !!sessionCookie;

    // Route type checks
    const isFormsRoute = pathname.includes("/forms");
    const isPublicRoute = publicRoutes.includes(pathname) || isFormsRoute;
    const isAuthRoute = authRoutes.includes(pathname);

    // Log middleware activity in development
    if (process.env.NODE_ENV === "development") {
      console.log(`Middleware: ${pathname}, logged in: ${isLoggedIn}`);
    }

    // Auth route logic: allow reset-password, redirect logged-in users
    if (isAuthRoute) {
      if (pathname.includes("/auth/reset-password")) {
        return;
      }
      if (isLoggedIn) {
        return NextResponse.redirect(
          new URL(DEFAULT_LOGIN_REDIRECT, request.nextUrl),
        );
      }
      return;
    }

    // Redirect unauthenticated users from protected routes to login
    if (!isLoggedIn && !isPublicRoute) {
      return NextResponse.redirect(new URL("/auth/login", request.nextUrl));
    }

    // Allow request to proceed
    return NextResponse.next();
  } catch (error) {
    console.error("Middleware error:", error);
    // On error, fail securely by redirecting to login
    return NextResponse.redirect(new URL("/auth/login", request.nextUrl));
  }
}

// Exclude static files, images, favicon, and certain API routes from middleware
export const config = {
  matcher: [
    // Match all request paths except for:
    // - api/auth (auth API routes)
    // - _next/static (static files)
    // - _next/image (image optimization files)
    // - favicon.ico (favicon file)
    // - public folder files (svg, png, jpg, jpeg, gif, webp)
    "/((?!api/auth|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
