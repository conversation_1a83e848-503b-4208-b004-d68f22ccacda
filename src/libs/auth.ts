import { env } from "@/env";
import {
  sendChangeEmail,
  sendOrgInviteEmail,
  sendPasswordResetEmail,
  sendVerifyEmail,
} from "@/libs/mail";
import { db } from "@/server/db";
import { generateAvatarImage } from "@/utils/generate-avatar-image";
import { type AuthContext, betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { openAPI, organization } from "better-auth/plugins";

export type Session = typeof auth.$Infer.Session;

export const auth = betterAuth({
  database: prismaAdapter(db, {
    provider: "postgresql",
  }),
  advanced: {
    generateId: false,
  },
  onAPIError: {
    onError: async (error: unknown, ctx: AuthContext) => {
      const errorContext = {
        error,
        baseURL: ctx.baseURL,
        session: ctx.session,
        trustedOrigins: ctx.trustedOrigins,
        request: {
          url: ctx.options.baseURL,
          method: ctx.options.basePath,
        },
      };
      console.info("Auth API error", errorContext);
    },
  },
  databaseHooks: {
    user: {
      create: {
        before: async (user) => {
          if (!user.image) {
            user.image = generateAvatarImage(user.name);
          }
          return { data: user };
        },
      },
    },
  },
  plugins: [
    openAPI(),
    organization({
      async sendInvitationEmail(data) {
        const inviteLink = constructInviteLink(data);

        await sendOrgInviteEmail(
          data.email,
          data.organization.name,
          inviteLink,
        );
      },
    }),
  ],
  accounts: {
    fields: {
      accountId: "providerAccountId",
      refreshToken: "refresh_token",
      accessToken: "access_token",
      accessTokenExpiresAt: "access_token_expires",
      idToken: "id_token",
    },
  },
  emailAndPassword: {
    enabled: true,
    sendResetPassword: async ({ user, url }) => {
      await sendPasswordResetEmail(user.email, url);
    },
  },
  user: {
    changeEmail: {
      enabled: true,
      sendChangeEmailVerification: async ({ user, newEmail, url }) => {
        await sendChangeEmail(user.email, newEmail, url);
      },
    },
    deleteUser: {
      enabled: true,
    },
  },
  emailVerification: {
    sendOnSignUp: true,
    autoSignInAfterVerification: true,
    sendVerificationEmail: async ({ user, url }) => {
      await sendVerifyEmail(user.email, `${url}&callbackURL=/organizations`);
    },
  },
  socialProviders: {
    google: {
      clientId: env.GOOGLE_CLIENT_ID,
      clientSecret: env.GOOGLE_CLIENT_SECRET,
    },
    microsoft: {
      clientId: env.MICROSOFT_CLIENT_ID,
      clientSecret: env.MICROSOFT_CLIENT_SECRET,
    },
  },
  account: {
    accountLinking: {
      enabled: true,
      trustedProviders: ["google", "github", "email-password", "microsoft"],
    },
  },
});

function constructInviteLink(data: {
  id: string;
  email: string;
  organization: { name: string; id: string };
}) {
  const searchParams = new URLSearchParams({
    organization: data.organization.name,
    organizationId: data.organization.id,
    inviteId: data.id,
    email: data.email,
  });

  const inviteLink = `${env.APP_URL}/api/invite?${searchParams.toString()}`;
  return inviteLink;
}
