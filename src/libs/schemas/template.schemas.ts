import { z } from "../zod";
import { getPaginationQuerySchema } from "./api.schemas";

export const templateCreateSchema = z.object({
  name: z.string().min(1, "Template name is a required field."),
  description: z.string().default(""),
  category: z.string().default(""),
  organizationId: z.string().optional(),
  isFormboxTemplate: z.boolean().default(false),

  // Form settings and customization options
  removeFormboxBranding: z.boolean().optional(),
  sendEmailNotifications: z.boolean().optional(),
  emailsToNotify: z.array(z.string()).default([]).optional(),
  submissionStorageDuration: z.string().optional(),
  sendRespondantEmailNotifications: z.boolean().optional(),
  respondantEmailFromName: z.string().optional(),
  respondantEmailSubject: z.string().optional(),
  respondantEmailMessageHTML: z.string().optional(),
  submitButtonText: z.string().optional(),
  limitResponses: z.boolean().optional(),
  isClosed: z.boolean().optional(),
  autoCloseEnabled: z.boolean().optional(),
  autoCloseDate: z.date().optional().nullable(),
  autoCloseTime: z.string().optional(),
  autoCloseTimezone: z.string().optional(),
  maxResponses: z.number().optional().nullable(),
  useCustomRedirect: z.boolean().optional(),
  customSuccessUrl: z.string().optional(),
  webhookEnabled: z.boolean().optional(),
  webhookUrl: z.string().optional(),
  customHoneypot: z.string().optional(),
  googleRecaptchaEnabled: z.boolean().optional(),
  googleRecaptchaSecretKey: z.string().optional(),
  allowedDomains: z.string().optional(),
  allowedCountries: z.string().optional(),
  ipBlacklist: z.string().optional(),

  // Styling options
  accentColor: z.string().optional(),
  backgroundColor: z.string().optional(),
  buttonBackgroundColor: z.string().optional(),
  buttonBorderStyle: z.string().optional(),
  buttonTextColor: z.string().optional(),
  closeMessageDescription: z.string().optional(),
  closeMessageTitle: z.string().optional(),
  headerDescription: z.string().optional(),
  headerImage: z.string().optional(),
  headerTitle: z.string().optional(),
  inputBorderStyle: z.string().optional(),
  logo: z.string().optional(),
  pageMode: z.string().optional(),
  saveAnswers: z.boolean().optional(),
  showCustomClosedMessage: z.boolean().optional(),
  textColor: z.string().optional(),
  tpBackgroundColor: z.string().optional(),
  tpButtonColor: z.string().optional(),
  tpButtonText: z.string().optional(),
  tpButtonUrl: z.string().optional(),
  tpHeader: z.string().optional(),
  tpMessage: z.string().optional(),
  tpTextColor: z.string().optional(),
  useCustomThankYouPage: z.boolean().optional(),
  tpButtonBackgroundColor: z.string().optional(),

  // SMTP settings
  smtpHost: z.string().optional(),
  smtpPassword: z.string().optional(),
  smtpPort: z.number().optional(),
  smtpUsername: z.string().optional(),
  smtpEnabled: z.boolean().optional(),
  smtpSenderEmail: z.string().optional(),

  fields: z
    .array(
      z.object({
        id: z.string(),
        label: z.string(),
        description: z.string().optional(),
        type: z.string(),
        subtype: z.string(),
        required: z.boolean(),
        ratingCount: z.number().optional(),
        options: z
          .array(z.object({ id: z.string(), value: z.string() }))
          .optional(),
        showDescription: z.boolean(),
      }),
    )
    .default([]),
});

export const templateDuplicateSchema = z.object({
  id: z.string(),
  organizationId: z.string().optional(),
});

export const templateUpdateSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  description: z.string().optional(),
  category: z.string().optional(),
  isFormboxTemplate: z.boolean().optional(),

  // Form settings and customization options
  removeFormboxBranding: z.boolean().optional(),
  sendEmailNotifications: z.boolean().optional(),
  emailsToNotify: z.array(z.string()).optional(),
  submissionStorageDuration: z.string().optional(),
  sendRespondantEmailNotifications: z.boolean().optional(),
  respondantEmailFromName: z.string().optional(),
  respondantEmailSubject: z.string().optional(),
  respondantEmailMessageHTML: z.string().optional(),
  submitButtonText: z.string().optional(),
  limitResponses: z.boolean().optional(),
  isClosed: z.boolean().optional(),
  autoCloseEnabled: z.boolean().optional(),
  autoCloseDate: z.date().optional().nullable(),
  autoCloseTime: z.string().optional(),
  autoCloseTimezone: z.string().optional(),
  maxResponses: z.number().optional().nullable(),
  useCustomRedirect: z.boolean().optional(),
  customSuccessUrl: z.string().optional(),
  webhookEnabled: z.boolean().optional(),
  webhookUrl: z.string().optional(),
  customHoneypot: z.string().optional(),
  googleRecaptchaEnabled: z.boolean().optional(),
  googleRecaptchaSecretKey: z.string().optional(),
  allowedDomains: z.string().optional(),
  allowedCountries: z.string().optional(),
  ipBlacklist: z.string().optional(),

  // Styling options
  accentColor: z.string().optional(),
  backgroundColor: z.string().optional(),
  buttonBackgroundColor: z.string().optional(),
  buttonBorderStyle: z.string().optional(),
  buttonTextColor: z.string().optional(),
  closeMessageDescription: z.string().optional(),
  closeMessageTitle: z.string().optional(),
  headerDescription: z.string().optional(),
  headerImage: z.string().optional(),
  headerTitle: z.string().optional(),
  inputBorderStyle: z.string().optional(),
  logo: z.string().optional(),
  pageMode: z.string().optional(),
  saveAnswers: z.boolean().optional(),
  showCustomClosedMessage: z.boolean().optional(),
  textColor: z.string().optional(),
  tpBackgroundColor: z.string().optional(),
  tpButtonColor: z.string().optional(),
  tpButtonText: z.string().optional(),
  tpButtonUrl: z.string().optional(),
  tpHeader: z.string().optional(),
  tpMessage: z.string().optional(),
  tpTextColor: z.string().optional(),
  useCustomThankYouPage: z.boolean().optional(),
  tpButtonBackgroundColor: z.string().optional(),

  // SMTP settings
  smtpHost: z.string().optional(),
  smtpPassword: z.string().optional(),
  smtpPort: z.number().optional(),
  smtpUsername: z.string().optional(),
  smtpEnabled: z.boolean().optional(),
  smtpSenderEmail: z.string().optional(),

  fields: z
    .array(
      z.object({
        id: z.string(),
        label: z.string(),
        description: z.string().optional(),
        type: z.string(),
        subtype: z.string(),
        required: z.boolean(),
        ratingCount: z.number().optional(),
        options: z
          .array(z.object({ id: z.string(), value: z.string() }))
          .optional(),
        showDescription: z.boolean(),
      }),
    )
    .optional(),
});

// External API schemas
export const templateCreateBodySchema = z.object({
  name: z
    .string()
    .min(1, "Template name is a required field.")
    .describe("Template name"),
  description: z.string().default("").describe("Template description"),
  category: z
    .string()
    .default("")
    .describe("Template category for organization"),
  isFormboxTemplate: z
    .boolean()
    .default(false)
    .describe("Whether this is a default Formbox template")
    .openapi({
      default: false,
    }),

  // Form settings and customization options
  removeFormboxBranding: z
    .boolean()
    .optional()
    .describe("Remove Formbox branding"),
  sendEmailNotifications: z
    .boolean()
    .optional()
    .describe("Send email notifications"),
  emailsToNotify: z
    .array(z.string())
    .default([])
    .optional()
    .describe("Emails to notify"),
  submissionStorageDuration: z
    .string()
    .optional()
    .describe("Submission storage duration"),
  sendRespondantEmailNotifications: z
    .boolean()
    .optional()
    .describe("Send respondent email notifications"),
  respondantEmailFromName: z
    .string()
    .optional()
    .describe("Respondent email from name"),
  respondantEmailSubject: z
    .string()
    .optional()
    .describe("Respondent email subject"),
  respondantEmailMessageHTML: z
    .string()
    .optional()
    .describe("Respondent email message HTML"),
  submitButtonText: z.string().optional().describe("Submit button text"),
  limitResponses: z.boolean().optional().describe("Limit responses"),
  isClosed: z.boolean().optional().describe("Is form closed"),
  autoCloseEnabled: z.boolean().optional().describe("Auto close enabled"),
  autoCloseDate: z.date().optional().nullable().describe("Auto close date"),
  autoCloseTime: z.string().optional().describe("Auto close time"),
  autoCloseTimezone: z.string().optional().describe("Auto close timezone"),
  maxResponses: z.number().optional().nullable().describe("Maximum responses"),
  useCustomRedirect: z.boolean().optional().describe("Use custom redirect"),
  customSuccessUrl: z.string().optional().describe("Custom success URL"),
  webhookEnabled: z.boolean().optional().describe("Webhook enabled"),
  webhookUrl: z.string().optional().describe("Webhook URL"),
  customHoneypot: z.string().optional().describe("Custom honeypot"),
  googleRecaptchaEnabled: z
    .boolean()
    .optional()
    .describe("Google reCAPTCHA enabled"),
  googleRecaptchaSecretKey: z
    .string()
    .optional()
    .describe("Google reCAPTCHA secret key"),
  allowedDomains: z.string().optional().describe("Allowed domains"),
  allowedCountries: z.string().optional().describe("Allowed countries"),
  ipBlacklist: z.string().optional().describe("IP blacklist"),

  // Styling options
  accentColor: z.string().optional().describe("Accent color"),
  backgroundColor: z.string().optional().describe("Background color"),
  buttonBackgroundColor: z
    .string()
    .optional()
    .describe("Button background color"),
  buttonBorderStyle: z.string().optional().describe("Button border style"),
  buttonTextColor: z.string().optional().describe("Button text color"),
  closeMessageDescription: z
    .string()
    .optional()
    .describe("Close message description"),
  closeMessageTitle: z.string().optional().describe("Close message title"),
  headerDescription: z.string().optional().describe("Header description"),
  headerImage: z.string().optional().describe("Header image"),
  headerTitle: z.string().optional().describe("Header title"),
  inputBorderStyle: z.string().optional().describe("Input border style"),
  logo: z.string().optional().describe("Logo"),
  pageMode: z.string().optional().describe("Page mode"),
  saveAnswers: z.boolean().optional().describe("Save answers"),
  showCustomClosedMessage: z
    .boolean()
    .optional()
    .describe("Show custom closed message"),
  textColor: z.string().optional().describe("Text color"),
  tpBackgroundColor: z
    .string()
    .optional()
    .describe("Thank you page background color"),
  tpButtonColor: z.string().optional().describe("Thank you page button color"),
  tpButtonText: z.string().optional().describe("Thank you page button text"),
  tpButtonUrl: z.string().optional().describe("Thank you page button URL"),
  tpHeader: z.string().optional().describe("Thank you page header"),
  tpMessage: z.string().optional().describe("Thank you page message"),
  tpTextColor: z.string().optional().describe("Thank you page text color"),
  useCustomThankYouPage: z
    .boolean()
    .optional()
    .describe("Use custom thank you page"),
  tpButtonBackgroundColor: z
    .string()
    .optional()
    .describe("Thank you page button background color"),

  // SMTP settings
  smtpHost: z.string().optional().describe("SMTP host"),
  smtpPassword: z.string().optional().describe("SMTP password"),
  smtpPort: z.number().optional().describe("SMTP port"),
  smtpUsername: z.string().optional().describe("SMTP username"),
  smtpEnabled: z.boolean().optional().describe("SMTP enabled"),
  smtpSenderEmail: z.string().optional().describe("SMTP sender email"),

  fields: z
    .array(
      z.object({
        id: z.string().describe("The field ID."),
        label: z.string().describe("The field label."),
        description: z.string().optional().describe("The field description."),
        type: z.string().describe("The field type."),
        subtype: z.string().describe("The field subtype."),
        required: z.boolean().describe("Whether the field is required."),
        ratingCount: z
          .number()
          .optional()
          .describe("The number of rating options."),
        options: z
          .array(z.object({ id: z.string(), value: z.string() }))
          .optional()
          .describe(
            "The options for multi-choice, dropdown, or single choice fields.",
          ),
        showDescription: z
          .boolean()
          .describe("Whether to show the field description."),
      }),
    )
    .default([])
    .describe("The template fields"),
});

export const templateUpdateBodySchema = z.object({
  name: z.string().optional().describe("Template name"),
  description: z.string().optional().describe("Template description"),
  category: z.string().optional().describe("Template category"),
  isFormboxTemplate: z
    .boolean()
    .optional()
    .describe("Whether this is a default Formbox template"),

  // Form settings and customization options
  removeFormboxBranding: z
    .boolean()
    .optional()
    .describe("Remove Formbox branding"),
  sendEmailNotifications: z
    .boolean()
    .optional()
    .describe("Send email notifications"),
  emailsToNotify: z.array(z.string()).optional().describe("Emails to notify"),
  submissionStorageDuration: z
    .string()
    .optional()
    .describe("Submission storage duration"),
  sendRespondantEmailNotifications: z
    .boolean()
    .optional()
    .describe("Send respondent email notifications"),
  respondantEmailFromName: z
    .string()
    .optional()
    .describe("Respondent email from name"),
  respondantEmailSubject: z
    .string()
    .optional()
    .describe("Respondent email subject"),
  respondantEmailMessageHTML: z
    .string()
    .optional()
    .describe("Respondent email message HTML"),
  submitButtonText: z.string().optional().describe("Submit button text"),
  limitResponses: z.boolean().optional().describe("Limit responses"),
  isClosed: z.boolean().optional().describe("Is form closed"),
  autoCloseEnabled: z.boolean().optional().describe("Auto close enabled"),
  autoCloseDate: z.date().optional().nullable().describe("Auto close date"),
  autoCloseTime: z.string().optional().describe("Auto close time"),
  autoCloseTimezone: z.string().optional().describe("Auto close timezone"),
  maxResponses: z.number().optional().nullable().describe("Maximum responses"),
  useCustomRedirect: z.boolean().optional().describe("Use custom redirect"),
  customSuccessUrl: z.string().optional().describe("Custom success URL"),
  webhookEnabled: z.boolean().optional().describe("Webhook enabled"),
  webhookUrl: z.string().optional().describe("Webhook URL"),
  customHoneypot: z.string().optional().describe("Custom honeypot"),
  googleRecaptchaEnabled: z
    .boolean()
    .optional()
    .describe("Google reCAPTCHA enabled"),
  googleRecaptchaSecretKey: z
    .string()
    .optional()
    .describe("Google reCAPTCHA secret key"),
  allowedDomains: z.string().optional().describe("Allowed domains"),
  allowedCountries: z.string().optional().describe("Allowed countries"),
  ipBlacklist: z.string().optional().describe("IP blacklist"),

  // Styling options
  accentColor: z.string().optional().describe("Accent color"),
  backgroundColor: z.string().optional().describe("Background color"),
  buttonBackgroundColor: z
    .string()
    .optional()
    .describe("Button background color"),
  buttonBorderStyle: z.string().optional().describe("Button border style"),
  buttonTextColor: z.string().optional().describe("Button text color"),
  closeMessageDescription: z
    .string()
    .optional()
    .describe("Close message description"),
  closeMessageTitle: z.string().optional().describe("Close message title"),
  headerDescription: z.string().optional().describe("Header description"),
  headerImage: z.string().optional().describe("Header image"),
  headerTitle: z.string().optional().describe("Header title"),
  inputBorderStyle: z.string().optional().describe("Input border style"),
  logo: z.string().optional().describe("Logo"),
  pageMode: z.string().optional().describe("Page mode"),
  saveAnswers: z.boolean().optional().describe("Save answers"),
  showCustomClosedMessage: z
    .boolean()
    .optional()
    .describe("Show custom closed message"),
  textColor: z.string().optional().describe("Text color"),
  tpBackgroundColor: z
    .string()
    .optional()
    .describe("Thank you page background color"),
  tpButtonColor: z.string().optional().describe("Thank you page button color"),
  tpButtonText: z.string().optional().describe("Thank you page button text"),
  tpButtonUrl: z.string().optional().describe("Thank you page button URL"),
  tpHeader: z.string().optional().describe("Thank you page header"),
  tpMessage: z.string().optional().describe("Thank you page message"),
  tpTextColor: z.string().optional().describe("Thank you page text color"),
  useCustomThankYouPage: z
    .boolean()
    .optional()
    .describe("Use custom thank you page"),
  tpButtonBackgroundColor: z
    .string()
    .optional()
    .describe("Thank you page button background color"),

  // SMTP settings
  smtpHost: z.string().optional().describe("SMTP host"),
  smtpPassword: z.string().optional().describe("SMTP password"),
  smtpPort: z.number().optional().describe("SMTP port"),
  smtpUsername: z.string().optional().describe("SMTP username"),
  smtpEnabled: z.boolean().optional().describe("SMTP enabled"),
  smtpSenderEmail: z.string().optional().describe("SMTP sender email"),

  fields: z
    .array(
      z.object({
        id: z.string(),
        label: z.string(),
        description: z.string().optional(),
        type: z.string(),
        subtype: z.string(),
        required: z.boolean(),
        ratingCount: z.number().optional(),
        options: z
          .array(z.object({ id: z.string(), value: z.string() }))
          .optional(),
        showDescription: z.boolean(),
      }),
    )
    .optional()
    .describe("The template fields"),
});

// OpenAPI response schemas
export const templateSchema = z
  .object({
    id: z.string().describe("The unique ID of the template."),
    name: z.string().describe("The name of the template."),
    description: z.string().describe("The description of the template."),
    category: z.string().describe("The category of the template."),
    isFormboxTemplate: z
      .boolean()
      .describe("Whether this is a default Formbox template."),
    organizationId: z
      .string()
      .nullable()
      .describe("The ID of the organization that owns this template."),
    fields: z
      .array(
        z.object({
          id: z.string().describe("The field ID."),
          label: z.string().describe("The field label."),
          description: z.string().optional().describe("The field description."),
          type: z.string().describe("The field type."),
          subtype: z.string().describe("The field subtype."),
          required: z.boolean().describe("Whether the field is required."),
          ratingCount: z
            .number()
            .optional()
            .describe("The number of rating options."),
          options: z
            .array(z.object({ id: z.string(), value: z.string() }))
            .optional()
            .describe(
              "The options for multi-choice, dropdown, or single choice fields.",
            ),
          showDescription: z
            .boolean()
            .describe("Whether to show the field description."),
        }),
      )
      .describe("The template fields"),
    createdAt: z.string().describe("The date the template was created."),
    updatedAt: z.string().describe("The date the template was last updated."),
  })
  .openapi({ title: "Template" });

export const getTemplatesQuerySchema = z
  .object({
    search: z.string().optional().describe("Search for a template by name."),
    category: z.string().optional().describe("Filter templates by category."),
    isFormboxTemplate: z
      .boolean()
      .optional()
      .describe("Filter by Formbox default templates."),
    sort: z
      .enum(["createdAt", "name"])
      .optional()
      .default("createdAt")
      .describe(
        "The field to sort the templates by. The default is `createdAt`, and sort order is always descending.",
      )
      .openapi({
        default: "createdAt",
      }),
  })
  .merge(getPaginationQuerySchema({ maxPageSize: 100 }));

export const getTemplatesResponseSchema = z.object({
  data: z
    .array(
      z.object({
        id: z.string().describe("The unique ID of the template."),
        name: z.string().describe("The name of the template."),
        description: z.string().describe("The description of the template."),
        category: z.string().describe("The category of the template."),
        isFormboxTemplate: z
          .boolean()
          .describe("Whether this is a default Formbox template."),
        organizationId: z
          .string()
          .nullable()
          .describe("The ID of the organization that owns this template."),
        createdAt: z.string().describe("The date the template was created."),
        updatedAt: z
          .string()
          .describe("The date the template was last updated."),
      }),
    )
    .describe("The list of templates."),
  meta: z
    .object({
      total: z.number().describe("The total number of templates."),
    })
    .describe("Extra metadata about the templates response."),
  pagination: z
    .object({
      totalPages: z.number().describe("The total number of pages."),
      currentPage: z.number().describe("The current page number."),
      nextPage: z.number().nullish().describe("The next page number."),
      previousPage: z.number().nullish().describe("The previous page number."),
    })
    .describe("Pagination metadata."),
});

export const templateResponseSchema = templateSchema;
