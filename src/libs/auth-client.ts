import { env } from "@/env";
import { type Session } from "@/libs/auth";
import { betterFetch } from "@better-fetch/fetch";
import { organizationClient, passkeyClient } from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";
import { type NextRequest } from "next/server";

export const authClient = createAuthClient({
  baseURL: env.NEXT_PUBLIC_APP_URL, // the base url of your auth server
  plugins: [organizationClient(), passkeyClient()],
});

export async function getAuthSession(request: NextRequest) {
  return await betterFetch<Session>("/api/auth/get-session", {
    baseURL: request.nextUrl.origin,
    headers: {
      // get the cookie from the request
      cookie: request.headers.get("cookie") ?? "",
    },
  });
}

export type AuthProvider = "google" | "github" | "microsoft";

export async function signInWithProvider(
  provider: AuthProvider,
  callbackURL?: string,
) {
  return await authClient.signIn.social({
    provider,
    callbackURL: callbackURL || `${env.NEXT_PUBLIC_APP_URL}/organizations`,
  });
}

export async function linkSocialProvider(
  provider: AuthProvider,
  callbackURL: string,
) {
  await authClient.linkSocial({
    provider,
    callbackURL,
  });
}

export async function getUserPasskeys() {
  const passkeys = await authClient.passkey.listUserPasskeys();
  return { passkeys: passkeys.data, error: passkeys.error };
}

export async function generatePasskey(name: string) {
  return await authClient.passkey.addPasskey({ name });
}

export async function removePasskey(id: string) {
  return await authClient.passkey.deletePasskey({ id });
}

export async function sendPasswordResetEmail(email: string) {
  return await authClient.forgetPassword({
    email,
    redirectTo: "/auth/reset-password", // The redirect URL after verification
  });
}

export async function sendChangeEmailVerificationEmail(email: string) {
  return await authClient.changeEmail({
    newEmail: email,
    callbackURL: "/settings",
  });
}
