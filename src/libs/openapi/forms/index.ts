import { type ZodOpenApiPathsObject } from "zod-openapi";
import { createFormOperation } from "./create-form";
import { deleteFormByIdOperation } from "./delete-form-by-id";
import { getFormByIdOperation } from "./get-form-by-id";
import { getFormsOperation } from "./get-forms";
import { updateFormByIdOperation } from "./update-form-by-id";

export const formsPaths: ZodOpenApiPathsObject = {
  "/forms": {
    get: getFormsOperation,
    post: createFormOperation,
  },
  "/forms/{formId}": {
    get: getFormByIdOperation,
    patch: updateFormByIdOperation,
    delete: deleteFormByIdOperation,
  },
};
