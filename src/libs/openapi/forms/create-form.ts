import { api<PERSON><PERSON>Header } from "@/libs/openapi/headers";
import { openApiErrorResponses } from "@/libs/openapi/responses";
import {
  formCreateBodySchema,
  formResponseSchema,
} from "@/libs/schemas/form.schemas";
import { type ZodOpenApiOperationObject } from "zod-openapi";

export const createFormOperation: ZodOpenApiOperationObject = {
  operationId: "createForm",
  summary: "Create a new form",
  description: "Create a new form for the authenticated organization.",
  requestParams: {
    header: apiKeyHeader,
  },
  requestBody: {
    content: {
      "application/json": {
        schema: formCreateBodySchema,
      },
    },
  },
  responses: {
    "200": {
      description: "The newly created form.",
      content: {
        "application/json": {
          schema: formResponseSchema,
        },
      },
    },
    ...openApiErrorResponses,
  },
  tags: ["Forms"],
  security: [{ token: [] }],
};
