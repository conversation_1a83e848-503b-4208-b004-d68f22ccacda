import { api<PERSON><PERSON><PERSON>eader } from "@/libs/openapi/headers";
import { openApiErrorResponses } from "@/libs/openapi/responses";
import {
  getFormsQuerySchema,
  getFormsResponseSchema,
} from "@/libs/schemas/form.schemas";
import { type ZodOpenApiOperationObject } from "zod-openapi";

export const getFormsOperation: ZodOpenApiOperationObject = {
  operationId: "getForms",
  summary: "Retrieve a list of forms",
  description:
    "Retrieve a paginated list of forms for the authenticated organization.",
  requestParams: {
    header: api<PERSON><PERSON><PERSON>eader,
    query: getFormsQuerySchema,
  },
  responses: {
    "200": {
      description: "A list of forms.",
      content: {
        "application/json": {
          schema: getFormsResponseSchema,
        },
      },
    },
    ...openApiErrorResponses,
  },
  tags: ["Forms"],
  security: [{ token: [] }],
};
