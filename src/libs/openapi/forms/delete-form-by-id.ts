import { api<PERSON><PERSON><PERSON>eader } from "@/libs/openapi/headers";
import { openApiErrorResponses } from "@/libs/openapi/responses";
import { z } from "@/libs/zod";
import { type ZodOpenApiOperationObject } from "zod-openapi";

export const deleteFormByIdOperation: ZodOpenApiOperationObject = {
  operationId: "deleteForm",
  summary: "Delete a form",
  description: "Delete a specific form for the authenticated organization.",
  requestParams: {
    header: api<PERSON>eyHeader,
    path: z.object({
      formId: z
        .string()
        .describe("The unique ID of the form to delete.")
        .openapi({ example: "clux0rgak00011..." }),
    }),
  },
  responses: {
    "200": {
      description: "A specific form.",
      content: {
        "application/json": {
          schema: z.object({
            formId: z
              .string()
              .describe("The unique ID of the form that was deleted."),
          }),
        },
      },
    },
    ...openApiErrorResponses,
  },
  tags: ["Forms"],
  security: [{ token: [] }],
};
