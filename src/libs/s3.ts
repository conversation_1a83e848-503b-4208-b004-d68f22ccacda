import { env } from "@/env";
import {
  DeleteObjectCommand,
  PutObjectCommand,
  S3Client,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

export const s3Client = new S3Client({
  region: "auto",
  endpoint: env.AWS_ENDPOINT_URL_S3,
  forcePathStyle: false,
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
});

export async function getUploadUrl(imageKey: string) {
  const putCommand = new PutObjectCommand({
    Bucket: env.AWS_BUCKET_NAME,
    Key: imageKey,
  });
  return await getSignedUrl(s3Client, putCommand, { expiresIn: 60 });
}

export async function deleteFile(fileKey: string) {
  const deleteParams = {
    Bucket: env.AWS_BUCKET_NAME,
    Key: fileKey,
  };

  return await s3Client.send(new DeleteObjectCommand(deleteParams));
}
