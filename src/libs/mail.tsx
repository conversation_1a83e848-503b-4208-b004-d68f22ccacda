import ChangeEmailTemplate from "@/emails/change-email-template";
import OrgInviteEmailTemplate from "@/emails/org-invite-email-template-v2";
import PasswordResetEmailTemplate from "@/emails/password-reset-email-template";
import VerifyEmailTemplate from "@/emails/verify-email-template-v2";
import WelcomeEmailTemplate from "@/emails/welcome-email-template";
import { env } from "@/env";
import { render } from "@react-email/components";
import nodemailer from "nodemailer";
import { Resend } from "resend";

export const resend = new Resend(env.RESEND_API_KEY);

const emailFrom = `Formbox <${env.EMAIL_FROM}>`;

export async function sendPasswordResetEmail(email: string, link: string) {
  const emailTemplate = PasswordResetEmailTemplate({ email, link });

  const text = await render(emailTemplate, {
    plainText: true,
  });

  return await resend.emails.send({
    subject: "Reset your password",
    from: `Formbox <<EMAIL>>`,
    to: email,
    text,
    react: emailTemplate,
  });
}

export async function sendWelcomeEmail(email: string) {
  const emailTemplate = WelcomeEmailTemplate({});

  const text = await render(emailTemplate, {
    plainText: true,
  });

  return await resend.emails.send({
    subject: "Welcome to Formbox",
    from: `Formbox Team <<EMAIL>>`,
    to: email,
    text,
    react: emailTemplate,
  });
}

export async function sendVerifyEmail(email: string, link: string) {
  const emailTemplate = VerifyEmailTemplate({ email, link });

  const text = await render(emailTemplate, {
    plainText: true,
  });

  return await resend.emails.send({
    subject: "Verify your email address",
    from: `Formbox <<EMAIL>>`,
    to: email,
    text,
    react: emailTemplate,
  });
}

export async function sendChangeEmail(
  email: string,
  newEmail: string,
  link: string,
) {
  const emailTemplate = ChangeEmailTemplate({ email: newEmail, link });

  const text = await render(emailTemplate, {
    plainText: true,
  });

  return await resend.emails.send({
    subject: "Change your email address",
    from: `Formbox <<EMAIL>>`,
    to: email,
    text,
    react: emailTemplate,
  });
}

export async function sendOrgInviteEmail(
  email: string,
  orgName: string,
  link: string,
) {
  const emailTemplate = OrgInviteEmailTemplate({ orgName, link });

  const text = await render(emailTemplate, {
    plainText: true,
  });

  return await resend.emails.send({
    subject: "You've been invited to join an organization on Formbox",
    from: `Formbox <<EMAIL>>`,
    to: email,
    text,
    react: emailTemplate,
  });
}

type SubmissionNotificationEmailPayload = {
  to: string;
  subject: string;
  text: string;
  react: React.ReactElement;
};

export async function sendBatchSubmissionNotificationEmail(
  payload: SubmissionNotificationEmailPayload[],
) {
  const emails = payload.map((email) => {
    return {
      ...email,
      from: emailFrom,
    };
  });
  return await resend.batch.send(emails);
}

export async function sendFormRespondentEmail(
  fromName: string,
  to: string,
  subject: string,
  html: string,
) {
  return await resend.emails.send({
    from: `${fromName} <${env.EMAIL_FROM}>`,
    subject,
    to,
    html,
  });
}

export async function sendFormRespondentEmailWithSMTP({
  fromName,
  to,
  subject,
  html,
  smtpHost,
  smtpPort,
  smtpUsername,
  smtpPassword,
  smtpSenderEmail,
}: {
  fromName: string;
  to: string;
  subject: string;
  html: string;
  smtpHost: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  smtpSenderEmail: string;
}) {
  const transporter = nodemailer.createTransport({
    host: smtpHost,
    port: smtpPort,
    secure: true,
    auth: {
      user: smtpUsername,
      pass: smtpPassword,
    },
  });

  return await transporter.sendMail({
    from: `${fromName} <${smtpSenderEmail}>`,
    to,
    subject,
    html,
  });
}

// function injectTemplateValues(answers: Answer[], html: string) {
//   let newHtml = html;
//   answers.forEach((answer) => {
//     newHtml = newHtml.replace(`{{${answer.label}}}`, answer.value);
//   });
//   return newHtml;
// }
