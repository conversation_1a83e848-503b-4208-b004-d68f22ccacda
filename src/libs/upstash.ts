import { env } from "@/env";
import { Client } from "@upstash/qstash";
import { Ratelimit } from "@upstash/ratelimit";
import { Redis } from "@upstash/redis";

export const redis = Redis.fromEnv();

export const qstash = new Client({ token: env.QSTASH_TOKEN });

export const qstashUrls = {
  welcomeEmailUrl: `${env.APP_URL}/api/jobs/send-welcome-email`,
} as const;

/**
 * Rate limit for API requests.
 * @param identifier The identifier to rate limit (e.g., IP address or user ID)
 * @returns A promise that resolves to a boolean indicating if the request is allowed
 */
export async function rateLimit(identifier: string) {
  const ratelimit = new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(600, "1 m"),
    analytics: true,
    prefix: "ratelimit/api",
  });

  return await ratelimit.limit(identifier);
}

/**
 * Deletes all Redis keys matching the given prefix.
 * Uses SCAN to avoid blocking Redis.
 * Deletes in batches to avoid large operations.
 * @param prefix The prefix to match (e.g., "form_api:")
 */
export async function invalidateByPrefix(prefix: string) {
  let cursor = "0";
  const batchSize = 100;
  const deleteBatchSize = 1000; // Delete in smaller batches
  let keysToDelete: string[] = [];

  do {
    const [nextCursor, keys] = await redis.scan(cursor, {
      match: `${prefix}*`,
      count: batchSize,
    });
    cursor = nextCursor;

    if (Array.isArray(keys) && keys.length > 0) {
      keysToDelete.push(...keys);

      // Delete in batches to avoid large operations
      if (keysToDelete.length >= deleteBatchSize) {
        await redis.del(...keysToDelete);
        keysToDelete = [];
      }
    }
  } while (cursor !== "0");

  // Delete remaining keys
  if (keysToDelete.length > 0) {
    await redis.del(...keysToDelete);
  }
}
