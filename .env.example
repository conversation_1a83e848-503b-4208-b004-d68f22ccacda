
# ===============================
# Client Environment Variables
# ===============================

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=""

# Unsplash
NEXT_PUBLIC_UNSPLASH_ACCESS_KEY=""

# Formbox API endpoints
NEXT_PUBLIC_SUBMISSIONS_API_URL="http://localhost:8080"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Integrations
NEXT_PUBLIC_NANGO_KEY=""

# Feedback & Docs
NEXT_PUBLIC_FEEDBACK_URL="https://formbox.featurebase.app"
NEXT_PUBLIC_CHANGELOG_URL="https://formbox.featurebase.app/changelog"
NEXT_PUBLIC_DOCS_URL="https://docs.formbox.app"

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_HOST="https://us.i.posthog.com"
NEXT_PUBLIC_POSTHOG_KEY=""

# AWS S3 (File Storage)
NEXT_PUBLIC_AWS_PUBLIC_BUCKET_URL=""

# ===============================
# Server Environment Variables
# ===============================

# Database
DATABASE_URL="postgres://postgres:postgres@localhost:54322/postgres" # Dev DB
# DATABASE_URL="postgres://postgres.arfvvafungsdznuanwdt:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1" # Prod DB

# App
APP_URL="https://dev.formbox.app"
# APP_URL="https://s23rmnwp-3000.use.devtunnels.ms"
NODE_ENV="development"

# Microsoft Azure
MICROSOFT_CLIENT_ID=""
MICROSOFT_CLIENT_SECRET=""
MICROSOFT_TENANT_ID=""

# Google OAuth
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Email
RESEND_API_KEY=""
EMAIL_FROM="<EMAIL>"
EMAIL_SERVER=""

# QStash (Background Jobs)
QSTASH_TOKEN=""
QSTASH_CURRENT_SIGNING_KEY=""
QSTASH_NEXT_SIGNING_KEY=""

# AWS S3 (File Storage)
AWS_BUCKET_NAME="formbox-dev"
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_ENDPOINT_URL_S3="https://t3.storage.dev"

# Stripe
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""

# Integrations
NANGO_SECRET_KEY=""

# PostHog Analytics
POSTHOG_API_KEY=""
POSTHOG_ENV_ID=""

# Abstract API
ABSTRACT_API_KEY=""

# Arcjet (Rate Limiting & Bot Protection)
ARCJET_KEY=""