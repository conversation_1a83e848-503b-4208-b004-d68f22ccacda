# Form Builder Refactor Plan (No Directory Changes)

This plan improves the form builder’s organization and extensibility while keeping your existing high‑level directory structure intact. We will add a few focused files inside current folders and refactor progressively without moving directories.

## Goals

- Improve maintainability and extensibility (new field types, features)
- Reduce prop drilling and central “god” render functions
- Isolate side‑effects (save, uploads) from UI
- Keep current folders; only add files inside them

---

## 1) State: enrich the existing store (src/stores/form.store.ts)

Keep the Zustand store file and add typed, granular actions. Prefer selectors in components.

Add actions:

- loadForm(form)
- select(id | null)
- addField(field, index?)
- updateField(id, updater)
- removeField(id)
- moveField(from, to)

Use Immer to simplify immutable updates.

Example shape (add inside src/stores/form.store.ts):

```ts
// Pseudocode showing the shape; merge into existing store
import { create } from "zustand";
import { produce } from "immer";

export const useFormStore = create<FormStateStore & Actions>()((set) => ({
  form: null,
  selectedId: null,
  setForm: (form) => set({ form }),
  loadForm: (f) => set({ form: f }),
  select: (id) => set({ selectedId: id }),
  addField: (field, index) =>
    set(
      produce((s: FormStateStore) => {
        if (!s.form) return;
        const i = index ?? s.form.fields.length;
        s.form.fields.splice(i, 0, field);
      }),
    ),
  updateField: (id, updater) =>
    set(
      produce((s: FormStateStore) => {
        const f = s.form?.fields.find((x) => x.id === id);
        if (f) updater(f);
      }),
    ),
  removeField: (id) =>
    set(
      produce((s: FormStateStore) => {
        if (!s.form) return;
        s.form.fields = s.form.fields.filter((x) => x.id !== id);
      }),
    ),
  moveField: (from, to) =>
    set(
      produce((s: FormStateStore) => {
        if (!s.form) return;
        const [item] = s.form.fields.splice(from, 1);
        s.form.fields.splice(to, 0, item);
      }),
    ),
}));
```

Benefits

- Eliminates passing setForm and selectedId down through props.
- Standardizes field operations across components.

---

## 2) Element registry (new file in existing elements directory)

Add a registry so each element type (renderer + settings + defaults) is self‑contained, and the builder looks up by subType instead of a big switch.

New file: src/components/forms/elements/registry.ts

```ts
import type { ReactNode } from "react";
import type { FormField } from "@/types/form.types";

export type ElementKind = FormField["subType"];

export interface ElementDefinition {
  kind: ElementKind;
  label: string;
  icon: ReactNode;
  createDefault: () => FormField;
  Renderer: (p: { field: FormField; index: number }) => JSX.Element;
  SettingsPanel: (p: {
    field: FormField;
    onChange: (f: FormField) => void;
  }) => JSX.Element;
}

const registry = new Map<ElementKind, ElementDefinition>();

export const registerElement = (def: ElementDefinition) =>
  registry.set(def.kind, def);
export const getElement = (kind: ElementKind) => registry.get(kind)!;
export const listElements = () => Array.from(registry.values());
```

Update form-build-view-v2.tsx to use registry (for migrated elements):

```ts
const { Renderer } = getElement(field.subType);
return <Renderer key={field.id} field={field} index={index} />;
```

Benefits

- Adding a field type requires only registering it.
- Shrinks and simplifies the central builder view.

---

## 3) Keep and strengthen the shared container

Continue using src/components/forms/form-field-container.tsx, but:

- Read selectedId from the store via selector.
- Call store actions (select, removeField, moveField) instead of receiving handlers as props.

This reduces prop drilling and centralizes common field behaviors (select, drag handle, duplicate, delete) in one place.

---

## 4) Autosave as a hook (keep in src/hooks)

Create src/hooks/use-autosave.ts to encapsulate save logic using existing queries. It should:

- Subscribe to the form in the store
- Debounce changes
- Call useFormUpdateMutation under the hood
- Expose status: idle | saving | saved | error and lastSaved timestamp

Navbar/layout consume this hook rather than implementing save details.

```ts
// src/hooks/use-autosave.ts (outline)
export function useAutosave({
  orgId,
  formId,
}: {
  orgId: string;
  formId: string;
}) {
  // watch store form
  // debounce serialization and mutation
  // return { status, lastSaved, saveNow }
}
```

Benefits

- Layout and navbar become UI-only shells.
- One place to tune debounce and error handling.

---

## 5) Consolidate file uploads into a hook (keep in src/hooks)

Create src/hooks/use-file-uploads.ts that wraps useFileUploadUrlMutation and useFileDeleteMutation:

- uploadLogo, deleteLogo, uploadHeader, deleteHeader
- Optionally: provide helpers to update the store after successful upload/delete

Components call this hook instead of owning upload logic.

---

## 6) DnD helper utilities (keep in src/utils)

Create src/utils/dnd.ts for typed reorder/move helpers for FormField[] and replace inline move logic in the builder with imports from this utility.

---

## 7) Validation schemas near types (keep in src/types)

Add src/types/form.schemas.ts using zod to validate:

- Form schema
- Per‑subtype field schemas and a discriminated union on subType

Use cases

- Builder: validate new defaults / edits (optional lightweight checks)
- Before save: optional preflight validation to surface errors early
- Runtime: ensure rendering assumptions match saved data

---

## 8) Theme and constants

Extract constants (border styles, default colors, etc.) to src/utils/form-builder-constants.ts and import where needed to eliminate magic strings.

---

## 9) Optional: builder context (keep in src/components/forms)

Add src/components/forms/BuilderContext.tsx if you want a local context abstraction for selection/actions for components that you prefer not to bind to Zustand directly. This is optional; selectors + hooks may suffice.

---

## 10) Refactor path: small, safe steps

Week 1 (Core groundwork)

- Add element registry file and register 1–2 elements (e.g., heading, short_answer)
- Add store actions; use selectors in those 1–2 elements and container to reduce prop drilling
- Add hooks/use-autosave.ts; wire into navbar/layout

Week 2 (Consolidate behaviors)

- Migrate remaining elements to the registry
- Replace central render map with registry usage
- Add hooks/use-file-uploads.ts and remove upload logic from components
- Add utils/dnd.ts; replace inline reorder logic

Week 3 (Validation and polish)

- Add zod schemas in src/types/form.schemas.ts
- Add constants file for theme/options
- Optional: add BuilderContext
- Remove dead code and logs; small unit tests for helpers

---

## 11) Testing guidance

If you have tests (Jest/Vitest):

- utils/dnd.ts: reorder correctness
- hooks/use-autosave.ts: debounce + success/error (mock mutations)
- Registry: register/get/list behavior and that a couple of elements render without crashing
- Snapshot tests for 1–2 element renderers and their SettingsPanel

---

## 12) Acceptance criteria

- Builder compiles; behaviors unchanged for users
- Adding a new field type requires only creating its renderer/settings/defaults and registering it
- Navbar/Layout do not contain save/upload side‑effects directly
- No prop drilling of setForm/selectedId in element trees (use store selectors and actions)
- Central renderElement map is removed or reduced to a thin registry call

---

## 13) Files to add (no folder moves/renames)

- src/components/forms/elements/registry.ts
- src/hooks/use-autosave.ts
- src/hooks/use-file-uploads.ts
- src/utils/dnd.ts
- src/utils/form-builder-constants.ts
- Optional: src/components/forms/BuilderContext.tsx

---

## 14) Risks and mitigations

- Registry adoption drift: migrate elements one by one; support mixed rendering briefly
- Autosave noise: use deep-compare or selective serialization; debounce appropriately
- Type drift between builder and runtime: centralize schemas in src/libs/schemas/form.schemas.ts

---

## 15) Checklist

- [ ] Add registry.ts and register 2 elements
- [ ] Add store actions; remove most prop drilling for those elements
- [ ] Hook up registry usage in form-build-view-v2.tsx for converted elements
- [ ] Add use-autosave; remove save logic from navbar/layout
- [ ] Add use-file-uploads; remove upload logic from components
- [ ] Migrate all elements to registry; delete large central map
- [ ] Add dnd.ts and constants; replace inline logic
- [ ] Add zod schemas; optional pre-save validation
- [ ] Clean up dead code and logs
